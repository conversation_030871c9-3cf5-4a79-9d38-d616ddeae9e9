package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid" // For generating response IDs
)

// --- OpenAI API Request/Response Structs ---

// OpenAIChatMessage represents a message in the OpenAI chat format.
type OpenAIChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAIChatCompletionRequest represents the incoming request body for OpenAI chat.
type OpenAIChatCompletionRequest struct {
	Model       string              `json:"model"`
	Messages    []OpenAIChatMessage `json:"messages"`
	MaxTokens   *int                `json:"max_tokens,omitempty"`
	Temperature *float64            `json:"temperature,omitempty"`
	// Add other OpenAI fields as needed for more robust mocking
}

// OpenAIChatCompletionChoice represents a choice in the OpenAI chat response.
type OpenAIChatCompletionChoice struct {
	Index        int               `json:"index"`
	Message      OpenAIChatMessage `json:"message"`
	LogProbs     interface{}       `json:"logprobs"` // Can be null
	FinishReason string            `json:"finish_reason"`
}

// OpenAIUsage represents token usage in the OpenAI response.
// This struct matches what the real OpenAI API returns.
type OpenAIUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenAIChatCompletionResponse represents the outgoing response body for OpenAI chat.
type OpenAIChatCompletionResponse struct {
	ID      string                       `json:"id"`
	Object  string                       `json:"object"` // e.g., "chat.completion"
	Created int64                        `json:"created"`
	Model   string                       `json:"model"`
	Choices []OpenAIChatCompletionChoice `json:"choices"`
	Usage   OpenAIUsage                  `json:"usage"` // Populated with estimated token counts
}

// --- Common Helper Functions ---

// countWords is a simple helper to simulate token counting by counting words.
// Ensures at least 1 token if content is not empty.
func countWords(s string) int {
	if s == "" {
		return 0
	}
	words := strings.Fields(s)
	if len(words) == 0 && len(s) > 0 { // For very short strings that have no spaces
		return 1
	}
	return len(words)
}

// --- Handler for OpenAI Mock Backend ---

// handleOpenAIRequest is the handler for incoming requests to the mock OpenAI backend.
func handleOpenAIRequest(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = "N/A (No X-Request-ID header)"
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-openai-unknown"
	}

	log.Printf("[%s] [%s] Received request: %s %s from %s", backendID, requestID, r.Method, r.URL.String(), r.RemoteAddr)

	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("[%s] [%s] Error reading request body: %v", backendID, requestID, err)
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var chatRequest OpenAIChatCompletionRequest
	err = json.Unmarshal(bodyBytes, &chatRequest)
	if err != nil {
		log.Printf("[%s] [%s] Error unmarshalling request body: %v. Body: %s", backendID, requestID, err, string(bodyBytes))
		http.Error(w, "Invalid request body format for OpenAI chat", http.StatusBadRequest)
		return
	}

	// Extract prompt from messages
	fullPrompt := ""
	for _, msg := range chatRequest.Messages {
		if msg.Role == "user" {
			fullPrompt += msg.Content + "\n"
		}
	}
	fullPrompt = strings.TrimSpace(fullPrompt)

	// Simulate processing time (latency)
	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 50 // Default delay for OpenAI mock
	}
	time.Sleep(time.Duration(delayMs) * time.Millisecond)
	log.Printf("[%s] [%s] Finished simulated initial delay of %d ms", backendID, requestID, delayMs)

	// Simulate CPU and Memory Usage (as in original mock_backend.go)
	log.Printf("[%s] [%s] Simulating CPU usage for a minimum duration...", backendID, requestID)
	startTimeCPU := time.Now()
	minimumCPUDuration := 2 * time.Second
	i := 0
	for {
		_ = i * i / (i + 1)
		i++
		if time.Since(startTimeCPU) >= minimumCPUDuration {
			break
		}
	}
	log.Printf("[%s] [%s] Finished simulating CPU usage after %s", backendID, requestID, time.Since(startTimeCPU))

	log.Printf("[%s] [%s] Simulating Memory usage...", backendID, requestID)
	memorySizeMB := 100
	tempMemory := make([]byte, memorySizeMB*1024*1024)
	memoryHoldDuration := 2 * time.Second
	time.Sleep(memoryHoldDuration)
	_ = tempMemory
	log.Printf("[%s] [%s] Finished simulating Memory usage after holding %d MB for %s", backendID, requestID, memorySizeMB, memoryHoldDuration)

	// Simulate AI Response and Token Counting
	simulatedResponseContent := fmt.Sprintf("This is a simulated OpenAI response from %s for prompt: '%s'.", backendID, fullPrompt)

	inputTokens := countWords(fullPrompt)
	outputTokens := countWords(simulatedResponseContent)
	if outputTokens == 0 && inputTokens == 0 { // Ensure at least 1 output token if no content, to prevent zero cost
		outputTokens = 1
	}

	response := OpenAIChatCompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", uuid.New().String()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   chatRequest.Model, // Reflect the requested model
		Choices: []OpenAIChatCompletionChoice{
			{
				Index: 0,
				Message: OpenAIChatMessage{
					Role:    "assistant",
					Content: simulatedResponseContent,
				},
				LogProbs:     nil,
				FinishReason: "stop",
			},
		},
		Usage: OpenAIUsage{ // Populated with estimated token counts
			PromptTokens:     inputTokens,
			CompletionTokens: outputTokens,
			TotalTokens:      inputTokens + outputTokens,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)

	log.Printf("[%s] [%s] Sent response. Input Tokens: %d, Output Tokens: %d", backendID, requestID, inputTokens, outputTokens)
}

func main() {
	listenAddr := os.Getenv("LISTEN_ADDR")
	if listenAddr == "" {
		listenAddr = ":5003" // Default port for mock-openai
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-openai"
	}

	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 50 // Default delay for OpenAI mock
	}

	log.Printf("Starting Mock AI Backend '%s' (OpenAI API) on %s with simulated initial delay of %d ms", backendID, listenAddr, delayMs)

	http.HandleFunc("/v1/chat/completions", handleOpenAIRequest) // OpenAI Chat Completion endpoint

	err = http.ListenAndServe(listenAddr, nil)
	if err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
