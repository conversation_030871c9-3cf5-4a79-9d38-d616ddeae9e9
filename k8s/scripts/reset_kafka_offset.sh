#!/bin/bash
# Exit immediately if a command exits with a non-zero status,
# exit if using an uninitialized variable,
# and exit if a pipeline fails.
set -euo pipefail

echo "Script execution started for resetting Data Processor Kafka offset on GKE."

# --- Get Kafka Pod Name ---
echo "Getting Kafka pod name..."
# We need to explicitly specify the container name for kubectl exec if the pod has multiple containers.
# Assuming 'kafka' is the main container for Kafka operations.
KAFKA_POD=$(kubectl get pods -l app=kafka -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -z "$KAFKA_POD" ]; then
    echo "Error: Kafka pod not found. Ensure Kafka is deployed and running in the GKE cluster."
    exit 1
fi
echo "Found Kafka pod: ${KAFKA_POD}"

# --- Wait for Kafka Pod to be Ready ---
echo "Waiting for Kafka pod '${KAFKA_POD}' to be in Running state and ready..."
kubectl wait --for=condition=Ready "pod/${KAFKA_POD}" --timeout=300s || {
    echo "Error: Kafka pod '${KAFKA_POD}' did not become ready within the timeout."
    echo "Please check 'kubectl describe pod ${KAFKA_POD}' and 'kubectl logs ${KAFKA_POD}' for more details."
    exit 1
}
echo "Kafka pod '${KAFKA_POD}' is ready."

# --- Define Consumer Group and Topic ---
CONSUMER_GROUP="data-processor-group" # This is the hardcoded consumer group name in data-processor
KAFKA_TOPIC="inference-logs" # The topic name

echo "--- Scaling Data Processor Deployment down to 0 replicas to clear consumer group members ---"
# Scale down the data-processor deployment to 0 replicas. This forces all pods to terminate,
# ensuring the consumer group becomes empty, which is required for deletion/offset reset.
kubectl scale deployment/data-processor --replicas=0 || {
    echo "Warning: Could not scale down 'data-processor' deployment. It might not exist or there's a permission issue."
    # We will still attempt to proceed with the reset, but this step indicates a potential problem.
}
echo "Data Processor deployment scaled down. Waiting for pods to terminate..."

# Wait for the data-processor deployment to scale down to 0 replicas.
kubectl wait --for=jsonpath='{.status.replicas}'=0 deployment/data-processor --timeout=300s || {
    echo "Warning: Data Processor deployment did not scale down to 0 replicas within the timeout. Proceeding with group deletion anyway (might fail if members still exist)."
    # We proceed even if it times out, as the goal is to make the group empty.
}
echo "Data Processor deployment has scaled down to 0 replicas. Consumer group should now be empty."

echo "--- Describing Kafka Consumer Group: ${CONSUMER_GROUP} (for diagnostics) ---"
# Describe the consumer group. Use the absolute path for kafka-consumer-groups.sh.
# Use '|| true' to prevent script from exiting if group doesn't exist initially.
kubectl exec -it "$KAFKA_POD" -- /usr/bin/kafka-consumer-groups --bootstrap-server kafka:9092 --describe --group "${CONSUMER_GROUP}" || true
echo "--- End of Consumer Group Description ---"

echo "--- Attempting to reset Kafka offset for Consumer Group: ${CONSUMER_GROUP} ---"
# Reset the consumer group offset to the earliest.
# This is a specific --reset-offsets command, not --delete, as previously discussed.
# Use the absolute path for kafka-consumer-groups.sh.
# Adding '--force' to prevent interactive prompts in a non-interactive environment (like Cloud Build).
kubectl exec -it "$KAFKA_POD" -- /usr/bin/kafka-consumer-groups --bootstrap-server kafka:9092 --reset-offsets --to-earliest --group "${CONSUMER_GROUP}" --topic "${KAFKA_TOPIC}" --execute

if [ $? -eq 0 ]; then
    echo "Successfully sent reset-offsets command for consumer group '${CONSUMER_GROUP}' to earliest."
else
    echo "Error: Failed to reset offsets for consumer group '${CONSUMER_GROUP}'. Check logs above for Kafka errors."
    exit 1
fi

echo "--- Scaling Data Processor Deployment back up to 1 replica ---"
# Scale the data-processor deployment back up to 1 replica so new pods can start consuming.
kubectl scale deployment/data-processor --replicas=1
echo "Data Processor deployment scaled up. Waiting for new pods to become available..."

# Wait for the new data-processor pods to become available.
kubectl wait --for=condition=Available deployment/data-processor --timeout=300s || {
    echo "Warning: Data Processor deployment did not become available within the timeout. Please check 'kubectl get pods' and 'kubectl logs' for data-processor."
}
echo "Data Processor deployment is available."

echo "--- Offset Reset Script Finished ---"
echo "You can check the status of your data-processor pods with: kubectl get pods -l app=data-processor"
echo "Once restarted, check the data-processor logs again: kubectl logs -f $(kubectl get pods -l app=data-processor -o jsonpath='{.items[0].metadata.name}')"
echo "And try sending a new request to your proxy-gateway to see if messages are processed."
