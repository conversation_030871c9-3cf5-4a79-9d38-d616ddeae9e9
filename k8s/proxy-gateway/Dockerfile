FROM golang:alpine AS builder
WORKDIR /app
# Explicitly copy go.mod and go.sum into the /app directory
COPY go.mod /app/
COPY go.sum /app/

# Download dependencies with a retry mechanism for network resilience
# This loop attempts to download modules up to 5 times with a 5-second delay between attempts.
RUN for i in $(seq 1 5); do \
    go mod download && break; \
    echo "Attempt $i failed. Retrying in 5 seconds..."; \
    sleep 5; \
done || { echo "Failed to download Go modules after multiple attempts."; exit 1; }

# Install the github.com/google/uuid module
RUN go get github.com/google/uuid

# Explicitly copy the contents of the build context directory into the /app directory
COPY . /app/
# Ensure all dependencies are correctly updated/resolved after copying all code
RUN go mod tidy

# Build the Go application binary statically for Alpine
# -o proxy_gateway specifies the output file name
# Using single quotes for -ldflags to handle nested double quotes
RUN CGO_ENABLED=0 go build -o proxy_gateway -ldflags='-extldflags="-static"' .

FROM alpine:latest
WORKDIR /app
# Set working directory to /app

# Install netcat (no longer strictly needed for the Proxy Gateway container itself,
# but harmless to keep if other scripts might use it)
RUN apk update && apk add --no-cache netcat-openbsd

# Copy config.yaml into the /app directory (if you have one, ensure it's in the build context)
COPY config.yaml /app/config.yaml

# Copy the built binary from the builder stage into the /app directory
COPY --from=builder /app/proxy_gateway /app/proxy_gateway

# Ensure the proxy gateway binary has execute permissions
RUN chmod +x /app/proxy_gateway

# Define the command to run when the container starts
CMD ["/app/proxy_gateway"]

