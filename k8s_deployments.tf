resource "kubernetes_persistent_volume_claim" "kafka-data-pvc" {
  metadata {
    name      = "kafka-data-pvc"
    namespace = "default"
  }
  spec {
    access_modes = ["ReadWriteOnce"]
    resources {
      requests = {
        storage = "10Gi"
      }
    }
  }
}
resource "kubernetes_deployment" "clickhouse" {
  metadata {
    name      = "clickhouse"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "clickhouse"
      }
    }
    template {
      metadata {
        labels = {
          app = "clickhouse"
        }
      }
      spec {
        container {
          name            = "clickhouse"
          image           = "clickhouse/clickhouse-server:latest"
          image_pull_policy = "Always"
          port {
            container_port = 8123
            protocol       = "TCP"
          }
          port {
            container_port = 9000
            protocol       = "TCP"
          }
          env {
            name  = "CLICKHOUSE_DB"
            value = "default"
          }
          env {
            name  = "CLICKHOUSE_USER"
            value = "test"
          }
          env {
            name  = "CLICKHOUSE_PASSWORD"
            value = "test"
          }
          resources {
            limits = {
              cpu    = "1"
              memory = "2Gi"
            }
            requests = {
              cpu    = "500m"
              memory = "1Gi"
            }
          }
          volume_mount {
            mount_path = "/var/lib/clickhouse/"
            name       = "clickhouse-persistent-storage"
          }
          volume_mount {
            mount_path = "/docker-entrypoint-initdb.d/"
            name       = "clickhouse-init-script"
          }
        }
        volume {
          name = "clickhouse-persistent-storage"
          persistent_volume_claim {
            claim_name = "clickhouse-data-pvc"
          }
        }
        volume {
          name = "clickhouse-init-script"
          config_map {
            name = "clickhouse-init-sql"
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "clickhouse" {
  metadata {
    name      = "clickhouse"
    namespace = "default"
  }
  lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "clickhouse"
    }
    port {
      port        = 8123
      target_port = 8123
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_config_map" "kafka-log4j-config" {
  metadata {
    name      = "kafka-log4j-config"
    namespace = "default"
  }
  lifecycle {
    ignore_changes = [data]
  }
  data = {
    "log4j.properties" = "" # You might want to add actual log4j properties here
  }
}

resource "kubernetes_config_map" "kafka-server-properties-config" {
  metadata {
    name      = "kafka-server-properties-config"
    namespace = "default"
  }
  lifecycle {
    ignore_changes = [data]
  }
  data = {
    "server.properties" = <<EOF
broker.id=0
listeners=PLAINTEXT://:9092
advertised.listeners=PLAINTEXT://kafka:9092
zookeeper.connect=zookeeper:2181
default.replication.factor=1
offsets.topic.replication.factor=1
transaction.state.log.replication.factor=1
transaction.state.log.min.isr=1

log4j.rootLogger=DEBUG, stdout
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d %p [%t] %m%n
EOF
  }
}

resource "kubernetes_deployment" "dashboard-api" {
  metadata {
    name      = "dashboard-api"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "dashboard-api"
      }
    }
    template {
      metadata {
        labels = {
          app = "dashboard-api"
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
            "echo \"Waiting for ClickHouse...\"; until nc -z clickhouse 9000; do echo \"ClickHouse not ready, waiting...\"; sleep 2; done; echo \"ClickHouse is ready.\"",
          ]
        }
        container {
          name            = "dashboard-api"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-dashboard-api:latest"
          port {
            container_port = 8080
          }
          env {
            name = "CLICKHOUSE_HOST"
            value = "clickhouse"
          }
          env {
            name  = "CLICKHOUSE_PORT"
            value = "9000"
          }
          env {
            name  = "CLICKHOUSE_DB"
            value = "default"
          }
          env {
            name  = "CLICKHOUSE_USER"
            value = "test"
          }
          env {
            name  = "CLICKHOUSE_PASSWORD"
            value = "test"
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "dashboard-api" {
  metadata {
    name      = "dashboard-api"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "dashboard-api"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "data-processor" {
  metadata {
    name      = "data-processor"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "data-processor"
      }
    }
    template {
      metadata {
        labels = {
          app = "data-processor"
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse-and-redis"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
          "echo \"Waiting for ClickHouse and Redis...\"; until nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"ClickHouse and Redis are ready.\"",
          ]
          resources {
            limits = {
              cpu    = "20m"
              memory = "20Mi"
            }
            requests = {
              cpu    = "10m"
              memory = "10Mi"
            }
          }
        }
        init_container {
          name  = "wait-for-redis-populator"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
            "echo \"Waiting for redis-populator-job...\"; until kubectl get job redis-populator-job -o jsonpath='{.status.conditions[?(@.type==\\\"Complete\\\")].status}' | grep -q \"True\"; do echo \"redis-populator-job not ready, waiting...\"; sleep 2; done; echo \"redis-populator-job is ready.\"",
          ]
          resources {
            limits = {
              cpu    = "20m"
              memory = "20Mi"
            }
            requests = {
              cpu    = "10m"
              memory = "10Mi"
            }
          }
        }
        container {
          name = "data-processor"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-data-processor:latest"
          port {
            container_port = 8080
          }
          env {
            name  = "CLICKHOUSE_HOST"
            value = "clickhouse"
          }
          env {
            name  = "CLICKHOUSE_PORT"
            value = "9000"
          }
          env {
            name  = "CLICKHOUSE_DB"
            value = "default"
          }
          env {
            name  = "CLICKHOUSE_USER"
            value = "test"
          }
          env {
            name  = "CLICKHOUSE_PASSWORD"
            value = "test"
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "data-processor" {
  metadata {
    name      = "data-processor"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "data-processor"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "evaluation-service" {
  metadata {
    name      = "evaluation-service"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "evaluation-service"
      }
    }
    template {
      metadata {
        labels = {
          app = "evaluation-service"
        }
      }
      spec {
        container {
          name  = "evaluation-service"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-evaluation-service:latest"
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "evaluation-service" {
  metadata {
    name      = "evaluation-service"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "evaluation-service"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "frontend" {
  metadata {
    name      = "frontend"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "frontend"
      }
    }
    template {
      metadata {
        labels = {
          app = "frontend"
        }
      }
      spec {
        container {
          name  = "frontend"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-frontend:latest"
          port {
            container_port = 80
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "frontend" {
  metadata {
    name      = "frontend"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "frontend"
    }
    port {
      port        = 80
      target_port = 80
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "zookeeper" {
  metadata {
    name      = "zookeeper"
    namespace = "default"
  }
  spec {
    selector {
      match_labels = {
        app = "zookeeper"
      }
    }
    template {
      metadata {
        labels = {
          app = "zookeeper"
        }
      }
      spec {
        container {
          name  = "zookeeper"
          image = "confluentinc/cp-zookeeper:7.5.0"
          port {
            container_port = 2181
          }
          env {
            name  = "ZOOKEEPER_CLIENT_PORT"
            value = "2181"
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "zookeeper" {
  metadata {
    name      = "zookeeper"
    namespace = "default"
  }
  spec {
    selector = {
      app = "zookeeper"
    }
    port {
      port        = 2181
      target_port = 2181
    }
    type = "LoadBalancer"
  }
}
resource "kubernetes_deployment" "kafka" {
  metadata {
    name      = "kafka"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "kafka"
      }
    }
    replicas = var.kafka_replicas # Use a variable for the number of replicas
    strategy {
      type = "RollingUpdate"
      rolling_update {
        max_surge       = "25%"
        max_unavailable = "25%"
      }
    }
    template {
      metadata {
        labels = {
          app = "kafka"
        }
      }
      spec {
        init_container {
          name  = "wait-for-dependencies"
          image = "busybox:1.36"
          command = [
           "sh",
           "-c",
           <<EOF
Waiting for ClickHouse, Redis, and Zookeeper...
Checking DNS resolution for zookeeper service...
nslookup zookeeper || echo "DNS resolution failed"
until nc -z -v -w 2 clickhouse 9000 && nc -z -v -w 2 redis 6379 && nc -z -v -w 2 zookeeper 2181; do
 echo "ClickHouse, Redis, or Zookeeper not ready, waiting..."
 echo "ClickHouse: $(nc -z -v -w 2 clickhouse 9000; echo $?)"
 echo "Redis: $(nc -z -v -w 2 redis 6379; echo $?)"
 echo "Zookeeper: $(nc -z -v -w 2 zookeeper 2181; echo $?)"
 sleep 5
done
echo "ClickHouse, Redis, and Zookeeper are ready."
until nc -z -v -w 2 zookeeper:2181; do
 echo "Zookeeper not ready, waiting..."
 sleep 5
done
echo "ClickHouse, Redis, and Zookeeper are ready."
EOF
       ]
       resources {
            limits = {
              cpu    = "20m"
              memory = "20Mi"
            }
            requests = {
              cpu    = "10m"
              memory = "10Mi"
            }
          }
        }
        container {
          name  = "kafka"
          image = "confluentinc/cp-kafka:7.5.0" # Hardcoding the image
          resources {
            limits = {
              cpu    = "2"
              memory = "4Gi"
            }
            requests = {
              cpu    = "1"
              memory = "2Gi"
            }
          }
          env {
            name  = "KAFKA_BROKER_ID"
            value = "0"
          }
          env {
            name  = "KAFKA_ZOOKEEPER_CONNECT"
            value = "zookeeper:2181"
          }
          env {
            name  = "KAFKA_ADVERTISED_LISTENERS"
            value = "PLAINTEXT://kafka:9092"
          }
          readiness_probe {
            tcp_socket {
              port = 9092
            }
            initial_delay_seconds = 30
            period_seconds        = 10
            timeout_seconds       = 5
            success_threshold     = 1
            failure_threshold     = 3
          }
          volume_mount {
            mount_path = "/var/lib/kafka/data"
            name       = "kafka-data"
          }
          volume_mount {
            name       = "kafka-server-properties-volume"
            mount_path = "/mnt/kafka/server.properties"
            sub_path    = "server.properties"
            read_only  = true
          }
          volume_mount {
            name       = "kafka-log4j-config-volume"
            mount_path = "/mnt/kafka/log4j.properties"
            sub_path    = "log4j.properties"
            read_only  = true
          }
        }
        volume {
          name = "kafka-data"
          persistent_volume_claim {
            claim_name = "kafka-data-pvc"
          }
        }
        volume {
          name = "kafka-server-properties-volume"
          config_map {
            name = "kafka-server-properties-config"
            optional = false
          }
        }
        volume {
          name = "kafka-log4j-config-volume"
          config_map {
            name = "kafka-log4j-config"
            optional = false
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "kafka" {
  metadata {
    name      = "kafka"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "kafka"
    }
    port {
      port        = 9092
      target_port = 9092
      name        = "kafka"
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "mock-backend-gpu1" {
  metadata {
    name      = "mock-backend-gpu1"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "mock-backend-gpu1"
      }
    }
    template {
      metadata {
        labels = {
          app = "mock-backend-gpu1"
        }
      }
      spec {
        container {
          name            = "mock-backend-gpu1"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-backend-gpu1:latest"
          command = ["/usr/local/bin/entrypoint.sh"]
          args = ["mock_backend_gpu1"]
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "mock-backend-gpu2" {
  metadata {
    name      = "mock-backend-gpu2"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "mock-backend-gpu2"
      }
    }
    template {
      metadata {
        labels = {
          app = "mock-backend-gpu2"
        }
      }
      spec {
        container {
          name  = "mock-backend-gpu2"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-backend-gpu2:latest"
          command = ["/usr/local/bin/entrypoint.sh"]
          args = ["mock_backend_gpu2"]
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "mock-openai" {
  metadata {
    name      = "mock-openai"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "mock-openai"
      }
    }
    template {
      metadata {
        labels = {
          app = "mock-openai"
        }
      }
      spec {
        container {
          name  = "mock-openai"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-openai:latest"
          command = ["/usr/local/bin/entrypoint.sh"]
          args = ["mock_openai"]  
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "mock-google" {
  metadata {
    name      = "mock-google"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "mock-google"
      }
    }
    template {
      metadata {
        labels = {
          app = "mock-google"
        }
      }
      spec {
        container {
          name  = "mock-google"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-google:latest"
          command = ["/usr/local/bin/entrypoint.sh"]
          args = ["mock_google"]
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "mock-anthropic" {
  metadata {
    name      = "mock-anthropic"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "mock-anthropic"
      }
    }
    template {
      metadata {
        labels = {
          app = "mock-anthropic"
        }
      }
      spec {
        container {
          name  = "mock-anthropic"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-anthropic:latest"
          command = ["/usr/local/bin/entrypoint.sh"]
          args = ["mock_anthropic"]
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "policy-manager" {
  metadata {
    name      = "policy-manager"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "policy-manager"
      }
    }
    template {
      metadata {
        labels = {
          app = "policy-manager"
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse-and-redis"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
          "echo \"Waiting for ClickHouse and Redis...\"; until nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"ClickHouse and Redis are ready.\"",
         ]
          resources {
            limits = {
              cpu    = "20m"
              memory = "20Mi"
            }
            requests = {
              cpu    = "10m"
              memory = "10Mi"
            }
          }
        }
        container {
          name  = "policy-manager"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-policy-manager:latest"
          liveness_probe {
            http_get {
              path = "/health"
              port = 8083
            }
            initial_delay_seconds = 3
            period_seconds        = 3
          }
          port {
            container_port = 8083
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "policy-manager" {
 metadata {
   name      = "policy-manager"
   namespace = "default"
 }
  lifecycle {
    ignore_changes = [spec]
  }
 spec {
   selector = {
     app = "policy-manager"
   }
   port {
     port        = 8083
     target_port = 8083
   }
   type = "LoadBalancer"
 }
}


resource "kubernetes_deployment" "prometheus" {
  metadata {
    name      = "prometheus"
    namespace = "default"
    }
   lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "prometheus"
      }
    }
    template {
      metadata {
        labels = {
          app = "prometheus"
        }
      }
      spec {
        container {
          name  = "prometheus"
          image = "prom/prometheus:latest"
          port {
            container_port = 9090
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "prometheus" {
  metadata {
    name      = "prometheus"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "prometheus"
    }
    port {
      port        = 9090
      target_port = 9090
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "proxy-gateway" {
  metadata {
    name      = "proxy-gateway"
    namespace = "default"
  }
  spec {
    selector {
      match_labels = {
        app = "proxy-gateway"
      }
    }
    template {
      metadata {
        labels = {
          app = "proxy-gateway"
        }
      }
      spec {
        container {
          name  = "proxy-gateway"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-proxy-gateway:latest"
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "proxy-gateway" {
  metadata {
    name      = "proxy-gateway"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "proxy-gateway"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "redis" {
  metadata {
    name      = "redis"
    namespace = "default"
  }
  spec {
    selector {
      match_labels = {
        app = "redis"
      }
    }
    template {
      metadata {
        labels = {
          app = "redis"
        }
      }
      spec {
        container {
          name  = "redis"
          image = "redis:latest"
          port {
            container_port = 6379
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "redis" {
  metadata {
    name      = "redis"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "redis"
    }
    port {
      port        = 6379
      target_port = 6379
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "governance-service" {
  metadata {
    name      = "governance-service"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "governance-service"
      }
    }
    template {
      metadata {
        labels = {
          app = "governance-service"
        }
      }
      spec {
        container {
          name  = "governance-service"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-governance-service:latest"
          liveness_probe {
            http_get {
              path = "/healthz"
              port = 8080
            }
            initial_delay_seconds = 3
            period_seconds        = 3
          }
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "governance-service" {
  metadata {
    name      = "governance-service"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "governance-service"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_deployment" "integration-service" {
  metadata {
    name      = "integration-service"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    selector {
      match_labels = {
        app = "integration-service"
      }
    }
    template {
      metadata {
        labels = {
          app = "integration-service"
        }
      }
      spec {
        container {
          name  = "integration-service"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-integration-service:latest"
          port {
            container_port = 8080
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "integration-service" {
  metadata {
    name      = "integration-service"
    namespace = "default"
  }
   lifecycle {
    ignore_changes = [spec]
  }
  spec {
    selector = {
      app = "integration-service"
    }
    port {
      port        = 8080
      target_port = 8080
    }
    type = "LoadBalancer"
  }
}

resource "kubernetes_job" "kafka_topic_creator" {
  metadata {
    name      = "kafka-topic-creator"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    template {
      metadata {
        labels = {
          app = "kafka-topic-creator"
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse-redis-kafka"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
            "echo \"Waiting for ClickHouse, Redis, and Kafka...\"; until nc -z clickhouse 9000 && nc -z redis 6379 && nc -z kafka 9092; do echo \"ClickHouse, Redis, or Kafka not ready, waiting...\"; sleep 2; done; echo \"ClickHouse, Redis, and Kafka are ready.\"",
           ]
        }
        restart_policy = "OnFailure"
        container {
          name  = "topic-creator"
          image = "ai-cost-performance-optimizer-kafka-topic-creator:latest"
          image_pull_policy = "IfNotPresent"
          env {
            name  = "KAFKA_BROKERS"
            value = "kafka:9092"
          }
          env {
            name  = "TOPIC_NAME"
            value = "inference-logs"
          }
          env {
            name  = "KAFKA_ZOOKEEPER_CONNECT"
            value = "zookeeper:2181"
          }
          env {
            name  = "KAFKA_ADVERTISED_LISTENERS"
            value = "PLAINTEXT://kafka:9092"
          }
          command = ["/app/create_topic_and_wait.sh"]
        }
      }
    }
    backoff_limit = 3
  }
}

resource "kubernetes_job" "redis_populator_job" {
  metadata {
    name      = "redis-populator-job"
    namespace = "default"
    }
  lifecycle {
    ignore_changes = [spec]
    }
  spec {
    template {
      metadata {
        labels = {
          app = "redis-populator"
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse-redis-data-processor"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
            "echo \"Waiting for ClickHouse, Redis, and Data Processor...\"; until nc -z clickhouse 9000 && nc -z redis 6379 && nc -z data-processor 8080; do echo \"ClickHouse, Redis, or Data Processor not ready, waiting...\"; sleep 2; done; echo \"ClickHouse, Redis, and Data Processor are ready.\"",
           ]
        }
        restart_policy = "OnFailure"
        container {
          name  = "redis-populator"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-redis-populator-job:latest"
          command = ["python3", "/app/populate_redis.py"]
          env {
            name  = "REDIS_HOST"
            value = "redis"
          }
          env {
            name  = "REDIS_PORT"
            value = "6379"
          }
          env {
            name  = "REDIS_DB"
            value = "0"
          }
          env {
            name  = "REDIS_PASSWORD"
            value = ""
          }
          env {
            name = "OPENAI_API_KEY"
            value_from {
              secret_key_ref {
                name     = "openai-api-key"
                key      = "OPENAI_API_KEY"
                optional = true
              }
            }
          }
          env {
            name = "GOOGLE_API_KEY"
            value_from {
              secret_key_ref {
                name     = "google-api-key"
                key      = "GOOGLE_API_KEY"
                optional = true
              }
            }
          }
          env {
            name = "ANTHROPIC_API_KEY"
            value_from {
              secret_key_ref {
                name     = "anthropic-api-key"
                key      = "ANTHROPIC_API_KEY"
                optional = true
              }
            }
          }
          env {
            name = "LLAMA3_API_KEY"
            value_from {
              secret_key_ref {
                name     = "llama3-api-key"
                key      = "LLAMA3_API_KEY"
                optional = true
              }
            }
          }
        }
        # The above secrets are expected to be created by the k8s/New_Autostart.sh script
      }
    }
    backoff_limit = 3
  }
}
