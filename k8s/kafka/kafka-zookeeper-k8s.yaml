# kafka-zookeeper-k8s.yaml
# Kubernetes YAML definitions for Zookeeper and Kafka

apiVersion: v1
kind: Service
metadata:
  name: zookeeper # Service name for in-cluster communication
spec:
  selector:
    app: zookeeper
  ports:
  - name: client
    protocol: TCP
    port: 2181
    targetPort: 2181
  type: ClusterIP # Use ClusterIP for in-cluster access

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper # Deployment name
  labels:
    app: zookeeper
spec:
  replicas: 1 # Single replica for simplicity
  selector:
    matchLabels:
      app: zookeeper
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.5.0 # Match Kafka version
        imagePullPolicy: IfNotPresent # Set image pull policy to IfNotPresent
        ports:
        - containerPort: 2181
          name: client
        env:
        - name: ZOOKEEPER_CLIENT_PORT
          value: "2181"
        - name: ZOOKEEPER_TICK_TIME
          value: "2000"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        readinessProbe:
          tcpSocket:
            port: 2181
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 3
        # Optional: Add volume mount for persistent data if needed
        # volumeMounts:
        # - name: zookeeper-storage
        #   mountPath: /var/lib/zookeeper
      # Optional: Define persistent volume for data
      # volumes:
      # - name: zookeeper-storage
      #   persistentVolumeClaim:
      #     claimName: zookeeper-data-pvc # Assuming you create a PVC

---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: zookeeper-data-pvc
# spec:
#   accessModes:
#     - ReadWriteOnce
#   resources:
#     requests:
#       storage: 1Gi # Adjust as needed

---
apiVersion: v1
kind: Service
metadata:
  name: kafka # Service name for in-cluster communication
spec:
  selector:
    app: kafka
  ports:
  - name: plaintext # Port for internal Kafka communication
    protocol: TCP
    port: 9092
    targetPort: 9092
  # You might need an additional service/port for external access if not using minikube tunnel
  # - name: external
  #   protocol: TCP
  #   port: 29092 # Port exposed externally
  #   targetPort: 29092 # Internal port
  type: ClusterIP # Use ClusterIP for in-cluster access

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka # Deployment name
  labels:
    app: kafka
spec:
  replicas: 1 # Single replica for simplicity
  selector:
    matchLabels:
      app: kafka
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: kafka
    spec:
      # Add an initContainer to wait for Zookeeper to be ready
      initContainers:
      - name: wait-for-zookeeper
        image: busybox:1.36 # A small image with networking tools
        imagePullPolicy: IfNotPresent # Set image pull policy to IfNotPresent
        # Use nc -z to check if the Zookeeper service is reachable on port 2181
        command: ['sh', '-c', 'echo "Waiting for Zookeeper..."; 
                  echo "Checking DNS resolution for zookeeper service...";
                  nslookup zookeeper || echo "DNS resolution failed";
                  echo "Attempting to connect to zookeeper:2181...";
                  until nc -z -v -w 2 zookeeper 2181; do 
                    echo "Zookeeper not ready, waiting..."; 
                    sleep 5; 
                  done; 
                  echo "Zookeeper is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.5.0  # Downgrade to a pre-8.0 version
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9092
          name: plaintext
        env:
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper:2181"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka:9092"
        command: ["sh", "-c"]
        args:
        - |
          export KAFKA_LOG4J_OPTS="-Dlog4j.configuration=file:/mnt/kafka/log4j.properties" && \
          exec kafka-server-start /mnt/kafka/server.properties
        volumeMounts:
        # Mount the server.properties ConfigMap to /mnt/kafka/server.properties
        - name: kafka-server-properties-volume
          mountPath: /mnt/kafka/server.properties
          subPath: server.properties
          readOnly: true
        # Mount the log4j.properties ConfigMap to /mnt/kafka/log4j.properties
        - name: kafka-log4j-config-volume
          mountPath: /mnt/kafka/log4j.properties
          subPath: log4j.properties
          readOnly: true
        # Optional: Add volume mount for persistent data if needed
        # - name: kafka-storage
        #   mountPath: /var/lib/kafka
        resources:
          requests:
            memory: "512Mi"
            cpu: "300m"
          limits:
            memory: "1Gi"
            cpu: "700m"
        # Add a readiness probe to check if Kafka is ready
        readinessProbe: # <-- Re-added and configured this
          tcpSocket:
            port: 9092
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      volumes:
      # Define the volume for server.properties ConfigMap
      - name: kafka-server-properties-volume
        configMap:
          name: kafka-server-properties # Name of the ConfigMap for server.properties
          optional: false
      # Define the volume for log4j.properties ConfigMap
      - name: kafka-log4j-config-volume
        configMap:
          name: kafka-log4j-config # Name of the ConfigMap for log4j.properties
          optional: false
      # Optional: Define persistent volume for data
      # - name: kafka-storage
      #   persistentVolumeClaim:
      #     claimName: kafka-data-pvc # Assuming you create a PVC
