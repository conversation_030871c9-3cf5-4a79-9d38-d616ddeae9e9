# cluster-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod # Use 'letsencrypt-staging' for testing to avoid hitting rate limits
spec:
  acme:
    # You must replace this email address with your own.
    # Let's Encrypt will use this to contact you about expiring certificates.
    email: phanid<PERSON><PERSON>@gmail.com
    server: https://acme-v02.api.letsencrypt.org/directory # Production ACME server
    # For testing, use: https://acme-staging-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      # Secret resource that will be used to store the ACME account's private key.
      name: letsencrypt-prod-account-key
    solvers:
    - http01:
        ingress:
          class: nginx # Specify your Ingress Controller class
