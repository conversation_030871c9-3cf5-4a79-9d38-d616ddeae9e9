# cloudbuild.yaml
# Google Cloud Build configuration for GitOps deployment to GKE

# This build will store logs in a regional, user-owned bucket.
# This option is required when a custom service account is used for the build,
# which is implicitly the case for Cloud Build's default service account in some projects.
options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET

steps:
# Step 1: Build and Push Docker Images to Artifact Registry

# --- Build and Push Steps for All Mocks (using the single Dockerfile.mockbackend) ---

# Build mock-backend-gpu1 image (uses mock_backend.go)
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-backend-gpu1'
  args: [
    'build',
    '-f', 'k8s/mock_backend/Dockerfile.mockbackend', # Use the consolidated Dockerfile
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest', # Unique image name
    '--build-arg', 'MOCK_NAME=mock_backend_gpu1', # Binary name
    '--build-arg', 'MOCK_GO_FILENAME=mock_backend.go', # Source file name
    'k8s/mock_backend' # Build context
  ]
  dir: '.' # Execute from project root
# Push mock-backend-gpu1 image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-backend-gpu1'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest']
  waitFor: ['Build mock-backend-gpu1']

# Build mock-backend-gpu2 image (uses mock_backend.go, but with a different ID/port config in K8s YAML)
# NOTE: If mock-backend-gpu2 should use the *same* mock_backend.go code as gpu1,
# you might not need a separate image if you configure the K8s Deployment correctly
# with different environment variables. However, if they are meant to be distinct
# binaries (e.g., different hardcoded logic), then keep this separate build.
# For now, assuming it uses the same mock_backend.go for simplicity as no specific
# mock_backend_gpu2.go was provided. If you have a separate mock_backend_gpu2.go,
# update MOCK_GO_FILENAME accordingly.
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-backend-gpu2'
  args: [
    'build',
    '-f', 'k8s/mock_backend/Dockerfile.mockbackend',
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest',
    '--build-arg', 'MOCK_NAME=mock_backend_gpu2',
    '--build-arg', 'MOCK_GO_FILENAME=mock_backend.go', # Assuming mock_backend.go for gpu2 as well
    'k8s/mock_backend'
  ]
  dir: '.'
# Push mock-backend-gpu2 image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-backend-gpu2'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest']
  waitFor: ['Build mock-backend-gpu2']

# Build mock-openai image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-openai'
  args: [
    'build',
    '-f', 'k8s/mock_backend/Dockerfile.mockbackend',
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest',
    '--build-arg', 'MOCK_NAME=mock_openai',
    '--build-arg', 'MOCK_GO_FILENAME=mock_openai.go', # Correct source file
    'k8s/mock_backend'
  ]
  dir: '.'
# Push mock-openai image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-openai'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest']
  waitFor: ['Build mock-openai']

# Build mock-google image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-google'
  args: [
    'build',
    '-f', 'k8s/mock_backend/Dockerfile.mockbackend',
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest',
    '--build-arg', 'MOCK_NAME=mock_google',
    '--build-arg', 'MOCK_GO_FILENAME=mock_google.go', # Correct source file
    'k8s/mock_backend'
  ]
  dir: '.'
# Push mock-google image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-google'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest']
  waitFor: ['Build mock-google']

# Build mock-anthropic image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-anthropic'
  args: [
    'build',
    '-f', 'k8s/mock_backend/Dockerfile.mockbackend',
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest',
    '--build-arg', 'MOCK_NAME=mock_anthropic',
    '--build-arg', 'MOCK_GO_FILENAME=mock_anthropic.go', # Correct source file
    'k8s/mock_backend'
  ]
  dir: '.'
# Push mock-anthropic image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-anthropic'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest']
  waitFor: ['Build mock-anthropic']


# --- Other Service Builds ---

# Build proxy-gateway image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build proxy-gateway'
  args: ['build', '-f', 'k8s/proxy-gateway/Dockerfile', '--no-cache', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest', 'k8s/proxy-gateway']
  dir: '.'
# Push proxy-gateway image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push proxy-gateway'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest']
  waitFor: ['Build proxy-gateway']

# Build data-processor image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build data-processor'
  args: ['build', '-f', 'k8s/data-processor/Dockerfile', '--no-cache', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest', 'k8s/data-processor']
  dir: '.'
# Push data-processor image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push data-processor'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest']
  waitFor: ['Build data-processor']

# Build kafka_topic_creator image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build kafka_topic_creator'
  env: ['DOCKER_BUILDKIT=1']
  args: ['build', '-f', 'k8s/kafka_topic_creator/Dockerfile', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest', 'k8s/kafka_topic_creator']
  dir: '.'
# Push kafka_topic_creator image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push kafka_topic_creator'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest']
  waitFor: ['Build kafka_topic_creator']

# Build dashboard-api image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build dashboard-api'
  args: ['build', '-f', 'k8s/dashboard-api/Dockerfile', '--no-cache', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest', 'k8s/dashboard-api']
  dir: '.'
# Push dashboard-api image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push dashboard-api'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest']
  waitFor: ['Build dashboard-api']

# Build frontend image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build frontend'
  args: [
    'build',
    '-f', 'Dockerfile', # Dockerfile is relative to the 'dir'
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest',
    '--build-arg', 'VITE_APP_API_URL=http://proxy-gateway:8080',
    '.' # Build context is the 'dir' itself
  ]
  dir: 'k8s/frontend'
  waitFor: ['Push dashboard-api'] # Ensure frontend build waits for dashboard-api

# Push frontend image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push frontend'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest']
  waitFor: ['Build frontend']

# Build policy-manager image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build policy-manager'
  args: ['build', '-f', 'k8s/policy-manager/Dockerfile', '--no-cache', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest', 'k8s/policy-manager']
  dir: '.'
# Push policy-manager image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push policy-manager'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest']
  waitFor: ['Build policy-manager']

# Build ai-optimizer image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build ai-optimizer'
  args: ['build', '-f', 'k8s/ai-optimizer/Dockerfile', '--no-cache', '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest', 'k8s/ai-optimizer']
  dir: '.'
# Push ai-optimizer image
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push ai-optimizer'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest']
  waitFor: ['Build ai-optimizer']

# --- Kubernetes Interaction Pre-Deployment Steps ---

# NEW STEP: Get GKE Credentials (This MUST be before any kubectl commands interacting with your cluster)
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Get GKE Credentials'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      gcloud container clusters get-credentials "${_GKE_CLUSTER_NAME}" --region "${_GCP_REGION}" --project "${_GCP_PROJECT_ID}"
  # Wait for all application images to be pushed, as their deployment might be needed.
  waitFor:
    - 'Push mock-backend-gpu1'
    - 'Push mock-backend-gpu2'
    - 'Push mock-openai'
    - 'Push mock-google'
    - 'Push mock-anthropic'
    - 'Push proxy-gateway'
    - 'Push data-processor'
    - 'Push kafka_topic_creator'
    - 'Push dashboard-api'
    - 'Push frontend'
    - 'Push policy-manager'
    - 'Push ai-optimizer'

# NEW DIAGNOSTIC STEP: List contents of k8s/scripts/ directory (REINSTATED)
# This helps confirm the scripts are correctly present in the build environment.
- name: 'bash'
  id: 'Debug: List k8s/scripts/ directory'
  args: ['ls', '-lR', 'k8s/scripts/'] # -lR for recursive detailed listing
  waitFor: ['Get GKE Credentials'] # Run after getting credentials, before using scripts

# --- Main Deployment Step ---

# Step 2: Deploy to GKE using New_Autostart.sh
# This single step now orchestrates ALL Kubernetes manifest applications and waits internally.
- name: 'gcr.io/cloud-builders/kubectl' # Use kubectl builder for general K8s interaction
  id: 'Deploy to GKE'
  env:
    - 'GCP_PROJECT_ID=${_GCP_PROJECT_ID}'
    - 'GCP_REGION=${_GCP_REGION}'
    - 'GKE_CLUSTER_NAME=${_GKE_CLUSTER_NAME}'
    - 'ARTIFACT_REGISTRY_REPO=${_ARTIFACT_REGISTRY_REPO}'
    # Pass the full image paths as environment variables to the script
    - 'MOCK_BACKEND_GPU1_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest'
    - 'MOCK_BACKEND_GPU2_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest'
    - 'PROXY_GATEWAY_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest'
    - 'FRONTEND_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest'
    - 'KAFKA_TOPIC_CREATOR_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest'
    - 'DATA_PROCESSOR_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest'
    - 'DASHBOARD_API_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest'
    - 'POLICY_MANAGER_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest'
    - 'AI_OPTIMIZER_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest'
    - 'MOCK_OPENAI_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest'
    - 'MOCK_GOOGLE_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest'
    - 'MOCK_ANTHROPIC_IMAGE=${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest'
  entrypoint: 'bash'
  args: ['k8s/New_Autostart.sh'] # Execute your new, orchestrated deployment script
  dir: '.' # Execute from the project root
  # This step now depends on the credentials being fetched, and the scripts directory being listed.
  waitFor: ['Debug: List k8s/scripts/ directory']

# --- Post-Deployment Configuration/Setup Steps (for deployed applications) ---

# NEW STEP: Build a temporary image for the Redis population job
# This image will contain Python, Redis client, and your populate_redis_with_debug.sh script
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build Redis Populator Job Image'
  args: [
    'build',
    '-f', '-', # Read Dockerfile from stdin
    '--no-cache',
    '-t', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/redis-populator-job:latest',
    '.' # Build context is the current directory (workspace)
  ]
  dir: '.' # Ensure context is the root so k8s/scripts is available
  # Pass Dockerfile content directly
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      docker build -f - --no-cache -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/redis-populator-job:latest" . <<EOF
      FROM python:3.9-slim-buster
      WORKDIR /app
      COPY k8s/scripts/populate_redis_with_debug.sh /app/populate_redis_with_debug.sh
      COPY k8s/scripts/populate_redis.py /app/populate_redis.py
      RUN chmod +x /app/populate_redis_with_debug.sh
      EOF
  waitFor: ['Deploy to GKE'] # Wait for GKE deployments to start (especially Redis)

- name: 'gcr.io/cloud-builders/docker'
  id: 'Push Redis Populator Job Image'
  args: ['push', '${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/redis-populator-job:latest']
  waitFor: ['Build Redis Populator Job Image']


# NEW STEP: Run the Redis Populator as a Kubernetes Job
- name: 'gcr.io/cloud-builders/kubectl'
  id: 'Run Redis Populator Job'
  entrypoint: 'bash'
  args:
    - 'k8s/scripts/create_redis_populator_job.sh' # Execute the new script directly
    - '${_GCP_PROJECT_ID}' # Pass arguments to the script
    - '${_GCP_REGION}'
    - '${_ARTIFACT_REGISTRY_REPO}'
    - '${_BUILD_ID}'
    - '${_GKE_CLUSTER_NAME}'
  waitFor: ['Push Redis Populator Job Image'] # Wait for the image to be pushed

# Define substitute variables for project ID, region, cluster name, and repo name.
# These will be replaced at build time.
substitutions:
  _GCP_PROJECT_ID: 'silken-zenith-460615-s7' # Your GCP Project ID
  _GCP_REGION: 'us-central1'              # Your GKE Cluster Region
  _GKE_CLUSTER_NAME: 'ai-optimizer-cluster' # Your GKE Cluster Name
  _ARTIFACT_REGISTRY_REPO: 'ai-optimizer-repo' # Your Artifact Registry Repository Name
  _BUILD_ID: '${BUILD_ID}' # Cloud Build's built-in BUILD_ID

# Service accounts used by Cloud Build
# The default Cloud Build service account needs specific IAM roles.
# Ensure the Cloud Build service account (<EMAIL>) has:
# - Cloud Build Service Account (default)
# - Artifact Registry Writer (to push images)
# - Kubernetes Engine Developer (to deploy to GKE)
# - Service Account User (to impersonate Workload Identity GSAs)
# - Service Account Admin (to create/manage Workload Identity GSAs/KSAs if the script does it)
# You might need to grant these roles manually via IAM in the GCP Console.
