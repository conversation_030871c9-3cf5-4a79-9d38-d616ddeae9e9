# dashboard-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-dashboard-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - scale-llm.com # <--- FIX: Changed to scale-llm.com
    secretName: dashboard-tls-secret
  rules:
  - host: scale-llm.com # <--- FIX: Changed to scale-llm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-dashboard
            port:
              number: 80 # Ensure this matches your frontend-dashboard Kubernetes Service's port

