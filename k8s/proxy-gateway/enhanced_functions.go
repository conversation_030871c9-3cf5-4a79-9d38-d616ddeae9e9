package main

// Constants for prompt compression
const (
	SEMANTIC_COMPRESSION_ENABLED    = true // Enable semantic-aware compression
	INSTRUCTION_PRESERVATION_FACTOR = 2.0  // Weight multiplier for instructions (higher = preserve more)
	CONTEXT_COMPRESSION_RATIO       = 0.6  // Target compression ratio for context sections
	MAX_COMPRESSION_ITERATIONS      = 3    // Maximum number of compression passes
	KEY_CONCEPT_THRESHOLD           = 0.75 // Threshold for key concept identification
	COMPRESSION_MIN_CHUNK_SIZE      = 150  // Minimum characters for compression chunks
	COMPRESSION_AGGRESSION          = 0.85 // Higher values = more aggressive compression
)

// Constants for batching and queue management
const (
	BATCH_PROCESSING_ENABLED   = true // Master switch for batch processing
	MAX_BATCH_SIZE             = 10   // Maximum number of requests in a batch
	BATCH_COLLECTION_WINDOW_MS = 100  // Window for collecting batches (milliseconds)
	PRIORITY_QUEUE_ENABLED     = true // Enable priority-based queue processing
	HIGH_PRIORITY_THRESHOLD    = 8    // Priority level 8-10 is considered high priority
	MEDIUM_PRIORITY_THRESHOLD  = 5    // Priority level 5-7 is considered medium priority
	BATCH_PROCESSOR_COUNT      = 3    // Number of batch processors to run
)

// NOTE: The semantic compression functions have been moved to prompt_optimization.go
// to avoid duplicate declarations
