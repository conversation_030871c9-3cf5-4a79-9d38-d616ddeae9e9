# prometheus.yml
# Global configuration
global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. Default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # alertmanager:9093

# Load rules once and periodically evaluate them in the background.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A list of scrape configurations.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.

  # Scrape Prometheus itself
  - job_name: 'prometheus'
    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.
    static_configs:
      - targets: ['localhost:9090'] # Scrape the Prometheus container itself

  # Scrape Kubelet for container metrics (equivalent to cAdvisor in Kubernetes)
  # This job scrapes the /metrics/cadvisor endpoint on each node's Kubelet.
  - job_name: 'kubernetes-cadvisor'
    # Use the kubernetes_sd_configs role 'node' to discover nodes
    kubernetes_sd_configs:
      - role: node
    scheme: https # Kubelet typically exposes metrics via HTTPS
    tls_config:
      insecure_skip_verify: true # WARNING: Insecure, skip certificate verification for simplicity in Minikube
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token # Use service account token for auth
    relabel_configs:
      # These apply to the TARGET before scraping
      # Keep only the metrics endpoint for cAdvisor
      - source_labels: [__metrics_path__]
        regex: /metrics/cadvisor
        action: keep

      # Extract the node IP and set the target address to node_ip:kubelet_metrics_port
      - source_labels: [__address__]
        regex: '(.*):.*' # Capture the node IP
        target_label: __address__
        # Replace with the node IP and the Kubelet's cAdvisor port
        # Common ports are 10250 (insecure) or 10255 (secure)
        # Check your Minikube setup to confirm the correct port
        replacement: '${1}:10250' # Assuming Kubelet cAdvisor port is 10250

      # Add node name as a label
      - source_labels: [__meta_kubernetes_node_name]
        target_label: node

      # Add pod name, namespace, and container name labels
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_container_name]
        target_label: container_name

    metric_relabel_configs:
      # These apply to the SCRAPED METRICS after scraping
      # Derive 'com_docker_compose_service' from 'pod_name'
      # The pod_name will be something like 'mock-backend-gpu1-xyz123'.
      # We extract 'mock-backend-gpu1' using a regex.
      - source_labels: [pod_name]
        regex: '(mock-backend-gpu[12]).*' # Capture 'mock-backend-gpu1' or 'mock-backend-gpu2'
        target_label: com_docker_compose_service
        action: replace
      # Derive 'container' label from 'pod_name' to match the backend ID
      # This is crucial for matching queries like `container="mock-backend-gpu1"`
      - source_labels: [pod_name]
        regex: '(mock-backend-gpu[12]).*' # Capture 'mock-backend-gpu1' or 'mock-backend-gpu2'
        target_label: container
        action: replace

  # Scrape application endpoints using Kubernetes service discovery
  # This job discovers endpoints and filters based on labels/annotations
  - job_name: 'application-metrics'
    kubernetes_sd_configs:
      - role: endpoints # Discover service endpoints
    relabel_configs:
      # Filter to scrape endpoints of specific services by name
      # Add the names of your services that expose metrics here
      - source_labels: [__meta_kubernetes_service_name]
        regex: 'proxy-gateway|data-processor|dashboard-api|mock-backend' # Add your service names here
        action: keep

      # Example: Filter to scrape specific ports by number
      # If your services expose metrics on specific ports (e.g., 8080, 5001, 5002)
      - source_labels: [__meta_kubernetes_endpoint_port_number]
        regex: '8080|8081|5001|5002' # Add the ports your apps expose metrics on
        action: keep

      # Set the metrics path if it's not the default /metrics
      # - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
      #   regex: (.+)
      #   target_label: __metrics_path__
      #   action: replace

      # Add useful labels from Kubernetes metadata
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: pod_name
      - source_labels: [__meta_kubernetes_namespace]
        target_label: namespace
      - source_labels: [__meta_kubernetes_container_name]
        target_label: container_name

      # Add the Docker Compose service label if available (less common in pure K8s)
      # This might require extracting labels from the pod metadata
      - source_labels: [__meta_kubernetes_pod_label_com_docker_compose_service]
        regex: '(.+)' # Match any non-empty value
        target_label: com_docker_compose_service # Use the label name expected by data processor
        action: replace # Replace the target label if a match is found

  # You can add more specific jobs if needed, e.g., for specific components
  # - job_name: 'proxy-gateway-metrics'
  #   kubernetes_sd_configs:
  #     - role: endpoints
  #   relabel_configs:
  #     - source_labels: [__meta_kubernetes_service_name]
  #       regex: 'proxy-gateway'
  #       action: keep
  #     - source_labels: [__meta_kubernetes_endpoint_port_number]
  #       regex: '8080' # Assuming proxy-gateway exposes metrics on 8080
  #       action: keep
  #     # Add other relabeling rules as needed

