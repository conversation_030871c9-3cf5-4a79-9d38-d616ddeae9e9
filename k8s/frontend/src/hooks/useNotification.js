import { useState, useCallback } from 'react';

const useNotification = () => {
  const [notification, setNotification] = useState(null);

  const showNotification = useCallback((notificationData) => {
    // Add safety checks
    if (notificationData == null) {
      console.warn('Attempted to show null notification');
      return;
    }
    
    // If notificationData is a string, convert it to a notification object
    if (typeof notificationData === 'string') {
      notificationData = {
        message: notificationData || 'Notification',
        type: 'info',
        duration: 5000
      };
    } else if (typeof notificationData === 'object') {
      // Ensure message is not null or undefined
      if (notificationData.message == null) {
        notificationData.message = 'Notification';
      }
    }

    // Set the notification
    setNotification(notificationData);

    // Auto-close after duration (if specified)
    if (notificationData.duration) {
      setTimeout(() => {
        setNotification(null);
      }, notificationData.duration);
    }
  }, []);

  const closeNotification = useCallback(() => {
    setNotification(null);
  }, []);

  return {
    notification,
    showNotification,
    closeNotification
  };
};

export default useNotification;
export { useNotification };
