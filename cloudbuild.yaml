# Google Cloud Build configuration for GitOps deployment to GKE

# This build will store logs in a regional, user-owned bucket.
# This option is required when a custom service account is used for the build,
# which is implicitly the case for Cloud Build's default service account.
options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET

steps:
# --- Step 1: Build and Push All Docker Images to Artifact Registry ---
# Ensure these image names and tags are consistent with your Kubernetes manifests.

# Build and Push Mock Backends (using a shared Dockerfile.mockbackend)
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-backend-gpu1'
  entrypoint: 'bash' # Use bash to allow conditional execution
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/mock_backend/Dockerfile.mockbackend' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest" \
          'k8s/mock_backend' \
          '--build-arg' 'MOCK_NAME=mock_backend_gpu1' '--build-arg' 'MOCK_GO_FILENAME=mock_backend.go'
      else
        echo "Skipping Build mock-backend-gpu1 as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  dir: '.' # Command executed from root
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-backend-gpu1'
  entrypoint: 'bash' # Use bash to allow conditional execution
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest"
      else
        echo "Skipping Push mock-backend-gpu1 as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  waitFor: ['Build mock-backend-gpu1']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-backend-gpu2'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/mock_backend/Dockerfile.mockbackend' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest" \
          'k8s/mock_backend' \
          '--build-arg' 'MOCK_NAME=mock_backend_gpu2' '--build-arg' 'MOCK_GO_FILENAME=mock_backend.go'
      else
        echo "Skipping Build mock-backend-gpu2 as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-backend-gpu2'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest"
      else
        echo "Skipping Push mock-backend-gpu2 as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  waitFor: ['Build mock-backend-gpu2']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-openai'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/mock_backend/Dockerfile.mockbackend' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest" \
          'k8s/mock_backend' \
          '--build-arg' 'MOCK_NAME=mock_openai' '--build-arg' 'MOCK_GO_FILENAME=mock_openai.go'
      else
        echo "Skipping Build mock-openai as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-openai'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest"
      else
        echo "Skipping Push mock-openai as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  waitFor: ['Build mock-openai']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-google'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/mock_backend/Dockerfile.mockbackend' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest" \
          'k8s/mock_backend' \
          '--build-arg' 'MOCK_NAME=mock_google' '--build-arg' 'MOCK_GO_FILENAME=mock_google.go'
      else
        echo "Skipping Build mock-google as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-google'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest"
      else
        echo "Skipping Push mock-google as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  waitFor: ['Build mock-google']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build mock-anthropic'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/mock_backend/Dockerfile.mockbackend' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest" \
          'k8s/mock_backend' \
          '--build-arg' 'MOCK_NAME=mock_anthropic' '--build-arg' 'MOCK_GO_FILENAME=mock_anthropic.go'
      else
        echo "Skipping Build mock-anthropic as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push mock-anthropic'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_MOCK_BACKENDS}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest"
      else
        echo "Skipping Push mock-anthropic as _BUILD_MOCK_BACKENDS is not 'true'."
      fi
  waitFor: ['Build mock-anthropic']


# --- Core Service Builds ---

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build proxy-gateway'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_PROXY_GATEWAY}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/proxy-gateway/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest" \
          'k8s/proxy-gateway'
      else
        echo "Skipping Build proxy-gateway as _BUILD_PROXY_GATEWAY is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push proxy-gateway'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_PROXY_GATEWAY}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest"
      else
        echo "Skipping Push proxy-gateway as _BUILD_PROXY_GATEWAY is not 'true'."
      fi
  waitFor: ['Build proxy-gateway']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build data-processor'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DATA_PROCESSOR}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/data-processor/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest" \
          'k8s/data-processor'
      else
        echo "Skipping Build data-processor as _BUILD_DATA_PROCESSOR is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push data-processor'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DATA_PROCESSOR}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest"
      else
        echo "Skipping Push data-processor as _BUILD_DATA_PROCESSOR is not 'true'."
      fi
  waitFor: ['Build data-processor']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build kafka_topic_creator'
  entrypoint: 'bash'
  env: ['DOCKER_BUILDKIT=1']
  args:
    - '-c'
    - |
      if [ "${_BUILD_KAFKA_TOPIC_CREATOR}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/kafka_topic_creator/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest" \
          'k8s/kafka_topic_creator'
      else
        echo "Skipping Build kafka_topic_creator as _BUILD_KAFKA_TOPIC_CREATOR is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push kafka_topic_creator'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_KAFKA_TOPIC_CREATOR}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest"
      else
        echo "Skipping Push kafka_topic_creator as _BUILD_KAFKA_TOPIC_CREATOR is not 'true'."
      fi
  waitFor: ['Build kafka_topic_creator']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build redis-populator-job'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_REDIS_POPULATOR_JOB}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/redis-populator-job/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-redis-populator-job:latest" \
          'k8s/redis-populator-job'
      else
        echo "Skipping Build redis-populator-job as _BUILD_REDIS_POPULATOR_JOB is not 'true'."
      fi
  dir: '.'
  waitFor: ['Build kafka_topic_creator']
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push redis-populator-job'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_REDIS_POPULATOR_JOB}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-redis-populator-job:latest"
      else
        echo "Skipping Push redis-populator-job as _BUILD_REDIS_POPULATOR_JOB is not 'true'."
      fi
  waitFor: ['Build redis-populator-job']
  
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build dashboard-api'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DASHBOARD_API}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/dashboard-api/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest" \
          'k8s/dashboard-api'
      else
        echo "Skipping Build dashboard-api as _BUILD_DASHBOARD_API is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push dashboard-api'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_DASHBOARD_API}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest"
      else
        echo "Skipping Push dashboard-api as _BUILD_DASHBOARD_API is not 'true'."
      fi
  waitFor: ['Build dashboard-api']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build policy-manager'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_POLICY_MANAGER}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/policy-manager/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest" \
          'k8s/policy-manager'
      else
        echo "Skipping Build policy-manager as _BUILD_POLICY_MANAGER is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push policy-manager'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_POLICY_MANAGER}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest"
      else
        echo "Skipping Push policy-manager as _BUILD_POLICY_MANAGER is not 'true'."
      fi
  waitFor: ['Build policy-manager']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build ai-optimizer'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_AI_OPTIMIZER}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/ai-optimizer/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest" \
          'k8s/ai-optimizer'
      else
        echo "Skipping Build ai-optimizer as _BUILD_AI_OPTIMIZER is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push ai-optimizer'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_AI_OPTIMIZER}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest"
      else
        echo "Skipping Push ai-optimizer as _BUILD_AI_OPTIMIZER is not 'true'."
      fi
  waitFor: ['Build ai-optimizer']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build evaluation-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_EVALUATION_SERVICE}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/evaluation-service/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-evaluation-service:latest" \
          'k8s/evaluation-service'
      else
        echo "Skipping Build evaluation-service as _BUILD_EVALUATION_SERVICE is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push evaluation-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_EVALUATION_SERVICE}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-evaluation-service:latest"
      else
        echo "Skipping Push evaluation-service as _BUILD_EVALUATION_SERVICE is not 'true'."
      fi
  waitFor: ['Build evaluation-service']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build governance-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_GOVERNANCE_SERVICE}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/governance-service/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-governance-service:latest" \
          'k8s/governance-service'
      else
        echo "Skipping Build governance-service as _BUILD_GOVERNANCE_SERVICE is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push governance-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_GOVERNANCE_SERVICE}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-governance-service:latest"
      else
        echo "Skipping Push governance-service as _BUILD_GOVERNANCE_SERVICE is not 'true'."
      fi
  waitFor: ['Build governance-service']

- name: 'gcr.io/cloud-builders/docker'
  id: 'Build integration-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_INTEGRATION_SERVICE}" = "true" ]; then
        docker build --no-cache \
          -f 'k8s/integration-service/Dockerfile' \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:latest" \
          'k8s/integration-service'
      else
        echo "Skipping Build integration-service as _BUILD_INTEGRATION_SERVICE is not 'true'."
      fi
  dir: '.'
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push integration-service'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_INTEGRATION_SERVICE}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-integration-service:latest"
      else
        echo "Skipping Push integration-service as _BUILD_INTEGRATION_SERVICE is not 'true'."
      fi
  waitFor: ['Build integration-service'] # Wait for build to complete before pushing

# Build and Push Frontend Docker Image (moved here, no external IP needed for frontend app build)
- name: 'gcr.io/cloud-builders/docker'
  id: 'Build and Push Frontend'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_FRONTEND}" = "true" ]; then
        echo "Building frontend (relative paths, no external IP injection into app)..."
        docker build \
          --no-cache \
          -f k8s/frontend/Dockerfile \
          -t "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest" \
          'k8s/frontend'
      else
        echo "Skipping Build and Push Frontend as _BUILD_FRONTEND is not 'true'."
      fi
  dir: '.'
  waitFor: ['Build dashboard-api', 'Build policy-manager'] # Wait for related APIs to be built first
- name: 'gcr.io/cloud-builders/docker'
  id: 'Push Frontend Image'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      if [ "${_BUILD_FRONTEND}" = "true" ]; then
        docker push "${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest"
      else
        echo "Skipping Push Frontend Image as _BUILD_FRONTEND is not 'true'."
      fi
  dir: '.'
  waitFor: ['Build and Push Frontend']


# --- Kubernetes Interaction Pre-Deployment Steps ---

# Get GKE Credentials (MUST be before any kubectl commands interacting with your cluster)
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Get GKE Credentials'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      gcloud container clusters get-credentials "${_GKE_CLUSTER_NAME}" --region "${_GCP_REGION}" --project "${_GCP_PROJECT_ID}"
  waitFor:
    - 'Push mock-backend-gpu1'
    - 'Push mock-backend-gpu2'
    - 'Push mock-openai'
    - 'Push mock-google'
    - 'Push mock-anthropic'
    - 'Push proxy-gateway'
    - 'Push data-processor'
    - 'Push kafka_topic_creator'
    - 'Push dashboard-api'
    - 'Push policy-manager'
    - 'Push ai-optimizer'
    - 'Push Frontend Image'
    - 'Push evaluation-service'
    - 'Push redis-populator-job'
    - 'Push governance-service'
    - 'Push integration-service'

# Debug: List contents of k8s/scripts/ directory
- name: 'bash'
  id: 'Debug: List k8s/scripts/ directory'
  args: ['ls', '-lR', 'k8s/scripts/']
  dir: '.'
  waitFor: ['Get GKE Credentials']

# Make New_Autostart.sh executable
- name: 'bash'
  id: 'Make Autostart Script Executable'
  args: ['chmod', '+x', 'k8s/New_Autostart.sh']
  dir: '.'
  waitFor: ['Debug: List k8s/scripts/ directory']

# Make create_redis_populator_job.sh executable
- name: 'bash'
  id: 'Make Redis Populator Executable'
  args: ['chmod', '+x', 'k8s/redis-populator-job/create_redis_populator_job.sh']
  dir: '.'
  waitFor: ['Make Autostart Script Executable']


# --- Main Deployment Step ---

# Apply all Kubernetes manifests including the new policy-manager.yaml
- name: 'gcr.io/cloud-builders/kubectl'
  id: 'Deploy All Services' # Renamed from 'Deploy All Services (Initial)'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Install gettext (contains envsubst) within this same step
      apt-get update && apt-get install -y gettext-base

      # Pass required variables as environment variables
      export GCP_PROJECT_ID="${_GCP_PROJECT_ID}"
      export GCP_REGION="${_GCP_REGION}"
      export GKE_CLUSTER_NAME="${_GKE_CLUSTER_NAME}"
      export ARTIFACT_REGISTRY_REPO="${_ARTIFACT_REGISTRY_REPO}"
      export BUILD_ID="${_BUILD_ID}"
      export MOCK_BACKEND_GPU1_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu1:latest"
      export MOCK_BACKEND_GPU2_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-backend-gpu2:latest"
      export PROXY_GATEWAY_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-proxy-gateway:latest"
      export FRONTEND_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-frontend:latest"
      export KAFKA_TOPIC_CREATOR_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-kafka-topic-creator:latest"
      export DATA_PROCESSOR_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-data-processor:latest"
      export DASHBOARD_API_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-dashboard-api:latest"
      export POLICY_MANAGER_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-policy-manager:latest"
      export AI_OPTIMIZER_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-ai-optimizer:latest"
      export MOCK_OPENAI_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-openai:latest"
      export MOCK_GOOGLE_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-google:latest"
      export MOCK_ANTHROPIC_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-mock-anthropic:latest"
      export EVALUATION_SERVICE_IMAGE="${_GCP_REGION}-docker.pkg.dev/${_GCP_PROJECT_ID}/${_ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-evaluation-service:latest"

      # Export build and deploy flags
      export _BUILD_DATA_PROCESSOR="${_BUILD_DATA_PROCESSOR}"
      export _DEPLOY_FRONTEND="${_DEPLOY_FRONTEND}"
      export _DEPLOY_POLICY_MANAGER="${_DEPLOY_POLICY_MANAGER}"
      export _DEPLOY_REDIS_POPULATOR_JOB="${_DEPLOY_REDIS_POPULATOR_JOB}"

      # Execute New_Autostart.sh to deploy all services including frontend.
      # This script should handle image replacement for all YAMLs it applies.
      k8s/New_Autostart.sh
  dir: '.'
  waitFor: ['Make Redis Populator Executable'] # Still waits for scripts to be executable,
                                              # and implicitly waits for all image pushes due to earlier dependencies on 'Get GKE Credentials'

# Removed: Get Frontend External IP
# Removed: Rebuild and Push Frontend
# Removed: Push Frontend Image
# Removed: Re-deploy Frontend


# Define substitute variables for project ID, region, cluster name, and repo name.
# Default values for build flags (set to "true" to build by default)
substitutions:
  _GCP_PROJECT_ID: 'silken-zenith-460615-s7'
  _GCP_REGION: 'us-central1'
  _GKE_CLUSTER_NAME: 'ai-optimizer-cluster'
  _ARTIFACT_REGISTRY_REPO: 'ai-optimizer-repo'
  _BUILD_ID: '${BUILD_ID}' # Cloud Build's built-in BUILD_ID

  # --- Build Flags for Selective Builds ---
  _BUILD_MOCK_BACKENDS: 'true'
  _BUILD_PROXY_GATEWAY: 'true'
  _BUILD_DATA_PROCESSOR: 'true'
  _BUILD_KAFKA_TOPIC_CREATOR: 'true'
  _BUILD_DASHBOARD_API: 'true'
  _BUILD_POLICY_MANAGER: 'true'
  _BUILD_AI_OPTIMIZER: 'true'
  _BUILD_FRONTEND: 'true'
  _BUILD_EVALUATION_SERVICE: 'true'
  _BUILD_REDIS_POPULATOR_JOB: 'true'
  _BUILD_GOVERNANCE_SERVICE: 'true'
  _BUILD_INTEGRATION_SERVICE: 'true'

  # --- Deploy Flags for Selective Deploys ---
  _DEPLOY_FRONTEND: 'true'
  _DEPLOY_POLICY_MANAGER: 'true'
  _DEPLOY_REDIS_POPULATOR_JOB: 'true'

