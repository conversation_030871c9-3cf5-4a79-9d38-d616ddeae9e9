LLM Cost-Performance Optimizer: Product Documentation
This document provides an overview and operational guide for application developers integrating with and administering the LLM Cost-Performance Optimizer.

1. Product Overview
The LLM Cost-Performance Optimizer is an intelligent AI Proxy Gateway designed to sit in front of various Large Language Models (LLMs). Its core mission is to empower developers and administrators with advanced capabilities for intelligent routing, flexible policy enforcement, real-time performance monitoring, and automated cost optimization across multiple LLM providers.

Why use this system?

Cost Efficiency: Automatically route requests to the most cost-effective LLM backend based on real-time metrics and dynamic preferences.

Performance Optimization: Prioritize latency for critical applications by routing to the fastest available LLM.

Flexibility & Control: Define custom routing policies, manage model profiles, and even override automatic decisions when needed.

Observability: Gain deep insights into LLM usage, performance, and costs through a centralized dashboard.

Conversational Awareness: Maintain context across multi-turn conversations for a more seamless user experience.

2. Key Features
The current MVP provides the following robust features:

Centralized Policy Management: Define and manage routing policies through an intuitive Admin Dashboard or directly via API. Policies dictate which LLM backend a request should go to, based on various criteria.

Configurable Model Profiles: Set up and maintain detailed profiles for different LLM backend endpoints, including their expected performance, cost, capabilities, and API keys.

Intelligent Prompt Classification: The system automatically classifies incoming LLM prompts into categories (e.g., creative_writing, code_generation, complex_reasoning, factual_query, general_chat) to inform routing decisions.

Semantic Routing (LLM-Assisted): Beyond simple classification, the system can understand the semantic meaning of a prompt by generating embeddings and matching them against predefined "semantic policies." This enables more nuanced and context-aware routing to models best suited for specific types of queries.

Real-time Performance & Cost Monitoring: A comprehensive data pipeline collects real-time inference logs, including latency, token usage, and computed costs. This data is stored and used by the AI Optimizer for dynamic routing decisions, and is visualized on the Admin Dashboard.

Dynamic Routing Preference: Administrators can dynamically adjust the AI Optimizer's primary routing objective (e.g., prioritize cost, prioritize latency, or maintain a balance).

Conversational Context Management: The proxy can intelligently store and retrieve multi-turn conversation history in Redis, allowing LLM requests to be sent with full conversational context for a more coherent user experience.

User-Controlled Routing Overrides: Application developers can explicitly override the automatic routing decisions by specifying a preferred backend via a custom HTTP header.

Transparency Logging: Observe a detailed log of recent routing decisions, including which policies were applied and which backend was chosen for individual requests.

3. Core Components
The LLM Cost-Performance Optimizer is comprised of several interconnected microservices:

proxy-gateway (Client-Facing): The primary entry point for all LLM requests. It intercepts incoming requests, communicates with the ai-optimizer for routing decisions, handles conversational context, applies user overrides, proxies requests to the chosen LLM backend, and publishes comprehensive inference logs to Kafka.

ai-optimizer (Intelligence Hub): The brain of the system. It receives routing requests from the proxy-gateway, performs prompt classification (task type), semantic analysis (embeddings), retrieves real-time metrics from ClickHouse, considers dynamic preferences, and determines the optimal LLM backend. It also publishes the globally optimal backend ID to Redis.

policy-manager (Configuration Service): Manages all routing policies and model profiles. It stores this configuration in Redis and notifies other services (like proxy-gateway and ai-optimizer) about updates via Redis Pub/Sub.

data-processor (Metrics Collection & Enrichment): Consumes raw inference logs from Kafka, enriches them with additional data (e.g., CPU/memory usage from Prometheus, calculated total cost based on model profiles), and persists the enriched data into ClickHouse for historical analysis and real-time optimization.

redis (Key-Value Store): Used for caching policies, model profiles, publishing optimal backend IDs, and storing short-term conversational context.

clickhouse (Analytical Database): A columnar database optimized for high-throughput ingestion and analytical queries, storing all enriched inference logs for performance monitoring and historical analysis.

kafka (Message Broker): A distributed streaming platform used for reliable and asynchronous communication of inference logs from proxy-gateway to data-processor.

prometheus (Monitoring): Collects system-level metrics (e.g., CPU, memory usage of LLM backends) that are then integrated into the cost calculation by data-processor.

4. How to Use the Proxy (for Application Developers)
Application developers will interact primarily with the proxy-gateway.

Proxy Endpoint:

All LLM chat completion requests should be sent to the following endpoint:

http://<PROXY_GATEWAY_IP>:8080/api/v1/chat/completions

Replace <PROXY_GATEWAY_IP> with the actual IP address of your proxy-gateway service.

Request Body:

The request body should follow the standard LLM Chat Completion API format (e.g., OpenAI's /v1/chat/completions endpoint).

New Request Body Fields for Enhanced Functionality:

"conversation_id": "your-unique-conversation-id" (Optional):

Purpose: To enable multi-turn conversational context.

Usage: For the first turn of a conversation, you can omit this field, and the proxy will automatically generate a new conversation_id. For all subsequent turns within the same conversation, you must include the same conversation_id obtained from the first turn. The proxy will then retrieve previous messages from Redis and include them in the LLM request.

Example:

{
  "conversation_id": "chat-session-12345",
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "What is the capital of France?"}
  ]
}

New HTTP Headers for User-Controlled Routing:

X-Preferred-Backend-ID: <backend-id> (Optional):

Purpose: Allows an application developer to explicitly override the AI Optimizer's automatic routing decision and force a request to a specific backend.

Usage: Include this header with the ID of the desired model profile (e.g., gemini-pro-mock, claude-haiku-mock, gpt-4o-mini-mock).

Caution: Use this judiciously, as it bypasses the system's optimization logic. The override will be logged in the inference data.

Example:

curl -X POST \
  http://<PROXY_GATEWAY_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-Backend-ID: gemini-pro-mock" \
  -d '{
    "model": "gemini-pro",
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate factorial recursively."}
    ]
  }'

Example curl Commands:

Basic Request (Intelligent Routing Applied):

curl -X POST \
  http://<PROXY_GATEWAY_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ]
  }'

Conversational Turn 1 (New Conversation):

curl -X POST \
  http://<PROXY_GATEWAY_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
# Observe logs for the generated conversation_id. E.g., "Generated new ID: <uuid>"

Conversational Turn 2 (Continuing the Conversation - REPLACE <YOUR_CONVERSATION_ID>):

curl -X POST \
  http://<PROXY_GATEWAY_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'

Request with User Override:

curl -X POST \
  http://<PROXY_GATEWAY_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-Backend-ID: gpt-4o-mini-mock" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Analyze the philosophical implications of AI sentience."}
    ]
  }'

5. Administering Policies (for Developers as Admins)
As an administrator, you'll use the Frontend Dashboard to manage the system's behavior.

Frontend Dashboard URL:

http://<FRONTEND_DASHBOARD_IP>:3000

Replace <FRONTEND_DASHBOARD_IP> with the actual IP address of your frontend-dashboard service.

Key Concepts in Administration:


Model Profiles: These define the characteristics of each LLM backend available to the system. You'll specify their ID, name, aliases, capabilities, pricing tier, data sensitivity, expected latency, expected cost per token, CPU/memory costs, and the actual backend URL and API key. Maintaining accurate model profiles is crucial for effective optimization.
Policies: Policies contain criteria that, when matched, trigger an action (currently routing to a specific backend_id). While the ai-optimizer now handles dynamic routing, these policies can still serve as hard rules or provide specific routing for certain model_name requests if preferred.

Dynamic Routing Preference:

The ai-optimizer can dynamically shift its optimization strategy. You can instruct it to prioritize cost, latency, or a balanced approach. This is currently done via a direct API call to the ai-optimizer service.

Endpoint: http://<AI_OPTIMIZER_IP>:8085/set-preference

Method: POST

Request Body: {"preference": "<value>"}

Possible Values for <value>:

"cost_priority": The optimizer will heavily weight cost reduction in its routing decisions.

"latency_priority": The optimizer will prioritize the fastest response time, even if it incurs higher costs.

"balanced": The optimizer will attempt to find a good balance between cost and latency (default).

Example API Call to Set Preference:

curl -X POST \
  http://<AI_OPTIMIZER_IP>:8085/set-preference \
  -H "Content-Type: application/json" \
  -d '{"preference": "cost_priority"}'

(Replace <AI_OPTIMIZER_IP> with the ClusterIP of ai-optimizer or if you have port-forwarded it).

6. Monitoring & Observability
All LLM inference requests passing through the proxy-gateway are logged with detailed information, including latency, token counts, selected backend, policy applied, and more. This data is processed by the data-processor and stored in ClickHouse.

Admin Dashboard: The "Monitoring" section of the Frontend Dashboard will provide aggregated and time-series views of these metrics, allowing you to track overall system performance, costs per backend, and usage patterns.

ClickHouse: For advanced analytics or custom dashboards, you can directly query the inference_logs table in the clickhouse database.

7. Future Enhancements (Roadmap)
Building on this foundation, future iterations will focus on:

Automated Caching: Implement caching for frequently repeated queries to reduce costs and latency further.

Advanced Prompt Optimization: Techniques like automated conciseness, token budgeting, and smart input truncation to minimize token usage before sending to LLMs.

Model Compilation & Quantization Integration: Exploring how the platform can integrate with or recommend tools for optimizing self-hosted LLMs for lower infrastructure costs and improved inference performance.
