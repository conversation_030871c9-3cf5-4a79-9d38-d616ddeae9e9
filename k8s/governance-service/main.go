package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"
)

func main() {
	fmt.Println("Governance service starting...")

	// You might want to initialize your content filtering/PII detection logic here if needed once
	// For demonstration, these functions are called on each request in the example below.

	// Define a handler for the root path
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Example usage of your functions within a request handler
		content := "This is a sample text with potentially sensitive information like SSN: 123-456-7890."
		filteredContent := filterContent(content)
		piiData := detectPII(content)

		response := fmt.Sprintf("Governance service is running!\nFiltered content: %s\nPII data detected: %v", filteredContent, piiData)
		fmt.Fprint(w, response)
	})

	// Set the port for the HTTP server
	port := ":8080" // Ensure this matches the containerPort in your Kubernetes YAML
	fmt.Printf("Listening on port %s...\n", port)

	// Start the HTTP server. log.Fatal will keep the program running until an error occurs.
	log.Fatal(http.ListenAndServe(port, nil))
}

func filterContent(content string) string {
	// Implement content filtering logic here
	filtered := strings.ReplaceAll(content, "SSN", "***")
	return filtered
}

func detectPII(content string) []string {
	// Implement PII detection logic here
	if strings.Contains(content, "SSN") {
		return []string{"SSN detected"}
	}
	return []string{}
}
