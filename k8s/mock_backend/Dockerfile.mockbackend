# Use a specific, stable Go builder image
FROM golang:1.24 as builder

# Set the working directory inside the container for the source code
WORKDIR /app

# Define build arguments
ARG MOCK_NAME         # The desired name for the final binary (e.g., mock_openai)
ARG MOCK_GO_FILENAME  # The actual .go file name (e.g., mock_openai.go)

# Copy the go.mod and go.sum files from the build context
COPY go.mod go.sum ./

# Download the Go module dependencies.
RUN go mod download

# Copy the specific mock's source code file and rename it to main.go for compilation.
COPY ${MOCK_GO_FILENAME} ./main.go

# Build the Go application binary
RUN CGO_ENABLED=0 go build -o ${MOCK_NAME} -ldflags='-extldflags="-static"' .

# --- START DIAGNOSTIC STEPS (BUILDER STAGE) ---
# Install 'file' utility in the builder stage for diagnostics
RUN apt-get update && apt-get install -y --no-install-recommends file && rm -rf /var/lib/apt/lists/*

# Verify the binary exists and is executable in the builder stage
RUN echo "--- Builder Stage Diagnostics ---"
RUN ls -l /app/${MOCK_NAME}
RUN file /app/${MOCK_NAME}
# REMOVED: RUN /app/${MOCK_NAME} --help || echo "Binary ran partially or failed in builder stage, but it's present."
# This line caused the hang because the mock backend starts a server and doesn't exit.
# --- END DIAGNOSTIC STEPS (BUILDER STAGE) ---

# Use a minimal Alpine Linux image for the final stage
FROM alpine:latest
WORKDIR /app
# Add netcat-openbsd and 'file' for diagnostics in final image
RUN apk update && apk add --no-cache netcat-openbsd file

# Copy the compiled binary from the builder stage
COPY --from=builder /app/${MOCK_NAME} .

# Ensure the binary is executable in the final image
RUN chmod +x /app/${MOCK_NAME}

# --- START DIAGNOSTIC STEPS (FINAL STAGE) ---
# Verify the binary exists and is executable in the final image
RUN echo "--- Final Stage Diagnostics ---"
RUN ls -l /app/
RUN file /app/${MOCK_NAME}
# REMOVED: RUN /app/${MOCK_NAME} --help || echo "Binary ran partially or failed in final stage, but it's present."
# This line would also cause a hang in the final image build.
# --- END DIAGNOSTIC STEPS (FINAL STAGE) ---

# Create a small shell script to act as the entrypoint
# This script will receive the MOCK_NAME as its first argument from the K8s Deployment args
# and then execute the correct binary.
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the ENTRYPOINT to the wrapper script.
# This is now fixed and will always be the wrapper script.
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Set the default CMD for the ENTRYPOINT.
# This CMD will be passed as arguments to the ENTRYPOINT script.
# The first argument will be the MOCK_NAME, followed by any other args.
CMD ["mock_backend_gpu1"] # Default value, will be overridden by K8s Deployment args

