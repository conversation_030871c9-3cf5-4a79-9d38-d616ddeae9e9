/** @type {import('tailwindcss').Config} */
module.exports = {
  // IMPORTANT: This 'content' array tells Tailwind where to scan for CSS classes.
  // Make sure it includes the path to your React component files (e.g., .jsx, .js, .ts, .tsx)
  content: [
    "./index.html", // If you have an index.html file at the root
    "./src/**/*.{js,ts,jsx,tsx}", // Scans all JS, TS, JSX, TSX files in the 'src' directory and its subdirectories
  ],
  theme: {
    extend: {}, // For extending Tailwind's default theme (e.g., custom colors, fonts)
  },
  plugins: [], // For adding Tailwind plugins (e.g., @tailwindcss/forms, @tailwindcss/typography)
};

