apiVersion: apps/v1
kind: Deployment
metadata:
  name: policy-manager
  labels:
    app: policy-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: policy-manager
  template:
    metadata:
      labels:
        app: policy-manager
    spec:
      serviceAccountName: policy-manager-ksa # Associated with Workload Identity
      initContainers:
      - name: wait-for-redis
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Redis..."; until nc -z redis 6379; do echo "Redis not ready, waiting..."; sleep 2; done; echo "Redis is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      containers:
      - name: policy-manager
        image: ai-cost-performance-optimizer-policy-manager:latest
        imagePullPolicy: Always # Always pull the latest image to ensure updates
        ports:
        - containerPort: 8083 # Policy Manager listens on 8083
          name: http
        env:
        - name: REDIS_ADDR
          value: "redis:6379" # Redis service in the same namespace
        - name: REDIS_PASSWORD
          value: "" # Assuming no password for Redis, or set via secret
        - name: LISTEN_ADDR # <--- CHANGED FROM PORT TO LISTEN_ADDR
          value: ":8083"   # Policy Manager will listen on this port
        # API Keys for external LLMs used in initial population
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-api-key
              key: OPENAI_API_KEY
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: google-api-key
              key: GOOGLE_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: anthropic-api-key
              key: ANTHROPIC_API_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 8083
          initialDelaySeconds: 15
          periodSeconds: 10
---
# Internal service for other components to access the Policy Manager
apiVersion: v1
kind: Service
metadata:
  name: policy-manager
  labels:
    app: policy-manager
spec:
  selector:
    app: policy-manager
  ports:
  - name: http
    protocol: TCP
    port: 8083     # Service port
    targetPort: 8083 # Container port
  type: ClusterIP # Internal service, not exposed externally by default
---

