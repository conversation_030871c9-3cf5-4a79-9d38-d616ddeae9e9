{"version": 4, "terraform_version": "1.11.4", "serial": 46, "lineage": "f568e341-48cc-edec-f2c4-d018f767480a", "outputs": {}, "resources": [{"mode": "data", "type": "google_client_config", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"access_token": "ya29.c.c0ASRK0GYXSqpAjuedHklKEnp6bkz7PVzKLbnHzz7JopuVKQ62LELmO4ll5eVwQ0Vi2C7j1H-Zdwm1R9LBu4nh2g-6X7l3Th6wMm4M1aRsZRuAa8zkFhoe0H3HTC5MvxcVH87nzbJsHh_9ERxq67CX_0EN1f8UUjDup0ApCPb1GWbHTS-G_eBkRZPORWDr6jdketMr6XOOr9re7ymRUnJxSwVuAL0upAQr7UuLD0f3PLo2ZuaN8SOxhC6vyZqIDBX1kEV9YikWutRWhejVLRN6_njbtO3YY9TZNKLwa755Q-dClZ89v1m7ayuOi9f6xyoW54lDsUaKigP-WfEvHl3R4cjINxojeQ4AcF6GMf6oALlc55M5Oik9hS07E385CbMQzp3hZde0SfntJ-M-oSkYn5nh-gIVzyh8Jasg94_bbJa0w3vtktm_fBZersUwgQBwbmS3XX3SdMjx4xmRyQFtQI-yaodab9a8JS67xXUvYa6VroVlOlU1zbbOQeXQtZ6rceVvaWZ90bpoWRzjiRl3rhvUORWbZ969c16Vfvp3rY3SydFUo1tZFzyf1bp53B8uQ9Mo8v2Qut3IQr3RWkrJxU7ttUdxO9bwz81Wu6-lma0a-apX8e7xFQzzanvJSUO9WtWZzis4rrml9ho6zwsg2-bhvx8iZjhSh9UuzOsB3fWz2xj3Mo2MFu0znjBRaWYV3-7qOpe5pRel1Sf7nwv8FraZZ00vIvdoS4Scm7RkJ_XkfQJz8iYuVMa7nWR4cUqefa7Rks5svZhQwcylI3SOX_1u9cl2wJmXFtFOey1uuSXp0RVaJ64WJmi6om5mR-RYJbfjS6_kSijYjakRz56SfdewJem4jq87qIdI6pXt2nrt1RSxUlR-czigmYreFiQaJlasu7J76yr2h15sj40VJYyFkzWFXJc1a1e3rvFsmQ3a_ZwdSUOj9rVl4g_Uir2XJB7kVjymQ0Sunoa_qOVMOl2VWnyf8SMdIsMl7ton9RYVjtMp9BIpXdX", "id": "projects/\"silken-zenith-460615-s7\"/regions/<null>/zones/<null>", "project": "silken-zenith-460615-s7", "region": null, "zone": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "access_token"}]]}]}, {"mode": "data", "type": "google_container_cluster", "name": "primary", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": true}]}], "allow_net_admin": null, "authenticator_groups_config": [], "binary_authorization": [], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "*********/14", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "", "default_max_pods_per_node": 110, "default_snat_status": [], "description": "", "dns_config": [], "enable_autopilot": false, "enable_binary_authorization": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "*************", "gateway_api_config": [], "id": "projects/silken-zenith-460615-s7/locations/us-central1/clusters/ai-optimizer-cluster", "initial_node_count": 0, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "*********/14", "cluster_secondary_range_name": "gke-ai-optimizer-cluster-pods-8ea9e383", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "************/20", "services_secondary_range_name": "", "stack_type": "IPV4"}], "label_fingerprint": "cfb7e07e", "location": "us-central1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.32.4-gke.1415000", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "relay_mode": ""}], "enable_components": ["SYSTEM_COMPONENTS", "DEPLOYMENT", "STATEFULSET", "JOBSET", "STORAGE", "HPA", "POD", "DAEMONSET", "CADVISOR", "KUBELET", "DCGM"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "ai-optimizer-cluster", "network": "projects/silken-zenith-460615-s7/global/networks/default", "network_policy": [{"enabled": false, "provider": "PROVIDER_UNSPECIFIED"}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [{"threads_per_core": 0}], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 20, "disk_type": "pd-standard", "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "pod_pids_limit": 0}], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-4", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"], "preemptible": false, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "on-demand"}, "service_account": "default", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "node_pool": [{"autoscaling": [{"location_policy": "ANY", "max_node_count": 0, "min_node_count": 0, "total_max_node_count": 3, "total_min_node_count": 1}], "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-c/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-76d5b428-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-a/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-5ea61ba4-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-b/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-cd25aa8f-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-c/instanceGroups/gke-ai-optimizer-cluster-pool-1-76d5b428-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-a/instanceGroups/gke-ai-optimizer-cluster-pool-1-5ea61ba4-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-b/instanceGroups/gke-ai-optimizer-cluster-pool-1-cd25aa8f-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "pool-1", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-ai-optimizer-cluster-pods-8ea9e383"}], "node_config": [{"advanced_machine_features": [{"threads_per_core": 0}], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 20, "disk_type": "pd-standard", "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "pod_pids_limit": 0}], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-4", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"], "preemptible": false, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "on-demand"}, "service_account": "default", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_count": 0, "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "placement_policy": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.32.4-gke.1415000"}], "node_pool_auto_config": [{"network_tags": null}], "node_pool_defaults": [{"node_config_defaults": [{"logging_variant": "DEFAULT"}]}], "node_version": "1.32.4-gke.1415000", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "***********", "private_endpoint_subnetwork": "", "public_endpoint": "*************"}], "private_ipv6_google_access": "", "project": "silken-zenith-460615-s7", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": null, "resource_labels": {"app": "ai-optimizer-platform", "env": "dev"}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/silken-zenith-460615-s7/locations/us-central1/clusters/ai-optimizer-cluster", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "************/20", "subnetwork": "projects/silken-zenith-460615-s7/regions/us-central1/subnetworks/default", "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": [{"workload_pool": "silken-zenith-460615-s7.svc.id.goog"}]}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "kubernetes_config_map", "name": "kafka-log4j-config", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"binary_data": {}, "data": {"log4j.properties": ""}, "id": "default/kafka-log4j-config", "immutable": false, "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "kafka-log4j-config", "namespace": "default", "resource_version": "1750982709020943018", "uid": "be79f100-775a-48b2-b13f-0e4533145f65"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_config_map", "name": "kafka-server-properties-config", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"binary_data": null, "data": {"server.properties": ""}, "id": "default/kafka-server-properties-config", "immutable": false, "metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": null, "name": "kafka-server-properties-config", "namespace": "default", "resource_version": "1750982709799919024", "uid": "5461f2f6-c7f3-44e5-87bf-3978744923ee"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/frontend", "metadata": [{"annotations": null, "generate_name": "", "generation": 1, "labels": null, "name": "frontend", "namespace": "default", "resource_version": "1750982715374463008", "uid": "25584a30-52ee-42e9-80a2-01aa2fe0ef31"}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "1", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "frontend"}}], "strategy": [{"rolling_update": [{"max_surge": "25%", "max_unavailable": "25%"}], "type": "RollingUpdate"}], "template": [{"metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": {"app": "frontend"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": null, "command": null, "env": [], "env_from": [], "image": "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-frontend:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "frontend", "port": [{"container_port": 80, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}], "readiness_probe": [], "resources": [{"limits": {}, "requests": {}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [], "node_name": "", "node_selector": null, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": true}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "governance-service", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"status": "tainted", "schema_version": 1, "attributes": {"id": "default/governance-service", "metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": null, "name": "governance-service", "namespace": "default", "resource_version": "", "uid": ""}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "governance-service"}}], "strategy": [], "template": [{"metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": {"app": "governance-service"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": null, "command": null, "env": [], "env_from": [], "image": "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-governance-service:latest", "image_pull_policy": "", "lifecycle": [], "liveness_probe": [], "name": "governance-service", "port": [{"container_port": 8080, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}], "readiness_probe": [], "resources": [], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [], "node_name": "", "node_selector": null, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "", "security_context": [], "service_account_name": "", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": true}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "integration-service", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"status": "tainted", "schema_version": 1, "attributes": {"id": "default/integration-service", "metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": null, "name": "integration-service", "namespace": "default", "resource_version": "", "uid": ""}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "integration-service"}}], "strategy": [], "template": [{"metadata": [{"annotations": null, "generate_name": "", "generation": 0, "labels": {"app": "integration-service"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": null, "command": null, "env": [], "env_from": [], "image": "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-integration-service:latest", "image_pull_policy": "", "lifecycle": [], "liveness_probe": [], "name": "integration-service", "port": [{"container_port": 8080, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}], "readiness_probe": [], "resources": [], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [], "node_name": "", "node_selector": null, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "", "security_context": [], "service_account_name": "", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": true}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "clickhouse", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/clickhouse", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "clickhouse", "namespace": "default", "resource_version": "1750982709659919008", "uid": "0f89f0db-bb26-46d5-87fc-7df58490d742"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "*************", "cluster_ips": ["*************"], "external_ips": [], "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "", "node_port": 31308, "port": 8123, "protocol": "TCP", "target_port": "8123"}], "publish_not_ready_addresses": false, "selector": {"app": "clickhouse"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/frontend", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": null, "name": "frontend", "namespace": "default", "resource_version": "1750982750487519019", "uid": "c0812243-c7ba-4598-a750-cf775f46450f"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": null, "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": null, "port": [{"app_protocol": "", "name": "", "node_port": 32303, "port": 80, "protocol": "TCP", "target_port": "80"}], "publish_not_ready_addresses": false, "selector": {"app": "frontend"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": [{"hostname": "", "ip": "************"}]}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "governance-service", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/governance-service", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": null, "name": "governance-service", "namespace": "default", "resource_version": "1750982749508191005", "uid": "f8a2be78-9960-40c9-87a7-3df39148ea8e"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": null, "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": null, "port": [{"app_protocol": "", "name": "", "node_port": 32461, "port": 8080, "protocol": "TCP", "target_port": "8080"}], "publish_not_ready_addresses": false, "selector": {"app": "governance-service"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": [{"hostname": "", "ip": "**************"}]}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "integration-service", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/integration-service", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": null, "name": "integration-service", "namespace": "default", "resource_version": "1750982753014191024", "uid": "8b91aa10-0c21-4376-b6e3-9ac2fadf4534"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": null, "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": null, "port": [{"app_protocol": "", "name": "", "node_port": 31886, "port": 8080, "protocol": "TCP", "target_port": "8080"}], "publish_not_ready_addresses": false, "selector": {"app": "integration-service"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": [{"hostname": "", "ip": "*************"}]}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "kafka", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/kafka", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "kafka", "namespace": "default", "resource_version": "1750982709406047018", "uid": "62164713-136b-4d57-9053-fa2eddc61d44"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": [], "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "", "node_port": 32559, "port": 9092, "protocol": "TCP", "target_port": "9092"}], "publish_not_ready_addresses": false, "selector": {"app": "kafka"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "prometheus", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/prometheus", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "prometheus", "namespace": "default", "resource_version": "1750982710117743011", "uid": "4f87f391-a04c-4d94-8855-d8e6aecf92d9"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "*************", "cluster_ips": ["*************"], "external_ips": [], "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "", "node_port": 30387, "port": 9090, "protocol": "TCP", "target_port": "9090"}], "publish_not_ready_addresses": false, "selector": {"app": "prometheus"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": [{"hostname": "", "ip": "**********"}]}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}, {"mode": "managed", "type": "kubernetes_service", "name": "redis", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/redis", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "redis", "namespace": "default", "resource_version": "1750982710178223023", "uid": "d14076f1-b4ad-43d8-b96a-8e9b57ffc0ce"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "*************", "cluster_ips": ["*************"], "external_ips": [], "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "", "node_port": 32380, "port": 6379, "protocol": "TCP", "target_port": "6379"}], "publish_not_ready_addresses": false, "selector": {"app": "redis"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": true}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.google_client_config.default", "data.google_container_cluster.primary"]}]}], "check_results": null}