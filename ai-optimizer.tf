resource "kubernetes_deployment" "ai_optimizer" {
  metadata {
    name      = "ai-optimizer"
    namespace = "default"
  }
  spec {
    replicas = 1 # Often good to specify replicas, though default is 1
    selector {
      match_labels = {
        app = "ai-optimizer" # THIS MUST MATCH THE LABELS IN TEMPLATE.METADATA
      }
    }
    template {
      metadata {
        labels = {
          app = "ai-optimizer" # THIS MUST MATCH THE match_labels IN SELECTOR
        }
      }
      spec {
        init_container {
          name  = "wait-for-clickhouse-and-redis"
          image = "busybox:1.36"
          command = [
            "sh",
            "-c",
          "echo \"Waiting for ClickHouse and Redis...\"; until kubectl get pod -l app=clickhouse -o jsonpath='{.status.conditions[?(@.type==\\\"Ready\\\")].status}' | grep -q \"True\" && nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"ClickHouse and <PERSON><PERSON> are ready.\"",
          ]
          resources {
            limits = {
              cpu    = "20m"
              memory = "20Mi"
            }
            requests = {
              cpu    = "10m"
              memory = "10Mi"
            }
          }
        }
        init_container {
          name  = "wait-for-inference-logs-table"
          image = "gcr.io/cloud-builders/kubectl:latest"
          command = [
            "/bin/bash",
            "-c",
          ]
          args = [
           file("k8s/ai-optimizer/wait-for-inference-logs.sh")
          ]
          resources {
            limits = {
              cpu    = "200m"
              memory = "200Mi"
            }
            requests = {
              cpu    = "100m"
              memory = "100Mi"
            }
          }
        }
        container {
          name  = "ai-optimizer-container"
          image = "us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-ai-optimizer:latest" # Replace with your actual image
          port {
            container_port = 8080
          }
          env {
            name  = "CLICKHOUSE_HOST"
            value = "clickhouse"
          }
          env {
            name  = "CLICKHOUSE_PORT"
            value = "9000"
          }
          env {
            name  = "CLICKHOUSE_DB"
            value = "default"
          }
          env {
            name  = "CLICKHOUSE_USER"
            value = "test"
          }
          env {
            name  = "CLICKHOUSE_PASSWORD"
            value = "test"
          }
          # The above credentials are used to connect to ClickHouse
        }
      }
    }
  }
}

variable "kafka_image" {
  type    = string
  default = "confluentinc/cp-kafka:7.5.0"
}

variable "kafka_replicas" {
  type    = number
  default = 1
}

variable "kafka_cpu_limit" {
  type    = string
  default = "2"
}

variable "kafka_memory_limit" {
  type    = string
  default = "4Gi"
}

variable "kafka_cpu_request" {
  type    = string
  default = "1"
}

variable "kafka_memory_request" {
  type    = string
  default = "2Gi"
}
