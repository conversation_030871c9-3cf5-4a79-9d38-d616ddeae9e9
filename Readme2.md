
# AI Inference & Observability Platform

This platform is designed to manage AI inference workloads, log inference activity, manage dynamic routing policies, and provide full observability and analytics using a modern distributed system.

---

## 📊 Architecture Diagram (Mermaid)

```mermaid
graph TD
    subgraph Client
        A[User / Client]
    end

    subgraph Gateway
        B[Proxy Gateway]
    end

    subgraph DataPlane
        C[Mock Backend (GPU 1)]
        M[Mock Backend (GPU 2)]
    end

    subgraph PolicyManagement
        D[Policy Manager]
        E[Firestore<br/>(Source of Truth)]
    end

    subgraph Stream
        F[Kafka<br/>(Message Broker)]
        N[Kafka Topic Creator]
    end

    subgraph Cache
        G[Redis<br/>(Cache, Metrics, Pub/Sub)]
    end

    subgraph Processing
        H[Kafka Consumer]
        I[Data Processor]
    end

    subgraph Monitoring
        J[Prometheus<br/>(Metrics Scraper)]
        L[Dashboard API<br/>(Grafana Backend)]
    end

    subgraph Analytics
        K[ClickHouse<br/>(Analytics DB)]
    end

    A -->|HTTP(S) Request| B
    B -->|HTTP(S)| C
    B -->|Redis GET/SET<br/>Kafka Producer (Logs)| G
    B -->|Pub/Sub (Policy/Profile Updates)| G
    G --> D
    D -->|CRUD APIs| E
    H -->|Logs| I
    I -->|Metrics| J
    I -->|Logs| K
    J -->|Scrapes| I
    K --> L
    N --> F
    F --> H
    F --> K
    B --> F
    M -->|HTTP(S)| B
```

---

## 🧠 Components Overview

### 🧑‍💻 Client
- Sends inference requests over HTTP(S).

### 🌐 Proxy Gateway
- Routes requests to inference backends.
- Produces logs to Kafka.
- Uses Redis for:
  - Real-time model profile and policy reads/writes.
  - Pub/Sub updates.

### 🔐 Policy Manager
- Offers CRUD APIs to manage:
  - Inference policies
  - Model profiles
- Syncs data to Firestore (source of truth).

### 🧠 Mock Backend (GPU 1/2)
- Simulated backend for inference processing.

---

## 🔁 Stream & Data Flow

### Redis
- Caches latency, metrics, and inference state.
- Publishes policy/profile update events.

### Kafka
- Transports inference logs.
- Topic creation bootstrapped by a Kafka Topic Creator.

### Kafka Consumer
- Consumes inference logs from Kafka.

### Data Processor
- Sends:
  - Inference metrics → Prometheus
  - Processed logs → ClickHouse

---

## 📊 Analytics & Monitoring

### Prometheus
- Scrapes metrics from the Data Processor.

### ClickHouse
- Stores processed inference logs for analytics.

### Dashboard API (Grafana)
- Visualizes data from ClickHouse and Prometheus.

---

## ⚙️ Technologies Used

| Component        | Tech Stack             |
|------------------|------------------------|
| Gateway/API      | Custom Proxy (Go/Python) |
| Storage          | Firestore              |
| Stream           | Kafka                  |
| Cache/Metrics    | Redis, Prometheus      |
| Analytics DB     | ClickHouse             |
| Visualization    | Grafana                |

---

## 🔄 Future Enhancements

- Policy versioning and audit trails
- Real GPU workload orchestration
- Auto-scaling inference backends
- UI for real-time model/policy monitoring

---

## 📄 License

MIT License

