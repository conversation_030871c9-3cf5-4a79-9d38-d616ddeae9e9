# Use a multi-stage build for a smaller final image

# Stage 1: Builder
# Use a specific Go version to ensure consistent builds
FROM golang:1.24 as builder

# Set the working directory inside the container
WORKDIR /app

# Copy go.mod and go.sum first to leverage Docker cache
# This layer will only be rebuilt if go.mod or go.sum change
COPY go.mod go.sum ./

# Download dependencies declared in go.mod
# This helps cache the go mod download step
RUN go mod download

# Copy all source code into the builder stage
# IMPORTANT: This must be done AFTER go.mod/go.sum and go mod download
# to ensure 'go mod tidy' sees all imports and can properly add/sync modules.
COPY . .

# Ensure all dependencies are correctly listed and downloaded
# This step might add missing direct dependencies to go.mod/go.sum
# if they are imported in the Go source files but not yet in go.mod.
RUN go mod tidy

# Build the Go application
# CGO_ENABLED=0 creates a statically linked binary, which is generally
# preferred for smaller, more portable Docker images.
# -ldflags='-extldflags="-static"' is for fully static linking.
RUN CGO_ENABLED=0 go build -o policy_manager -ldflags='-extldflags="-static"' .

# Stage 2: Final minimal image
# Use a minimal base image like alpine for the final executable
FROM alpine:latest

# Set the working directory in the final image
WORKDIR /app

# Install ca-certificates for HTTPS calls (e.g., to external LLMs)
# This is crucial for your Go application to securely communicate with external APIs (OpenAI, Google, Anthropic).
RUN apk add --no-cache ca-certificates

# Set necessary environment variables. These can be overridden by Kubernetes.
ENV LISTEN_ADDR=:8083 \
    REDIS_ADDR=redis:6379 \
    REDIS_PASSWORD=""

# Copy the compiled binary from the builder stage
COPY --from=builder /app/policy_manager .

# Ensure the binary has execute permissions (good practice, though Go binaries are typically executable by default)
RUN chmod +x /app/policy_manager

# Expose the port the application listens on. This is for documentation and container linking.
EXPOSE 8083

# Define the command to run the application when the container starts.
# ENTRYPOINT is preferred when you want the container to run as an executable.
ENTRYPOINT ["/app/policy_manager"]

