# mock-google.yaml
# Kubernetes YAML definitions for Mock Google Gemini Backend

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-google
  labels:
    app: mock-backend
    backend-id: mock-google # Specific label for this backend instance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-google
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-google
        com.docker.compose.service: mock-google # Crucial for data-processor/Prometheus
    spec:
      containers:
      - name: mock-google
        image: ai-cost-performance-optimizer-mock-google:latest # <--- Placeholder for substitution
        imagePullPolicy: Always
        ports:
        - containerPort: 5004
          name: http-google
        env:
        - name: LISTEN_ADDR
          value: ":5004"
        - name: BACKEND_ID
          value: "mock-google"
        - name: DELAY_MS
          value: "150"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        args: ["mock_google"]
---
apiVersion: v1
kind: Service
metadata:
  name: mock-google # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-google # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5004
    targetPort: 5004
  type: ClusterIP # Use ClusterIP for in-cluster access
