import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Extend dayjs with necessary plugins
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Calculates start and end dates based on a given timeframe.
 * @param {'1h' | '24h' | '7d' | '30d' | '90d' | 'all'} timeframe
 * @returns {{startDate: string, endDate: string}} Dates in ISOString format.
 */
export const getDateRange = (timeframe) => {
  const endDate = dayjs().utc(); // Use UTC for consistency with backend
  let startDate = endDate.subtract(24, 'hour'); // Default for 24h

  switch (timeframe) {
    case '1h':
      startDate = endDate.subtract(1, 'hour');
      break;
    case '24h':
      startDate = endDate.subtract(24, 'hour');
      break;
    case '7d':
      startDate = endDate.subtract(7, 'day');
      break;
    case '30d':
      startDate = endDate.subtract(30, 'day');
      break;
    case '90d':
      startDate = endDate.subtract(90, 'day');
      break;
    case 'all':
      startDate = dayjs('2023-01-01T00:00:00Z').utc(); // A very old date, adjust if your data starts earlier
      break;
    default:
      startDate = endDate.subtract(24, 'hour');
  }

  return {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
  };
};
