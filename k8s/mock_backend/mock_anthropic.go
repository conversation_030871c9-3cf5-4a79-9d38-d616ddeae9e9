package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid" // For generating response IDs
)

// --- Anthropic Messages API Request/Response Structs ---

// AnthropicMessage represents a message in the Anthropic format.
type AnthropicMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"` // For text content
	// Can be `[]map[string]interface{}` for mixed content (text, image)
}

// AnthropicMessagesRequest represents the incoming request body for Anthropic Messages API.
type AnthropicMessagesRequest struct {
	Model       string             `json:"model"`
	Messages    []AnthropicMessage `json:"messages"`
	MaxTokens   int                `json:"max_tokens"` // Required by Anthropic
	Temperature *float64           `json:"temperature,omitempty"`
	// Add other Anthropic fields as needed
}

// AnthropicContentBlock represents a content block in the Anthropic response.
type AnthropicContentBlock struct {
	Type string `json:"type"` // e.g., "text"
	Text string `json:"text"`
}

// AnthropicUsage represents token usage in the Anthropic response.
// This struct matches what the real Anthropic API returns.
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// AnthropicMessagesResponse represents the outgoing response body for Anthropic Messages API.
type AnthropicMessagesResponse struct {
	ID           string                  `json:"id"`
	Type         string                  `json:"type"` // e.g., "message"
	Role         string                  `json:"role"` // e.g., "assistant"
	Content      []AnthropicContentBlock `json:"content"`
	Model        string                  `json:"model"`
	StopReason   *string                 `json:"stop_reason"`   // Can be null
	StopSequence *string                 `json:"stop_sequence"` // Can be null
	Usage        AnthropicUsage          `json:"usage"`         // Populated with estimated token counts
}

// --- Common Helper Functions ---

// countWords is a simple helper to simulate token counting by counting words.
// Ensures at least 1 token if content is not empty.
func countWords(s string) int {
	if s == "" {
		return 0
	}
	words := strings.Fields(s)
	if len(words) == 0 && len(s) > 0 { // For very short strings that have no spaces
		return 1
	}
	return len(words)
}

// --- Handler for Anthropic Mock Backend ---

// handleAnthropicRequest is the handler for incoming requests to the mock Anthropic backend.
func handleAnthropicRequest(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = "N/A (No X-Request-ID header)"
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-anthropic-unknown"
	}

	log.Printf("[%s] [%s] Received request: %s %s from %s", backendID, requestID, r.Method, r.URL.String(), r.RemoteAddr)

	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("[%s] [%s] Error reading request body: %v", backendID, requestID, err)
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var messagesRequest AnthropicMessagesRequest
	err = json.Unmarshal(bodyBytes, &messagesRequest)
	if err != nil {
		log.Printf("[%s] [%s] Error unmarshalling request body: %v. Body: %s", backendID, requestID, err, string(bodyBytes))
		http.Error(w, "Invalid request body format for Anthropic Messages API", http.StatusBadRequest)
		return
	}

	// Extract prompt from messages
	fullPrompt := ""
	for _, msg := range messagesRequest.Messages {
		if msg.Role == "user" {
			fullPrompt += msg.Content + "\n"
		}
	}
	fullPrompt = strings.TrimSpace(fullPrompt)

	// Simulate processing time (latency)
	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 300 // Default delay for Anthropic mock
	}
	time.Sleep(time.Duration(delayMs) * time.Millisecond)
	log.Printf("[%s] [%s] Finished simulated initial delay of %d ms", backendID, requestID, delayMs)

	// Simulate CPU and Memory Usage (as in original mock_backend.go)
	log.Printf("[%s] [%s] Simulating CPU usage for a minimum duration...", backendID, requestID)
	startTimeCPU := time.Now()
	minimumCPUDuration := 2 * time.Second
	i := 0
	for {
		_ = i * i / (i + 1)
		i++
		if time.Since(startTimeCPU) >= minimumCPUDuration {
			break
		}
	}
	log.Printf("[%s] [%s] Finished simulating CPU usage after %s", backendID, requestID, time.Since(startTimeCPU))

	log.Printf("[%s] [%s] Simulating Memory usage...", backendID, requestID)
	memorySizeMB := 100
	tempMemory := make([]byte, memorySizeMB*1024*1024)
	memoryHoldDuration := 2 * time.Second
	time.Sleep(memoryHoldDuration)
	_ = tempMemory
	log.Printf("[%s] [%s] Finished simulating Memory usage after holding %d MB for %s", backendID, requestID, memorySizeMB, memoryHoldDuration)

	// Simulate AI Response and Token Counting
	simulatedResponseContent := fmt.Sprintf("This is a simulated Anthropic Claude response from %s for prompt: '%s'.", backendID, fullPrompt)

	inputTokens := countWords(fullPrompt)
	outputTokens := countWords(simulatedResponseContent)
	if outputTokens == 0 && inputTokens == 0 { // Ensure at least 1 output token if no content, to prevent zero cost
		outputTokens = 1
	}

	// Stop reason can be "end_turn" or "max_tokens" etc.
	stopReason := "end_turn" // Default stop reason

	response := AnthropicMessagesResponse{
		ID:   fmt.Sprintf("msg_%s", uuid.New().String()),
		Type: "message",
		Role: "assistant",
		Content: []AnthropicContentBlock{
			{Type: "text", Text: simulatedResponseContent},
		},
		Model:        messagesRequest.Model, // Reflect the requested model
		StopReason:   &stopReason,
		StopSequence: nil, // Can be populated if the mock supports specific stop sequences
		Usage: AnthropicUsage{ // Populated with estimated token counts
			InputTokens:  inputTokens,
			OutputTokens: outputTokens,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)

	log.Printf("[%s] [%s] Sent response. Input Tokens: %d, Output Tokens: %d", backendID, requestID, inputTokens, outputTokens)
}

func main() {
	listenAddr := os.Getenv("LISTEN_ADDR")
	if listenAddr == "" {
		listenAddr = ":5005" // Default port for mock-anthropic
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-anthropic"
	}

	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 300 // Default delay for Anthropic mock
	}

	log.Printf("Starting Mock AI Backend '%s' (Anthropic Messages API) on %s with simulated initial delay of %d ms", backendID, listenAddr, delayMs)

	http.HandleFunc("/v1/messages", handleAnthropicRequest) // Anthropic Messages API endpoint

	err = http.ListenAndServe(listenAddr, nil)
	if err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
