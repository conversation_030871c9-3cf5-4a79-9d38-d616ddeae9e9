# Use the same Kafka image as the base
FROM confluentinc/cp-kafka:latest

# Set the working directory
WORKDIR /app

# Copy the topic creation script into the container and make it executable
# Using --chmod=0755 to set execute permissions using octal notation
COPY --chmod=0755 create_topic_and_wait.sh .

# Define the command to run when the container starts
# This will execute your topic creation script
CMD ["/app/create_topic_and_wait.sh"]

