import React from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react'; // Importing necessary icons

/**
 * @typedef {Object} ThProps
 * @property {string} column - The data key for the column.
 * @property {string} label - The display label for the column header.
 * @property {string} sortColumn - The currently sorted column.
 * @property {'asc' | 'desc'} sortDirection - The current sort direction.
 * @property {(column: string) => void} handleSort - Function to call when header is clicked for sorting.
 */

/**
 * Table Header component for sortable columns.
 * @param {ThProps} props
 */
export const Th = ({ column, label, sortColumn, sortDirection, handleSort }) => (
  <th
    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
    onClick={() => handleSort(column)}
  >
    <div className="flex items-center space-x-1">
      <span>{label}</span>
      {sortColumn === column && (
        sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />
      )}
    </div>
  </th>
);
