// API endpoints
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
export const DASHBOARD_API_PREFIX = `${API_BASE_URL}/api`;
export const POLICY_MANAGER_API_PREFIX = `${API_BASE_URL}/api`;

// Ensure consistent endpoint naming
export const POLICIES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/policies`;
export const MODEL_PROFILES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/model-profiles`; // Use hyphenated version consistently

// Model Profile Constants
export const MODEL_PROFILE_CAPABILITIES = [
  'text-generation',
  'chat',
  'code-generation',
  'summarization',
  'complex-reasoning',
  'financial-analysis',
  'customer-support',
  'multi-modal',
  'vision',
  'text-classification',
  'long-context',
  'report-generation',
  'FAQ-answering'
];

export const MODEL_PROFILE_PRICING_TIERS = [
  'free',
  'basic',
  'standard',
  'premium',
  'enterprise',
  'custom'
];

export const MODEL_PROFILE_DATA_SENSITIVITY_LEVELS = [
  'low',
  'medium',
  'high'
];

export const MODEL_PROFILE_BACKEND_TYPES = [
  'openai',
  'openai-external',
  'anthropic',
  'anthropic-external',
  'google',
  'google-external',
  'vllm',
  'llama',
  'mistral',
  'custom'
];
