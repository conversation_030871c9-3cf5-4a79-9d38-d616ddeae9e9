# Use a specific, stable Go builder image
FROM golang:1.24 as builder

# Set the working directory inside the container
WORKDIR /app

# Copy the Go module files first (for caching)
# This layer is cached as long as go.mod/go.sum don't change
COPY go.mod go.sum ./

# Download the Go module dependencies
# This caches the dependencies if go.mod/go.sum don't change
RUN go mod download

# Copy the rest of your application source code
COPY . .

# Build the Go application binary
# CGO_ENABLED=0 is important for building statically linked binaries
# for minimal base images like Alpine
# -o dashboard_api specifies the output file name
# -ldflags="-extldflags=-static" ensures static linking for musl libc
RUN CGO_ENABLED=0 go build -o dashboard_api -ldflags='-extldflags="-static"' .

# Use a minimal Alpine Linux image for the final stage
FROM alpine:latest

# Set the working directory in the final image
WORKDIR /app

# Install netcat (useful for init containers or debugging, though not strictly needed by the app itself)
RUN apk update && apk add --no-cache netcat-openbsd

# Copy the built binary from the builder stage to the final image
COPY --from=builder /app/dashboard_api .

# Ensure the binary has execute permissions
RUN chmod +x /app/dashboard_api

# Define the default command to run when the container starts
# This executes your compiled dashboard API application
CMD ["/app/dashboard_api"]

