# Planning Engine Integration Guide

This guide explains how to integrate the Autonomous Task Decomposition & Planning Engine with the existing AI Cost-Performance Optimizer infrastructure.

## Architecture Integration

### 1. Service Communication Flow

```
User Request → Proxy Gateway → Planning Service → AI Optimizer → LLM Backends
                     ↓              ↓              ↓
                Data Processor ← <PERSON><PERSON><PERSON> ← Execution Logs
                     ↓
                ClickHouse (Analytics)
```

### 2. Proxy Gateway Integration

Add planning endpoints to the proxy gateway to provide a unified API:

```go
// Add to k8s/proxy-gateway/main.go

// Planning endpoints
router.HandleFunc("/v1/goals", proxyToPlanningService).Methods("POST", "GET")
router.HandleFunc("/v1/goals/{id}", proxyToPlanningService).Methods("GET", "PUT", "DELETE")
router.HandleFunc("/v1/goals/{id}/plan", proxyToPlanningService).Methods("POST", "GET")
router.HandleFunc("/v1/goals/{id}/execute", proxyToPlanningService).Methods("POST")
router.HandleFunc("/v1/goals/{id}/status", proxyToPlanningService).Methods("GET")

func proxyToPlanningService(w http.ResponseWriter, r *http.Request) {
    planningServiceURL := os.Getenv("PLANNING_SERVICE_URL")
    if planningServiceURL == "" {
        planningServiceURL = "http://planning-service:8080"
    }
    
    // Forward request to planning service
    targetURL := planningServiceURL + r.URL.Path
    if r.URL.RawQuery != "" {
        targetURL += "?" + r.URL.RawQuery
    }
    
    // Create proxy request
    proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
    if err != nil {
        http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
        return
    }
    
    // Copy headers
    for key, values := range r.Header {
        for _, value := range values {
            proxyReq.Header.Add(key, value)
        }
    }
    
    // Make request
    client := &http.Client{Timeout: 60 * time.Second}
    resp, err := client.Do(proxyReq)
    if err != nil {
        http.Error(w, "Planning service unavailable", http.StatusServiceUnavailable)
        return
    }
    defer resp.Body.Close()
    
    // Copy response headers
    for key, values := range resp.Header {
        for _, value := range values {
            w.Header().Add(key, value)
        }
    }
    
    w.WriteHeader(resp.StatusCode)
    io.Copy(w, resp.Body)
}
```

### 3. AI Optimizer Integration

The planning service integrates with the AI Optimizer through the existing `/route` endpoint:

```go
// Planning service calls AI Optimizer for LLM routing
type OptimizationRequest struct {
    Prompt          string `json:"prompt"`
    Model           string `json:"model"`
    UserID          string `json:"user_id"`
    TaskType        string `json:"task_type"`
    PreferredLLMID  string `json:"preferred_llm_id,omitempty"`
}

func (client *AIOptimizerLLMClient) GenerateResponse(ctx context.Context, prompt string, model string, options map[string]interface{}) (string, error) {
    // Create optimization request
    optReq := OptimizationRequest{
        Prompt:   prompt,
        Model:    model,
        TaskType: "planning_llm_call",
    }
    
    // Call AI Optimizer for routing decision
    routeResp, err := client.callAIOptimizer("/route", optReq)
    if err != nil {
        return "", err
    }
    
    // Use the selected backend for the actual LLM call
    return client.callSelectedBackend(routeResp.SelectedBackendURL, prompt, options)
}
```

### 4. Data Processor Integration

Enhance the data processor to handle planning-related logs:

```go
// Add to k8s/data-processor/main.go

type PlanningLog struct {
    GoalID          string    `json:"goal_id"`
    PlanID          string    `json:"plan_id"`
    TaskID          string    `json:"task_id,omitempty"`
    EventType       string    `json:"event_type"` // "goal_created", "plan_generated", "task_executed", etc.
    Status          string    `json:"status"`
    Duration        int64     `json:"duration_ms,omitempty"`
    Cost            float64   `json:"cost,omitempty"`
    Timestamp       time.Time `json:"timestamp"`
    UserID          string    `json:"user_id"`
    Metadata        string    `json:"metadata,omitempty"`
}

func processPlanningLog(logEntry PlanningLog) error {
    // Insert into ClickHouse planning_logs table
    query := `
        INSERT INTO planning_logs (
            goal_id, plan_id, task_id, event_type, status, 
            duration_ms, cost, timestamp, user_id, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    _, err := clickhouseConn.Exec(query,
        logEntry.GoalID,
        logEntry.PlanID,
        logEntry.TaskID,
        logEntry.EventType,
        logEntry.Status,
        logEntry.Duration,
        logEntry.Cost,
        logEntry.Timestamp,
        logEntry.UserID,
        logEntry.Metadata,
    )
    
    return err
}
```

## Database Schema Extensions

### ClickHouse Tables

Add new tables to support planning analytics:

```sql
-- Planning logs table
CREATE TABLE planning_logs (
    goal_id String,
    plan_id String,
    task_id String,
    event_type String,
    status String,
    duration_ms UInt64,
    cost Float64,
    timestamp DateTime,
    user_id String,
    metadata String
) ENGINE = MergeTree()
ORDER BY (timestamp, goal_id)
PARTITION BY toYYYYMM(timestamp);

-- Goal metrics table
CREATE TABLE goal_metrics (
    goal_id String,
    user_id String,
    description String,
    status String,
    priority UInt8,
    created_at DateTime,
    completed_at DateTime,
    total_cost Float64,
    total_duration_ms UInt64,
    task_count UInt32,
    success_rate Float64
) ENGINE = MergeTree()
ORDER BY (created_at, user_id);

-- Task performance table
CREATE TABLE task_performance (
    task_id String,
    plan_id String,
    goal_id String,
    task_type String,
    status String,
    duration_ms UInt64,
    cost Float64,
    retry_count UInt8,
    timestamp DateTime
) ENGINE = MergeTree()
ORDER BY (timestamp, task_type);
```

### Redis Schema

Store planning state in Redis:

```
# Goal state
goal:{goal_id} -> JSON(Goal)

# Plan state  
plan:{plan_id} -> JSON(Plan)

# Execution context
execution:{context_id} -> JSON(ExecutionContext)

# User goals index
user_goals:{user_id} -> SET(goal_ids)

# Active executions
active_executions -> SET(goal_ids)
```

## Deployment Configuration

### 1. Update docker-compose.yml

```yaml
services:
  planning-service:
    build: ./k8s/planning-service
    ports:
      - "8085:8080"
    environment:
      - PORT=8080
      - AI_OPTIMIZER_URL=http://ai-optimizer:8080
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - redis
      - kafka
      - ai-optimizer
    networks:
      - ai-optimizer-network

  proxy-gateway:
    environment:
      - PLANNING_SERVICE_URL=http://planning-service:8080
    depends_on:
      - planning-service
```

### 2. Kubernetes Deployment

```yaml
# Add to existing namespace
apiVersion: v1
kind: ConfigMap
metadata:
  name: planning-service-config
data:
  AI_OPTIMIZER_URL: "http://ai-optimizer:8080"
  REDIS_URL: "redis://redis:6379"
  KAFKA_BROKERS: "kafka:9092"

---
# Update proxy-gateway deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-gateway
spec:
  template:
    spec:
      containers:
      - name: proxy-gateway
        env:
        - name: PLANNING_SERVICE_URL
          value: "http://planning-service:8080"
```

## Monitoring and Observability

### 1. Metrics Collection

Add planning-specific metrics to Prometheus:

```go
// Add to planning service
var (
    goalsCreated = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "planning_goals_created_total",
            Help: "Total number of goals created",
        },
        []string{"user_id", "status"},
    )
    
    planExecutionDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "planning_execution_duration_seconds",
            Help: "Duration of plan execution",
        },
        []string{"goal_id", "status"},
    )
    
    taskExecutionCost = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "planning_task_cost_dollars",
            Help: "Cost of individual task execution",
        },
        []string{"task_type", "status"},
    )
)
```

### 2. Dashboard Integration

Extend the existing dashboard to include planning metrics:

```javascript
// Add to k8s/frontend/src/components/Dashboard.jsx

const PlanningMetrics = () => {
  const [planningData, setPlanningData] = useState(null);
  
  useEffect(() => {
    fetch('/api/planning/metrics')
      .then(response => response.json())
      .then(data => setPlanningData(data));
  }, []);
  
  return (
    <div className="planning-metrics">
      <h3>Autonomous Planning</h3>
      <div className="metrics-grid">
        <MetricCard 
          title="Active Goals" 
          value={planningData?.activeGoals || 0}
          trend={planningData?.goalsTrend}
        />
        <MetricCard 
          title="Avg Execution Time" 
          value={planningData?.avgExecutionTime || "0s"}
          trend={planningData?.timeTrend}
        />
        <MetricCard 
          title="Success Rate" 
          value={`${planningData?.successRate || 0}%`}
          trend={planningData?.successTrend}
        />
        <MetricCard 
          title="Cost Savings" 
          value={`$${planningData?.costSavings || 0}`}
          trend={planningData?.costTrend}
        />
      </div>
    </div>
  );
};
```

## Testing and Validation

### 1. Integration Tests

```bash
#!/bin/bash
# test_planning_integration.sh

# Test goal creation
GOAL_ID=$(curl -s -X POST http://localhost:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test-user" \
  -d '{"description": "Test goal for integration"}' | jq -r '.id')

echo "Created goal: $GOAL_ID"

# Test plan generation
curl -X POST http://localhost:8080/v1/goals/$GOAL_ID/plan \
  -H "X-User-ID: test-user"

# Test execution
curl -X POST http://localhost:8080/v1/goals/$GOAL_ID/execute \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test-user" \
  -d '{"max_concurrency": 2}'

# Monitor status
while true; do
  STATUS=$(curl -s http://localhost:8080/v1/goals/$GOAL_ID/status | jq -r '.status')
  echo "Status: $STATUS"
  if [[ "$STATUS" == "completed" || "$STATUS" == "failed" ]]; then
    break
  fi
  sleep 5
done

# Get results
curl -s http://localhost:8080/v1/goals/$GOAL_ID/results | jq '.'
```

### 2. Performance Testing

```bash
# Load test with multiple concurrent goals
for i in {1..10}; do
  curl -X POST http://localhost:8080/v1/goals \
    -H "Content-Type: application/json" \
    -H "X-User-ID: load-test-$i" \
    -d "{\"description\": \"Load test goal $i\"}" &
done
wait
```

## Security Considerations

1. **Authentication**: Integrate with existing RBAC system
2. **Authorization**: Respect user permissions for goal creation and execution
3. **Data Privacy**: Ensure sensitive data in goals is properly protected
4. **Resource Limits**: Implement quotas to prevent resource abuse
5. **Audit Logging**: Log all planning activities for compliance

## Migration Strategy

1. **Phase 1**: Deploy planning service alongside existing infrastructure
2. **Phase 2**: Update proxy gateway to route planning requests
3. **Phase 3**: Integrate with data processor for analytics
4. **Phase 4**: Update dashboard with planning metrics
5. **Phase 5**: Enable advanced features (adaptive re-planning, custom templates)

This integration maintains backward compatibility while adding powerful agentic capabilities to the AI Cost-Performance Optimizer.
