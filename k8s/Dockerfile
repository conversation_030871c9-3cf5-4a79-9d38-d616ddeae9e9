# k8s/redis-populator-job/Dockerfile
FROM python:3.9-slim-buster

# Set the working directory in the container
WORKDIR /app

# Copy the Python script and its requirements file
# Assuming populate_redis.py and any requirements.txt are in the same directory as this Dockerfile
COPY populate_redis.py .

# If populate_redis.py has any external Python dependencies (e.g., 'redis', 'json', 'time', 'uuid')
# (which it does, for 'redis' and 'uuid'), you should have a requirements.txt file.
# If you don't have requirements.txt, you can install them directly:
# RUN pip install redis==3.5.3 # Or a newer version of redis-py compatible with Python 3.9
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Command to run the application (this will be overridden by the Kubernetes Job command)
# This CMD is mostly for documentation/default run if not specified by Kubernetes.
# The Kubernetes Job will use `command: ["python3", "/app/populate_redis.py"]`
CMD ["python3", "populate_redis.py"]

