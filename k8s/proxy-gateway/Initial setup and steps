

go mod init <your_module_name>
go get github.com/google/uuid gopkg.in/yaml.v2

go run main.go

BACKEND_ID="selfhosted-llama7b-gpu1" LISTEN_ADDR=":5001" DELAY_MS="100" go run mock_backend.go
# Or if you built it:
# BACKEND_ID="selfhosted-llama7b-gpu1" LISTEN_ADDR=":5001" DELAY_MS="100" ./mock_backend


BACKEND_ID="selfhosted-llama7b-gpu2" LISTEN_ADDR=":5002" DELAY_MS="150" go run mock_backend.go
# Or if you built it:
# BACKEND_ID="selfhosted-llama7b-gpu2" LISTEN_ADDR=":5002" DELAY_MS="150" ./mock_backend


curl -X POST -H "Content-Type: application/json" -d '{"prompt": "hello"}' http://localhost:8080/predict