import React from 'react';
import { AlertCircle, CheckCircle, XCircle, Info as InfoIcon } from 'lucide-react';

/**
 * @typedef {Object} ModalProps
 * @property {boolean} isOpen - Whether the modal is open.
 * @property {() => void} onClose - Function to call when the modal is closed.
 * @property {React.ReactNode} children - Content of the modal.
 * @property {string} title - Title displayed at the top of the modal.
 */

/**
 * Generic Modal component.
 * @param {ModalProps} props
 */
export const Modal = ({ isOpen, onClose, children, title }) => {
  if (!isOpen) return null; // Don't render if not open

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 p-4 font-sans">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-auto overflow-hidden animate-fade-in-up"> {/* Added fade-in animation */}
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-2xl font-semibold text-gray-800">{title}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 transition-colors duration-200" title="Close">
            <XCircle size={24} />
          </button>
        </div>
        <div className="p-6 max-h-[70vh] overflow-y-auto custom-scrollbar"> {/* Added custom-scrollbar class for styling */}
          {children}
        </div>
      </div>
      {/* Tailwind CSS for custom-scrollbar and animations (often in index.css or global.css) */}
      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #555;
        }
        @keyframes fadeInMoveUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fadeInMoveUp 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

/**
 * @typedef {Object} ConfirmModalProps
 * @property {boolean} isOpen - Whether the confirmation modal is open.
 * @property {string} message - The message to display in the modal.
 * @property {() => void} onConfirm - Function to call when the user confirms.
 * @property {() => void} onCancel - Function to call when the user cancels.
 */

/**
 * Confirmation Modal component.
 * @param {ConfirmModalProps} props
 */
export const ConfirmModal = ({ isOpen, message, onConfirm, onCancel }) => {
  return (
    <Modal isOpen={isOpen} onClose={onCancel} title="Confirm Action">
      <div className="text-gray-700 text-lg mb-6">{message}</div>
      <div className="flex justify-end space-x-3">
        <button
          onClick={onCancel}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-all duration-200 shadow-sm"
        >
          Cancel
        </button>
        <button
          onClick={onConfirm} // onConfirm already handles modal closing in App.jsx
          className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow-sm"
        >
          Confirm
        </button>
      </div>
    </Modal>
  );
};

/**
 * @typedef {Object} NotificationProps
 * @property {string} message - The notification message.
 * @property {'success' | 'error' | 'warning'} type - Type of notification for styling.
 * @property {() => void} onClose - Function to call to close the notification.
 */

/**
 * Notification component for success/error messages.
 * @param {NotificationProps} props
 */
export const Notification = ({ notification, onClose }) => {
  // Add safety check at the beginning
  if (!notification || !notification.message) {
    return null;
  }

  // Ensure message is a string
  const message = String(notification.message || '');
  
  // Determine the icon, background color, and text color based on the notification type
  let Icon = InfoIcon;
  let bgColor = 'bg-blue-100';
  let textColor = 'text-blue-800';
  
  if (notification.type === 'success') {
    Icon = CheckCircle;
    bgColor = 'bg-green-100';
    textColor = 'text-green-800';
  } else if (notification.type === 'error') {
    Icon = XCircle;
    bgColor = 'bg-red-100';
    textColor = 'text-red-800';
  } else if (notification.type === 'warning') {
    Icon = AlertCircle;
    bgColor = 'bg-yellow-100';
    textColor = 'text-yellow-800';
  }
  
  return (
    <div className={`fixed top-4 right-4 p-4 rounded-md shadow-md ${bgColor} ${textColor} animate-fade-in-up z-50`}>
      <div className="flex items-center">
        <Icon size={20} className="mr-2" />
        <span>{message}</span>
        <button onClick={onClose} className="ml-4 text-gray-500 hover:text-gray-700">×</button>
      </div>
    </div>
  );
};
