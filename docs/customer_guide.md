# AI Cost-Performance Optimizer: Customer Guide

This guide provides comprehensive instructions for customers to integrate and use the AI Cost-Performance Optimizer service to intelligently route LLM requests and optimize costs.

## Getting Started

### Account Setup

1. **Sign Up and Create Account**
   - Navigate to our customer portal at [https://portal.ai-optimizer.com](https://portal.ai-optimizer.com)
   - Complete the registration form with your organization details
   - Verify your email address to activate your account
   - Set up your organization and invite team members with appropriate roles

2. **Generate API Keys**
   - In the portal, navigate to "Settings" → "API Keys"
   - Create a new API key with an appropriate description
   - Store this key securely as it will be used for all SDK and API integrations
   - Set appropriate permissions for each key (read-only, read-write, admin)

### Integration Options

Choose the integration method that best fits your infrastructure:

#### Option A: Proxy Integration (Recommended)

The proxy integration routes all your LLM requests through our optimization service:

1. **Update Endpoint URLs**
   - Replace your current LLM provider endpoints with our proxy endpoint
   - Original: `https://api.openai.com/v1/chat/completions`
   - New: `https://proxy.ai-optimizer.com/v1/chat/completions`

2. **Add Authentication Header**
   ```
   X-API-Key: YOUR_API_KEY_HERE
   ```

3. **Request Modification (Optional)**
   - To specify routing preferences, add the header:
   ```
   X-Preferred-Backend-ID: model-name
   ```

#### Option B: SDK Integration

For greater control and detailed metrics:

1. **Install the SDK**
   ```bash
   # Python
   pip install ai-optimizer-sdk

   # Node.js
   npm install @ai-optimizer/sdk

   # Go
   go get github.com/ai-optimizer/sdk
   ```

2. **Initialize the SDK**
   ```python
   # Python example
   from ai_optimizer import OptimizerClient

   optimizer = OptimizerClient(
       api_key="YOUR_API_KEY_HERE",
       service_name="my-service-name"
   )

   # Create a wrapped client for any supported provider
   openai_client = optimizer.wrap_openai()
   anthropic_client = optimizer.wrap_anthropic()
   ```

3. **Make Optimized Requests**
   ```python
   # The SDK handles all optimization logic
   response = openai_client.chat.completions.create(
       model="gpt-4-turbo",
       messages=[
           {"role": "user", "content": "Explain quantum computing"}
       ]
   )
   ```

#### Option C: API-Based Integration

For custom implementations:

1. **Prepare Request Data**
   - Format your inference requests according to our API specification
   - Include all necessary metadata for proper routing decisions

2. **Make API Calls**
   ```bash
   curl -X POST "https://api.ai-optimizer.com/v1/optimize" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: YOUR_API_KEY_HERE" \
     -d '{
       "prompt": "Explain quantum computing",
       "task_type": "educational",
       "models": ["gpt-4", "claude-3-opus", "gemini-pro"],
       "optimization_priority": "balanced"
     }'
   ```

## Configuration

### Model Profiles

Configure profiles for each LLM you want to use:

1. **Access Model Profiles**
   - Navigate to "Settings" → "Model Profiles" in the dashboard

2. **Create/Update Profiles**
   - Define each model's capabilities, costs, and performance characteristics:
     - **Name**: Unique identifier for the model
     - **Provider**: OpenAI, Google, Anthropic, or custom
     - **Capabilities**: Select from code generation, summarization, factual queries, etc.
     - **Cost Settings**: Input and output token costs
     - **Performance Metrics**: Typical latency and throughput
     - **Status**: Active or inactive

### Routing Policies

Create policies to define how requests are routed:

1. **Access Routing Policies**
   - Navigate to "Policies" → "Routing Rules" in the dashboard

2. **Create New Policy**
   - Define criteria and actions:
     - **Name & Description**: Clear identifier for the policy
     - **Criteria**: Task type, user roles, data sensitivity, etc.
     - **Action**: Route, optimize, block
     - **Target Backend**: For direct routing policies
     - **Priority**: Numerical priority (higher numbers take precedence)

3. **Example Policies**
   - Route code tasks to CodeLLama
   - Use cost optimization for factual queries
   - Direct creative writing to Claude
   - Apply user preferences where specified

## Monitoring & Analytics

### Dashboard Overview

1. **Access Dashboard**
   - Log in to [https://portal.ai-optimizer.com](https://portal.ai-optimizer.com)
   - View the main dashboard for key metrics

2. **Key Metrics**
   - **Cost Savings**: Comparison with direct provider usage
   - **Request Volume**: Tracking of daily/weekly/monthly usage
   - **Response Times**: Average and percentile latencies
   - **Token Usage**: Input and output token counts by model
   - **Cache Hit Rate**: Percentage of requests served from cache

### Usage Reports

1. **Generate Reports**
   - Navigate to "Reports" → "Usage Reports"
   - Select date range and filters
   - Generate detailed reports for cost analysis

2. **Export Options**
   - Download as CSV, Excel, or PDF
   - Schedule automated report delivery

## Advanced Features

### Conversation Management

Track and optimize multi-turn conversations:

1. **Enable Conversation Tracking**
   ```python
   conversation = optimizer.create_conversation(
       user_id="user-123",
       initial_prompt="I need help with a project"
   )
   
   # First turn
   response1 = conversation.send_message("Can you explain neural networks?")
   
   # Follow-up turn
   response2 = conversation.send_message("How do they compare to other ML methods?")
   ```

2. **Access Conversation History**
   - View complete conversation logs in the dashboard
   - Analyze token usage across conversation turns

### Custom Task Classification

Train custom classifiers for your specific use cases:

1. **Access Classifier Settings**
   - Navigate to "Settings" → "Task Classification"

2. **Create Custom Classifier**
   - Provide sample prompts for each task category
   - Train and test the classifier
   - Deploy to production when satisfied with accuracy

### Response Caching

Configure caching for appropriate request types:

1. **Access Cache Settings**
   - Navigate to "Settings" → "Response Cache"

2. **Configure Cache Rules**
   - **Enable/Disable**: Toggle caching functionality
   - **TTL**: Set time-to-live for cached responses
   - **Exclusions**: Define patterns or task types to exclude from caching
   - **Namespace Settings**: Configure isolation between teams/projects

## Security & Compliance

### Data Privacy

1. **Data Retention Settings**
   - Navigate to "Settings" → "Data Privacy"
   - Configure retention periods for different data types
   - Set up automatic data purging schedules

2. **PII Handling**
   - Enable/disable PII detection
   - Configure automatic redaction of sensitive information

### Access Control

1. **User Management**
   - Navigate to "Settings" → "Users & Permissions"
   - Create user accounts with appropriate role assignments
   - Configure SSO integration if needed

2. **Role Definitions**
   - **Admin**: Full system access
   - **Manager**: Policy configuration and reporting
   - **Developer**: API keys and integration management
   - **Analyst**: Read-only access to dashboards and reports

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify API key validity and permissions
   - Check for correct header formatting in requests

2. **Unexpected Routing**
   - Review policy priority settings
   - Check task classification results
   - Verify model status (active/inactive)

3. **Performance Issues**
   - Monitor backend provider status
   - Check for rate limiting or quota exhaustion
   - Review network connectivity between your services and our proxy

### Support Channels

1. **Documentation**
   - Access comprehensive documentation at [https://docs.ai-optimizer.com](https://docs.ai-optimizer.com)

2. **Technical Support**
   - Email: <EMAIL>
   - Chat: Available in portal during business hours
   - Priority Support: Available with Enterprise plans

## Reference Documentation

For more detailed information, refer to these resources:

- [System Architecture](architecture.md)
- [Cost Model & Optimization](cost_model.md)
- [Routing Examples](routing_examples.md)
- [Setup Guide](setup_guide.md) (for self-hosted deployments)

## Cache Analytics

The dashboard provides cache analytics to track the performance of the cache.

1.  **Access Cache Analytics**

    - Navigate to "Reports" → "Cache Analytics" in the dashboard

2.  **Key Metrics**

    -   **Cache Hits**: Number of requests served from the cache.
    -   **Cache Misses**: Number of requests not served from the cache.
    -   **Semantic Cache Hits**: Number of requests served from the semantic cache.
    -   **Tokens Saved**: Number of tokens saved by serving requests from the cache.
    -   **Cost Saved**: Cost saved by serving requests from the cache.
    -   **Average Latency**: Average latency of requests served from the cache.