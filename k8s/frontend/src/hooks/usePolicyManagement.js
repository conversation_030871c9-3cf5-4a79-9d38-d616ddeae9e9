import { useState, useCallback } from 'react';
import useApi from './useApi'; // Changed from named import to default import
import { POLICY_MANAGER_API_PREFIX } from '../utils/constants';

/**
 * @typedef {Object} PolicyManagementResult
 * @property {(policy: Policy) => Promise<void>} handleCreatePolicy
 * @property {(policy: Policy) => Promise<void>} handleUpdatePolicy
 * @property {(policyId: string) => Promise<void>} handleDeletePolicy
 * @property {(policyData: Policy) => Object.<string, string>} validatePolicyForm
 * @property {boolean} loading
 * @property {string | null} error
 */

/**
 * Custom hook for managing API Policies.
 * @param {() => Promise<void>} refetchPolicies - Function to re-fetch policies after a CUD operation.
 * @param {(message: string, type?: 'success' | 'error' | 'warning') => void} showNotification - Function to display notifications.
 * @param {(errors: Object.<string, string>) => void} setFormErrors - Function to set form validation errors.
 * @returns {PolicyManagementResult}
 */
export const usePolicyManagement = (refetchPolicies, showNotification, setFormErrors) => {
  const { callApi, apiLoading: loading, apiError: error } = useApi();

  /**
   * Validates a policy form data.
   * @param {Policy} policyData
   * @returns {Object.<string, string>} Validation errors.
   */
  const validatePolicyForm = useCallback((policyData) => {
    const errors = {};
    if (!policyData.name) errors.name = "Name is required.";
    if (!policyData.action) errors.action = "Action is required.";
    if ((policyData.action === 'ROUTE' || policyData.action === 'OPTIMIZE') && !policyData.backend_id) {
      errors.backend_id = "LLM ID is required for ROUTE/OPTIMIZE actions.";
    }
    if (isNaN(parseInt(policyData.priority, 10))) errors.priority = "Priority must be a number.";
    setFormErrors(errors); // Update form errors state
    return errors;
  }, [setFormErrors]);


  /**
   * Handles creating a new policy.
   * @param {Policy} newPolicy
   */
  const handleCreatePolicy = useCallback(async (newPolicy) => {
    const validationErrors = validatePolicyForm(newPolicy);
    if (Object.keys(validationErrors).length > 0) {
      showNotification('Please correct the errors in the policy form.', 'error');
      return;
    }
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/policies`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newPolicy)
      });
      showNotification('Policy created successfully!', 'success');
      refetchPolicies(); // Refresh the list
      return true; // Indicate success
    } catch (err) {
      console.error("Failed to create policy:", err);
      // Notification handled by useApi
      return false; // Indicate failure
    }
  }, [callApi, refetchPolicies, showNotification, validatePolicyForm]);

  /**
   * Handles updating an existing policy.
   * @param {Policy} updatedPolicy
   */
  const handleUpdatePolicy = useCallback(async (updatedPolicy) => {
    const validationErrors = validatePolicyForm(updatedPolicy);
    if (Object.keys(validationErrors).length > 0) {
      showNotification('Please correct the errors in the policy form.', 'error');
      return;
    }
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/policies/${updatedPolicy.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedPolicy)
      });
      showNotification('Policy updated successfully!', 'success');
      refetchPolicies(); // Refresh the list
      return true;
    } catch (err) {
      console.error("Failed to update policy:", err);
      // Notification handled by useApi
      return false;
    }
  }, [callApi, refetchPolicies, showNotification, validatePolicyForm]);

  /**
   * Handles deleting a policy.
   * @param {string} policyId
   */
  const handleDeletePolicy = useCallback(async (policyId) => {
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/policies/${policyId}`, {
        method: 'DELETE'
      });
      showNotification('Policy deleted successfully!', 'success');
      refetchPolicies(); // Refresh the list
      return true;
    } catch (err) {
      console.error("Failed to delete policy:", err);
      // Notification handled by useApi
      return false;
    }
  }, [callApi, refetchPolicies, showNotification]);

  return {
    loading,
    error,
    handleCreatePolicy,
    handleUpdatePolicy,
    handleDeletePolicy,
    validatePolicyForm,
  };
};
