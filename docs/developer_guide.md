# AI Cost-Performance Optimizer: Developer Guide

This guide provides developers with detailed instructions for integrating applications with the AI Cost-Performance Optimizer and its new Autonomous Task Decomposition & Planning Engine.

## Integration Overview

The AI Cost-Performance Optimizer provides two main integration approaches:

1. **Direct LLM Integration**: Send individual LLM requests through our intelligent Proxy Gateway for cost optimization and routing
2. **Agentic Workflow Integration**: Submit high-level goals to our Planning Engine for autonomous multi-step task execution

Both approaches leverage the same underlying infrastructure for cost optimization, policy enforcement, and performance monitoring.

## Getting Started

### Core Endpoints

The system provides multiple integration points depending on your use case:

**Direct LLM API Endpoint:**
```
POST https://scale-llm.com/v1/chat/completions
```

**Autonomous Planning API Endpoints:**
```
POST https://scale-llm.com/v1/goals              # Create goal
POST https://scale-llm.com/v1/goals/{id}/plan    # Generate plan
POST https://scale-llm.com/v1/goals/{id}/execute # Execute plan
GET  https://scale-llm.com/v1/goals/{id}/status  # Monitor progress
```

## Integration Approaches

### Approach 1: Direct LLM Integration

For traditional LLM usage, send requests to the Proxy Gateway using the standard Chat Completion API format:

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Your prompt here"}
  ],
  "temperature": 0.7,
  "max_tokens": 150
}
```

### Approach 2: Autonomous Goal-Based Integration

For complex, multi-step workflows, submit high-level goals to the Planning Engine:

```json
{
  "description": "Analyze customer feedback from the last month and generate actionable insights",
  "success_criteria": [
    {
      "description": "Process at least 100 feedback entries",
      "metric": "feedback_count",
      "target": 100,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    }
  ],
  "constraints": [
    {
      "type": "time",
      "description": "Complete within 2 hours",
      "limit": "2h",
      "operator": "<=",
      "severity": "hard"
    }
  ],
  "priority": 8
}
```

## Key Features

### Intelligent LLM Routing

Based on configured policies, the proxy automatically selects the most appropriate backend LLM for each request. Routing decisions can be based on:
- Prompt content and complexity
- User roles and permissions
- Token length and cost constraints
- Performance/latency requirements

### Autonomous Task Decomposition

The Planning Engine automatically breaks down complex goals into executable tasks:
- **Natural Language Processing**: Parse goals from plain English descriptions
- **Intelligent Planning**: Use LLM-assisted decomposition with task templates
- **Dependency Management**: Handle complex task relationships and execution order
- **Resource Optimization**: Estimate and manage computational resources

### Cost & Performance Optimization

Unified optimization across both individual requests and multi-step workflows:
- `OPTIMIZE_LATENCY`: Prioritize response speed
- `OPTIMIZE_COST`: Prioritize lower cost
- `OPTIMIZE_BALANCED`: Balance performance and cost
- **Workflow-level optimization**: Optimize entire goal execution for cost and time

### Execution Orchestration

For goal-based workflows, the system provides:
- **Parallel Execution**: Run independent tasks concurrently
- **Failure Handling**: Automatic retry with exponential backoff
- **Progress Tracking**: Real-time monitoring of task completion
- **State Management**: Persistent context across multi-step executions

### Conversational Context Management

For multi-turn conversations, the proxy maintains context automatically:
1. For the first turn, omit `conversation_id` - the proxy will generate one
2. Include this `conversation_id` in subsequent requests
3. The proxy automatically retrieves prior messages before sending to the chosen LLM

### Policy Application

Benefit from centrally defined policies without implementing complex routing logic in your application. Policies can be configured through the admin dashboard and apply to both direct LLM calls and autonomous workflows.

## API Usage Examples

### Direct LLM API Examples

#### Basic Chat Completion

The proxy will automatically select the best LLM based on configured policies:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

### Conversational Turn 1 (New Conversation)

For the first turn, omit `conversation_id`:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
```

The response will contain a `conversation_id` in the payload.

### Conversational Turn 2 (Continuing the Conversation)

Use the `conversation_id` from the first turn:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID_FROM_PREVIOUS_RESPONSE>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'
```

### Manual Model Override

Override automatic routing by specifying a preferred LLM ID:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gpt-4-turbo-us-east" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'
```

**Note:** The `model` field in the request body still acts as a hint or fallback if no policy matches or if the preferred LLM is unavailable.

### Autonomous Planning API Examples

#### Create and Execute a Goal

**Step 1: Create a Goal**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "description": "Analyze customer support tickets from the last week and generate a comprehensive report with recommendations",
    "success_criteria": [
      {
        "description": "Process at least 100 tickets",
        "metric": "ticket_count",
        "target": 100,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete within 2 hours",
        "limit": "2h",
        "operator": "<=",
        "severity": "hard"
      }
    ],
    "priority": 8
  }'
```

**Step 2: Generate Execution Plan**
```bash
# Use the goal_id from step 1
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/plan \
  -H "X-User-ID: your-user-id"
```

**Step 3: Execute the Plan**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/execute \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "max_concurrency": 3,
    "timeout": "30m"
  }'
```

**Step 4: Monitor Progress**
```bash
# Check execution status
curl https://scale-llm.com/v1/goals/{goal_id}/status \
  -H "X-User-ID: your-user-id"

# Get detailed task progress
curl https://scale-llm.com/v1/goals/{goal_id}/tasks \
  -H "X-User-ID: your-user-id"

# Get final results
curl https://scale-llm.com/v1/goals/{goal_id}/results \
  -H "X-User-ID: your-user-id"
```

#### Common Goal Types

**Data Analysis Workflow:**
```json
{
  "description": "Perform comprehensive sales analysis for Q4 and predict Q1 performance",
  "success_criteria": [
    {
      "description": "Analyze at least 6 months of data",
      "metric": "data_coverage_months",
      "target": 6,
      "operator": ">=",
      "weight": 0.9,
      "required": true
    }
  ],
  "context": {
    "data_sources": ["sales_db", "customer_db"],
    "analysis_period": "Q4_2023",
    "prediction_horizon": "Q1_2024"
  }
}
```

**Content Creation Workflow:**
```json
{
  "description": "Create a complete marketing campaign for our new product launch",
  "success_criteria": [
    {
      "description": "Generate 5 blog posts",
      "metric": "blog_posts_count",
      "target": 5,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    },
    {
      "description": "Create 20 social media posts",
      "metric": "social_posts_count",
      "target": 20,
      "operator": ">=",
      "weight": 0.7,
      "required": true
    }
  ],
  "context": {
    "product_name": "AI Assistant Pro",
    "target_audience": "business professionals",
    "launch_date": "2024-03-15"
  }
}
```

## Client Libraries

For improved developer experience, we provide client libraries that abstract the HTTP calls and handle both direct LLM integration and autonomous planning workflows.

### Python Client Library

#### Direct LLM Client

```python
import requests
import json
from typing import List, Dict, Any, Optional

class LLMOptimizerClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.chat_completions_endpoint = f"{self.base_url}/v1/chat/completions"

    def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        conversation_id: Optional[str] = None,
        preferred_llm_id: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Sends a chat completion request to the LLM Optimizer Proxy.

        Args:
            messages (List[Dict[str, str]]): List of message objects.
            model (str): The model name (used as a hint/fallback by the proxy).
            temperature (float): Sampling temperature.
            max_tokens (Optional[int]): Maximum number of tokens to generate.
            conversation_id (Optional[str]): ID to continue a conversation.
            preferred_llm_id (Optional[str]): Forces routing to a specific LLM.
            **kwargs: Additional parameters to pass to the LLM API.

        Returns:
            Dict[str, Any]: The response payload from the LLM.
        """
        payload = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            **kwargs
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if conversation_id:
            payload["conversation_id"] = conversation_id

        headers = {"Content-Type": "application/json"}
        if preferred_llm_id:
            headers["X-Preferred-LLM-ID"] = preferred_llm_id

        try:
            response = requests.post(
                self.chat_completions_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            raise
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            raise
        except Exception as err:
            print(f"An unexpected error occurred: {err}")
            raise

#### Autonomous Planning Client

```python
class PlanningClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.goals_endpoint = f"{self.base_url}/v1/goals"

    def create_goal(
        self,
        description: str,
        user_id: str,
        success_criteria: Optional[List[Dict]] = None,
        constraints: Optional[List[Dict]] = None,
        context: Optional[Dict] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """Create a new goal for autonomous execution."""
        payload = {
            "description": description,
            "priority": priority
        }
        if success_criteria:
            payload["success_criteria"] = success_criteria
        if constraints:
            payload["constraints"] = constraints
        if context:
            payload["context"] = context

        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }

        response = requests.post(self.goals_endpoint, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()

    def create_plan(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Generate an execution plan for a goal."""
        headers = {"X-User-ID": user_id}
        response = requests.post(f"{self.goals_endpoint}/{goal_id}/plan", headers=headers)
        response.raise_for_status()
        return response.json()

    def execute_goal(
        self,
        goal_id: str,
        user_id: str,
        max_concurrency: int = 3,
        timeout: str = "30m"
    ) -> Dict[str, Any]:
        """Execute a goal's plan."""
        payload = {
            "max_concurrency": max_concurrency,
            "timeout": timeout
        }
        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }
        response = requests.post(
            f"{self.goals_endpoint}/{goal_id}/execute",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def get_status(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution status."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/status", headers=headers)
        response.raise_for_status()
        return response.json()

    def get_results(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution results."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/results", headers=headers)
        response.raise_for_status()
        return response.json()
```

### Usage Examples

#### Direct LLM Usage

```python
# Replace with your actual deployed frontend IP
OPTIMIZER_BASE_URL = "https://scale-llm.com"  # Or your actual IP/hostname

llm_client = LLMOptimizerClient(OPTIMIZER_BASE_URL)

# Example 1: Basic Request (Proxy chooses LLM)
try:
    response_basic = llm_client.create_chat_completion(
        messages=[{"role": "user", "content": "What is the capital of France?"}],
        model="gpt-3.5-turbo"  # Hint model
    )
    print("Basic Request Response:", json.dumps(response_basic, indent=2))
except Exception as e:
    print(f"Error during basic request: {e}")
```

#### Autonomous Planning Usage

```python
planning_client = PlanningClient(OPTIMIZER_BASE_URL)

# Example: Customer Service Analysis Workflow
try:
    # Step 1: Create goal
    goal = planning_client.create_goal(
        description="Analyze customer support tickets from last week and generate insights",
        user_id="analyst-team",
        success_criteria=[
            {
                "description": "Process at least 100 tickets",
                "metric": "ticket_count",
                "target": 100,
                "operator": ">=",
                "weight": 0.8,
                "required": True
            }
        ],
        constraints=[
            {
                "type": "time",
                "description": "Complete within 2 hours",
                "limit": "2h",
                "operator": "<=",
                "severity": "hard"
            }
        ],
        context={
            "data_source": "support_tickets_db",
            "time_range": "last_7_days"
        },
        priority=8
    )

    goal_id = goal["id"]
    print(f"Created goal: {goal_id}")

    # Step 2: Generate plan
    plan = planning_client.create_plan(goal_id, "analyst-team")
    print(f"Generated plan with {len(plan['tasks'])} tasks")

    # Step 3: Execute
    execution = planning_client.execute_goal(goal_id, "analyst-team", max_concurrency=3)
    print("Execution started")

    # Step 4: Monitor progress
    import time
    while True:
        status = planning_client.get_status(goal_id, "analyst-team")
        print(f"Status: {status['status']}")

        if status["status"] in ["completed", "failed"]:
            break
        time.sleep(10)

    # Step 5: Get results
    if status["status"] == "completed":
        results = planning_client.get_results(goal_id, "analyst-team")
        print("Results:", json.dumps(results, indent=2))

except Exception as e:
    print(f"Error during autonomous workflow: {e}")
```

### Other Available Client Libraries

- **JavaScript/TypeScript**: For Node.js backends or browser-based applications
- **Go**: Native integration for Go services
- **Ruby, Java, C#**: Available based on your development ecosystem

## Benefits of Integration

### "Set it and Forget it" Optimization
Write code once to hit our proxy, and the backend infrastructure automatically handles cost and performance decisions for both individual requests and complex workflows.

### Autonomous Workflow Execution
Submit high-level goals and let the system automatically:
- Break down complex objectives into executable tasks
- Orchestrate multi-step workflows with proper dependencies
- Handle failures and retries intelligently
- Optimize costs across the entire workflow

### Future-Proofing
As new LLMs emerge or task execution capabilities improve, we can update backend policies and planning algorithms without requiring any code changes in your applications.

### Simplified AI Integration
- No need to manage API keys for multiple LLM providers
- No complex workflow orchestration logic in your application
- No manual task decomposition or dependency management
- Unified interface for both simple requests and complex workflows

### Centralized Observability
Use our dashboard to monitor:
- Individual LLM request performance and costs
- Goal execution progress and success rates
- Task-level metrics and resource utilization
- Workflow optimization opportunities

### Reduced Development Time
- Less boilerplate code for LLM integration
- No need to build custom workflow orchestration
- Automatic handling of complex multi-step AI processes
- Built-in retry logic and error handling

## Next Steps

- Review the [Setup Guide](setup_guide.md) for installation details
- Explore [Routing Examples](routing_examples.md) for policy configurations
- Learn about the [Cost Model](cost_model.md) for optimization strategies
- Understand the [System Architecture](architecture.md) for integration planning
- Check out [Planning Engine Examples](planning_engine_examples.md) for autonomous workflow patterns
- Read the [Planning Engine Integration Guide](planning_engine_integration.md) for advanced setup

## Prompt Management API

The Policy Manager provides API endpoints for managing LLM prompts.

**Create a Prompt**
```
POST /prompts
```
**Get All Prompts**
```
GET /prompts
```
**Get a Specific Prompt**
```
GET /prompts/{id}
```
**Update a Prompt**
```
PUT /prompts/{id}
```
**Delete a Prompt**
```
DELETE /prompts/{id}
```

For more details, refer to the Policy Manager documentation.

## Synthetic Data Generation

The data processor includes a synthetic data generation loop that periodically generates synthetic LLM inference data. This data is used for testing and evaluation purposes.

The synthetic data generation loop is configured using the following environment variables:

*   `SYNTHETIC_DATA_GENERATION_MODEL_ID`: Model ID for synthetic data generation.
*   `FEEDBACK_LOOP_INTERVAL`: Interval between synthetic data generation runs.

## Evaluation Results API

The Dashboard API provides endpoints for retrieving evaluation results and curated data.

**Get Evaluation Results**
```
GET /evaluation-results
```
**Get Curated Data**
```
GET /curated-data
```

For more details, refer to the Dashboard API documentation.