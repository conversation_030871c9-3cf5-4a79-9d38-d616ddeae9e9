# proxy-gateway-job-reader-rbac.yaml
# Kubernetes RBAC definitions for the Proxy Gateway's initContainer to read Job status

apiVersion: v1
kind: ServiceAccount
metadata:
  name: proxy-gateway-job-reader-sa # This ServiceAccount is referenced by the proxy-gateway Deployment
  namespace: default # Ensure this is the same namespace where your proxy-gateway is deployed

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole # Use ClusterRole for broader permissions if needed, or Role for namespace-scoped
metadata:
  name: job-reader-clusterrole # A ClusterRole to allow reading Job status
rules:
- apiGroups: ["batch"] # The API group for Jobs
  resources: ["jobs"]  # The resource type we need to access
  verbs: ["get", "list", "watch"] # Permissions to get, list, and watch jobs

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding # Bind the ClusterRole to the ServiceAccount
metadata:
  name: proxy-gateway-job-reader-binding # Name for the ClusterRoleBinding
subjects:
- kind: ServiceAccount
  name: proxy-gateway-job-reader-sa # Reference the ServiceAccount created above
  namespace: default # Must match the namespace of the ServiceAccount
roleRef:
  kind: ClusterRole
  name: job-reader-clusterrole # Reference the ClusterRole created above
  apiGroup: rbac.authorization.k8s.io # API group for RBAC

