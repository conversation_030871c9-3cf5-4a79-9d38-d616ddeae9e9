# k8s/evaluation-service.yaml
# Combined Deployment and Service definition for the Evaluation Service

apiVersion: apps/v1
kind: Deployment
metadata:
  name: evaluation-service
  labels:
    app: evaluation-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: evaluation-service
  template:
    metadata:
      labels:
        app: evaluation-service
    spec:
      containers:
      - name: evaluation-service
        image: ai-cost-performance-optimizer-evaluation-service:latest # Replace with your project ID
        imagePullPolicy: Always
        ports:
        - containerPort: 8087
        env:
        - name: CLICKHOUSE_HOST
          value: "clickhouse"
        - name: CLICKHOUSE_PORT
          value: "9000"
        - name: CLICKHOUSE_DB
          value: "default"
        - name: CLICKHOUSE_USER
          value: "test"
        - name: CL<PERSON><PERSON>HOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: clickhouse-credentials
              key: password
              optional: false
---
apiVersion: v1
kind: Service
metadata:
  name: evaluation-service
  labels:
    app: evaluation-service
spec:
  selector:
    app: evaluation-service
  ports:
    - protocol: TCP
      port: 8087
      targetPort: 8087
  type: NodePort # Changed from ClusterIP to NodePort for external testing

