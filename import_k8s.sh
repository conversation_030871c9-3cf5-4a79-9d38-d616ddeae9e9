#!/bin/bash

# Set the namespace
NAMESPACE="default"

# Function to check if a resource is already managed by Terraform
is_resource_managed() {
  terraform state show "$1" &> /dev/null
  return $?
}

# Define the list of directories containing Kubernetes manifests
DIRECTORIES=(
  "k8s/clickhouse"
  "k8s/kafka"
  "k8s/data-processor"
  "k8s/proxy-gateway"
  "k8s/prometheus"
  "k8s/dashboard-api"
  "k8s/frontend"
  "k8s/mock_backend"
  "k8s/policy-manager"
  "k8s/ai-optimizer"
  "k8s/evaluation-service"
  "k8s/governance-service"
  "k8s/integration-service"
)

# Iterate through the directories
for directory in "${DIRECTORIES[@]}"; do
  # Skip the redis-populator-job and scripts directories
  if [[ "$directory" == "redis-populator-job" || "$directory" == "scripts" || "$directory" == "k8s/proxy-gateway" ]]; then
    echo "Skipping $directory directory"
    continue
  fi

  # Find the Kubernetes manifest file in the directory
  manifest_file=$(find "$directory" -name "*.yaml" -o -name "*.yml" | head -n 1)

  # If no manifest file is found, skip the directory
  if [ -z "$manifest_file" ]; then
    echo "No manifest file found in $directory directory"
    continue
  fi

  # Extract the resource kind and name from the manifest file
  resource_kind=$(yq e '.kind' "$manifest_file")
  resource_name=$(yq e '.metadata.name' "$manifest_file")

  # Define the Terraform resource type based on the resource kind
  case "$resource_kind" in
    Deployment)
      resource_type="kubernetes_deployment"
      ;;
    Service)
      resource_type="kubernetes_service"
      ;;
    ConfigMap)
      resource_type="kubernetes_config_map"
      ;;
    Secret)
      resource_type="kubernetes_secret"
      ;;
    PersistentVolumeClaim|ClusterIssuer|ServiceAccount|Role|RoleBinding)
      echo "Skipping unsupported resource kind: $resource_kind"
      continue
      ;;
    *)
      echo "Unsupported resource kind: $resource_kind"
      continue
      ;;
  esac

  # Define the Terraform resource address
  terraform_resource_address="$resource_type.$resource_name"

  # Check if the resource is already managed by Terraform
  if ! is_resource_managed "$terraform_resource_address"; then
    # Import the resource into Terraform
    terraform import "$terraform_resource_address" "default/$resource_name"
    echo "Imported $terraform_resource_address"
  else
    echo "$terraform_resource_address already managed by Terraform"
  fi
done