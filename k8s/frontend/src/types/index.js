/**
 * @typedef {Object} PolicyCriteria
 * @property {string} field
 * @property {string} operator
 * @property {string} value
 */

/**
 * @typedef {Object} Policy
 * @property {string} id
 * @property {string} name
 * @property {string} description
 * @property {PolicyCriteria[]} criteria
 * @property {string} action
 * @property {string} backend_id
 * @property {number} priority
 * @property {string} rules // JSON string of rules
 * @property {string} created_at
 * @property {string} string
 * @property {Object} metadata
 * @property {string[]} allowed_users
 * @property {string[]} blocked_users
 * @property {number} rate_limit
 * @property {number} budget
 */

/**
 * @typedef {Object} ModelProfile
 * @property {string} id
 * @property {string} name
 * @property {string[]} aliases
 * @property {string[]} capabilities
 * @property {string} pricing_tier
 * @property {string} data_sensitivity
 * @property {number} expected_latency_ms
 * @property {number} expected_cost
 * @property {string} url               // *** FIX: Changed from backend_url to url to match backend JSON ***
 * @property {string} backend_type
 * @property {number} cost_per_input_token
 * @property {number} cost_per_output_token
 * @property {number} cpu_cost_per_hour
 * @property {number} memory_cost_per_hour
 * @property {string} api_key
 * @property {string} created_at
 * @property {string} updated_at
 * @property {string} [version]
 * @property {string} [owner]
 * @property {string} [status]
 * @property {string} [documentation_url]
 * @property {string} [license]
 * @property {string} [fine_tuning_details]
 * @property {number} [input_context_length]
 * @property {number} [output_context_length]
 * @property {string} [training_data_info]
 * @property {string} [last_evaluated_at] // Stored as string, use dayjs for parsing
 * @property {Object} [evaluation_metrics] // Or more specific typedef if known structure
 * @property {string[]} [compliance_tags]
 * @property {string} [region]
 * @property {string} [provider]
 */

/**
 * @typedef {Object} InferenceLog
 * @property {string} requestId
 * @property {string} timestamp
 * @property {string} method
 * @property {string} path
 * @property {string} clientIp
 * @property {string} userAgent
 * @property {string} selectedBackendId
 * @property {string} backend_url        // *** FIX: Changed from backendUrl to backend_url to match backend JSON ***
 * @property {string} backendType
 * @property {number} responseStatus
 * @property {number} latencyMs
 * @property {number} inputTokens
 * @property {number} outputTokens
 * @property {number} totalCost
 * @property {string} error
 * @property {string} policyIdApplied
 * @property {string} modelUsed
 * @property {number} cpuUsage
 * @property {number} memoryUsage
 * @property {string} taskType
 * @property {Object.<string, string[]>} requestHeaders
 * @property {string} requestBodySnippet
 * @property {Object.<string, string[]>} responseHeaders
 * @property {string} responseBodySnippet
 */

/**
 * @typedef {Object} InferenceSummary
 * @property {string} selectedBackendId
 * @property {number} totalRequests
 * @property {number} averageLatencyMs
 * @property {number} averageStatusCode
 * @property {number} totalInputTokens
 * @property {number} totalOutputTokens
 * @property {number} totalCost
 * @property {string} policyIdApplied
 * @property {string} modelUsed
 */

/**
 * @typedef {Object} TimeSeriesData
 * @property {string} timeInterval
 * @property {number} requestCount
 * @property {number} averageLatencyMs
 * @property {number} totalInputTokens
 * @property {number} totalOutputTokens
 * @property {number} totalCost
 * @property {string} policyIdApplied
 * @property {string} modelUsed
 */

/**
 * @typedef {Object} BackendLatency
 * @property {string} backendId
 * @property {string} backendName // This will be populated by client-side mapping
 * @property {number} averageLatencyMs
 * @property {string} lastUpdated
 */

/**
 * @typedef {Object} OptimalBackend
 * @property {string} optimalBackendId
 * @property {number} averageLatencyMs
 * @property {string} message
 * @property {string} preference
 * @property {string} timestamp // This field is inferred from UI if not directly from backend
 */
