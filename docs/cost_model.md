# LLM Cost-Performance Optimizer: Cost Model

## Overview

The LLM Cost-Performance Optimizer implements a sophisticated cost model that tracks, calculates, and optimizes expenses associated with LLM usage. This document outlines how costs are modeled, measured, and used for optimization decisions.

## Cost Components

The system tracks several cost dimensions:

### 1. Token-Based Costs

- **Input Token Costs**: Charges incurred for tokens in the prompt/request
  - Tracked per model via `CostPerInputToken` in model profiles
  - Different models have different input token pricing

- **Output Token Costs**: Charges incurred for tokens in the completion/response
  - Tracked per model via `CostPerOutputToken` in model profiles
  - Generally higher than input token costs
  - Varies significantly between model providers and tiers

### 2. Infrastructure Costs

- **CPU Usage Costs**: Computational resources used during inference
  - Tracked via `CPUCostPerHour` in model profiles
  - Particularly relevant for self-hosted models

- **Memory Usage Costs**: RAM utilized during inference
  - Tracked via `MemoryCostPerHour` in model profiles
  - Critical for large context models and batch processing

### 3. Aggregate Costs

- **Total Request Cost**: Sum of all costs for a single request
  - Calculated as: `(InputTokens * CostPerInputToken) + (OutputTokens * CostPerOutputToken)`
  - Additional computational costs may be added for self-hosted models

- **Expected Cost**: A pre-calculated estimate of typical cost per request
  - Used for initial routing decisions before actual token counts are known

## Cost Calculation Process

1. The proxy-gateway intercepts the LLM request and forwards it to the selected backend
2. Upon receiving a response, it extracts token usage information based on the backend type:
   - OpenAI/vLLM: Extracts from Usage.PromptTokens and Usage.CompletionTokens
   - Google Gemini: Extracts from UsageMetadata.PromptTokenCount and UsageMetadata.CandidatesTokenCount
   - Anthropic: Extracts from Usage.InputTokens and Usage.OutputTokens
3. The cost is calculated using the formula: 
   ```
   totalCost = (inputTokens * profile.CostPerInputToken) + (outputTokens * profile.CostPerOutputToken)
   ```
4. The calculated cost is included in the inference log sent to Kafka
5. The data-processor may further enrich cost data with infrastructure metrics

## Cost Optimization Strategies

The system employs several strategies to optimize costs:

### 1. Intelligent Routing

- **Cost-Priority Routing**: When "cost_priority" is set as the optimization preference, the system routes requests to the most cost-effective model capable of handling the task
- **Balanced Optimization**: When "balanced" is set, the system considers both cost and performance, using a weighted formula to determine optimal routing

### 2. Response Caching

- Frequently asked queries can be served from cache, eliminating token costs for duplicate requests
- The system intelligently determines which response types are suitable for caching (primarily factual queries and code generation)
- Non-streaming requests are prioritized for caching

### 3. Task-Type Optimization

- Different task types have different cost-performance profiles
- The AI Optimizer classifies prompts and can route simpler tasks to more cost-effective models
- Complex reasoning can be directed to more capable (but expensive) models only when necessary

### 4. Model Selection Based on Token Count

- Models with lower per-token costs are preferred for verbose tasks
- The system can potentially route based on estimated output length

## Cost Monitoring and Reporting

- All request costs are logged to Kafka and stored in ClickHouse
- The dashboard provides visibility into:
  - Total costs per time period
  - Costs by model/backend
  - Costs by task type
  - Cost savings from optimization

## Model Profile Cost Configuration

Each model profile includes these cost-related fields:

```json
{
  "id": "model-id",
  "name": "Model Name",
  "expected_cost": 0.001,
  "cost_per_input_token": 0.0000001,
  "cost_per_output_token": 0.0000002,
  "cpu_cost_per_hour": 1.25,
  "memory_cost_per_hour": 0.75
}
  "version": "1.0.0",
  "owner": "Platform Team",
  "status": "active",
  "documentation_url": "https://example.com/model-docs",
  "license": "MIT",
  "fine_tuning_details": "Fine-tuned on...",
  "input_context_length": 4096,
  "output_context_length": 1024,
  "training_data_info": "...",
  "last_evaluated_at": "2024-01-01T00:00:00Z",
  "evaluation_metrics": {
    "accuracy": 0.95,
    "f1_score": 0.92
  },
  "compliance_tags": ["HIPAA", "GDPR"],
  "region": "us-west2",
  "provider": "OpenAI",
  "description": "A powerful LLM..."
}
```

## Model Profile Fields Explained

*   **id**: Unique identifier for the model.
*   **name**: Human-readable name of the model.
*   **aliases**: Alternative names for the model.
*   **capabilities**: List of supported capabilities (e.g., "chat", "text-generation").
*   **pricing\_tier**: Pricing tier of the model (e.g., "free", "standard", "premium").
*   **data\_sensitivity**: Data sensitivity level supported by the model (e.g., "low", "medium", "high").
*   **expected\_latency\_ms**: Expected latency of the model in milliseconds.
*   **expected\_cost**: Expected cost per request.
*   **cost\_per\_input\_token**: Cost per input token.
*   **cost\_per\_output\_token**: Cost per output token.
*   **cpu\_cost\_per\_hour**: Cost per CPU hour for self-hosted models.
*   **memory\_cost\_per\_hour**: Cost per memory hour for self-hosted models.
*   **url**: URL of the model's backend endpoint.
*   **backend\_type**: Type of backend (e.g., "openai", "google", "anthropic", "vllm").
*   **version**: Version of the model.
*   **owner**: Owner of the model.
*   **status**: Status of the model (e.g., "active", "inactive").
*   **documentation\_url**: URL of the model's documentation.
*   **license**: License of the model.
*   **fine\_tuning\_details**: Details about fine-tuning.
*   **input\_context\_length**: Maximum input context length.
*   **output\_context\_length**: Maximum output context length.
*   **training\_data\_info**: Information about the training data.
*   **last\_evaluated\_at**: Timestamp of the last evaluation.
*   **evaluation\_metrics**: Evaluation metrics for the model.
*   **compliance\_tags**: Compliance tags for the model (e.g., "HIPAA", "GDPR").
*   **region**: Region where the model is deployed.
*   **provider**: Provider of the model (e.g., "OpenAI", "Google", "Anthropic").
  *   **description**: Description of the model.


## Future Cost Optimization Enhancements

1. **Automated Conciseness**: Techniques to reduce token usage through prompt optimization
2. **Token Budgeting**: Setting maximum token limits based on task criticality and budget constraints
3. **Smart Truncation**: Intelligently trimming context to reduce input token costs while preserving meaning
4. **Model Compilation & Quantization**: Optimizing self-hosted models for lower infrastructure costs
5. **Cost-Aware Batching**: Grouping similar requests to reduce per-request overhead

## Cost Considerations by Request Type

Different request types have different cost optimization opportunities:

- **Factual Queries**: High caching potential, good candidates for smaller models
- **Creative Writing**: Less cacheable, may require more capable models
- **Code Generation**: Moderately cacheable, benefits from specialized code models
- **Complex Reasoning**: Rarely cacheable, often requires premium models
