package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gorilla/mux"
)

// --- Constants ---
const (
	redisAddr                       = "redis:6379"
	REDIS_POLICIES_KEY_PREFIX       = "policy:"
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"
	REDIS_PROMPT_KEY_PREFIX         = "prompt:" // Redis key prefix for prompts
	POLICY_UPDATES_CHANNEL          = "policy_updates"
	MODEL_PROFILE_UPDATES_CHANNEL   = "model_profile_updates"
	PROMPT_UPDATES_CHANNEL          = "prompt_updates" // Channel for prompt updates
)

// --- Structs ---

// Policy defines a routing policy.
// It's crucial for the AI Optimizer to enforce governance.
// ENHANCED for RBAC: Added Effect and Subjects fields.
type Policy struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Criteria    json.RawMessage `json:"criteria"`             // JSON string for specific criteria (e.g., {"user_id": "abc"}, {"model_id": "gpt-4"})
	Action      string          `json:"action"`               // "ROUTE", "OPTIMIZE", "BLOCK", "ALLOW_ACCESS", "DENY_ACCESS", etc.
	BackendID   string          `json:"backend_id,omitempty"` // Only if Action is ROUTE or OPTIMIZE
	Priority    int             `json:"priority"`             // Lower number means higher priority
	Rules       json.RawMessage `json:"rules,omitempty"`      // For more complex routing rules (could be a more structured type)
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	Metadata    json.RawMessage `json:"metadata,omitempty"`   // Generic field for additional data
	RateLimit   int             `json:"rate_limit,omitempty"` // Requests per minute
	Budget      float64         `json:"budget,omitempty"`     // Max cost per time period

	Effect       string   `json:"effect,omitempty"`        // "ALLOW" or "DENY"
	Subjects     []string `json:"subjects,omitempty"`      // List of user IDs, role IDs, or group IDs
	ResourceType string   `json:"resource_type,omitempty"` // e.g., "model", "feature", "log"
	ResourceIDs  []string `json:"resource_ids,omitempty"`  // List of specific resource IDs (e.g., model IDs)
	Permissions  []string `json:"permissions,omitempty"`   // List of actions (e.g., "read", "write", "execute", "access_llm")
}

// RoutingStrategy defines how requests should be routed
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
	Permissions       []string                   `json:"permissions,omitempty"` // List of actions (e.g., "read", "write", "execute", "access_llm")
}

// ModelCapabilityRequirement defines what capabilities are needed for a specific task
type ModelCapabilityRequirement struct {
	RequiredCapabilities []string `json:"required_capabilities"`
	MinimumTier          int      `json:"minimum_tier"`
	PreferredProvider    string   `json:"preferred_provider,omitempty"`
}

// ModelProfile defines the structure for an LLM model's profile, including its cost and performance characteristics.
// This struct is enhanced to serve as the Centralized LLM Registry.
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"`
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"` // *** FIX: Changed to "url" for consistency with other services and populate_redis.py ***
	BackendType        string    `json:"backend_type"`
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	Version             string          `json:"version,omitempty"`
	Owner               string          `json:"owner,omitempty"`
	Status              string          `json:"status,omitempty"`
	DocumentationURL    string          `json:"documentation_url,omitempty"`
	License             string          `json:"license,omitempty"`
	FineTuningDetails   string          `json:"fine_tuning_details,omitempty"`
	InputContextLength  int             `json:"input_context_length,omitempty"`
	OutputContextLength int             `json:"output_context_length,omitempty"`
	TrainingDataInfo    string          `json:"training_data_info,omitempty"`
	LastEvaluatedAt     time.Time       `json:"last_evaluated_at,omitempty"`
	EvaluationMetrics   json.RawMessage `json:"evaluation_metrics,omitempty"`
	ComplianceTags      []string        `json:"compliance_tags,omitempty"`
	Region              string          `json:"region,omitempty"`
	Provider            string          `json:"provider,omitempty"`
}

// Prompt defines the structure for a managed LLM prompt.
type Prompt struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Version     string          `json:"version"` // e.g., "1.0", "v2-testing"
	Content     string          `json:"content"` // The actual prompt text
	Description string          `json:"description,omitempty"`
	Tags        []string        `json:"tags,omitempty"`     // e.g., "chatbot", "summarization", "latest"
	Owner       string          `json:"owner,omitempty"`    // Creator or responsible team
	Status      string          `json:"status,omitempty"`   // e.g., "draft", "approved", "deprecated"
	Metadata    json.RawMessage `json:"metadata,omitempty"` // Arbitrary additional metadata
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// --- Global Variables ---
var (
	redisClient   *redis.Client
	policies      map[string]Policy
	modelProfiles map[string]ModelProfile
	prompts       map[string]Prompt // Cache for managed prompts
	mu            sync.RWMutex      // Mutex for protecting all maps
)

// init function runs once on startup to initialize connections and load initial data.
func init() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize caches
	policies = make(map[string]Policy)
	modelProfiles = make(map[string]ModelProfile)
	prompts = make(map[string]Prompt) // Initialize prompts cache

	// Load initial data from Redis
	if err := loadPoliciesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load policies from Redis on startup: %v", err)
	}
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	}
	// Load initial prompts
	if err := loadPromptsFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load prompts from Redis on startup: %v", err)
	}

	// Start goroutines to listen for Redis Pub/Sub updates
	go listenForRedisPolicyUpdates()
	go listenForRedisModelProfileUpdates()
	go listenForRedisPromptUpdates() // Start listening for prompt updates
}

func main() {
	defer redisClient.Close()

	router := mux.NewRouter()

	// Add health check endpoint
	router.HandleFunc("/health", healthCheck).Methods("GET")

	// API prefix for all endpoints
	apiRouter := router.PathPrefix("/api").Subrouter()

	// Policies API Endpoints - with both underscore and hyphen versions
	apiRouter.HandleFunc("/policies", createPolicy).Methods("POST")
	apiRouter.HandleFunc("/policies/", createPolicy).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/policies", getPolicies).Methods("GET")
	apiRouter.HandleFunc("/policies/", getPolicies).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", getPolicy).Methods("GET")
	apiRouter.HandleFunc("/policies/{id}/", getPolicy).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", updatePolicy).Methods("PUT")
	apiRouter.HandleFunc("/policies/{id}/", updatePolicy).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/policies/{id}", deletePolicy).Methods("DELETE")
	apiRouter.HandleFunc("/policies/{id}/", deletePolicy).Methods("DELETE") // With trailing slash

	// Model Profiles API Endpoints - with both underscore and hyphen versions
	apiRouter.HandleFunc("/model_profiles", createModelProfile).Methods("POST")
	apiRouter.HandleFunc("/model-profiles", createModelProfile).Methods("POST")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/", createModelProfile).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/", createModelProfile).Methods("POST") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles", getModelProfiles).Methods("GET")
	apiRouter.HandleFunc("/model-profiles", getModelProfiles).Methods("GET")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/", getModelProfiles).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/", getModelProfiles).Methods("GET") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", getModelProfile).Methods("GET")
	apiRouter.HandleFunc("/model-profiles/{id}", getModelProfile).Methods("GET")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", getModelProfile).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", getModelProfile).Methods("GET") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", updateModelProfile).Methods("PUT")
	apiRouter.HandleFunc("/model-profiles/{id}", updateModelProfile).Methods("PUT")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", updateModelProfile).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", updateModelProfile).Methods("PUT") // Hyphenated with trailing slash

	apiRouter.HandleFunc("/model_profiles/{id}", deleteModelProfile).Methods("DELETE")
	apiRouter.HandleFunc("/model-profiles/{id}", deleteModelProfile).Methods("DELETE")  // Hyphenated version
	apiRouter.HandleFunc("/model_profiles/{id}/", deleteModelProfile).Methods("DELETE") // With trailing slash
	apiRouter.HandleFunc("/model-profiles/{id}/", deleteModelProfile).Methods("DELETE") // Hyphenated with trailing slash

	// Prompt Management API Endpoints with /api prefix
	apiRouter.HandleFunc("/prompts", createPrompt).Methods("POST")
	apiRouter.HandleFunc("/prompts/", createPrompt).Methods("POST") // With trailing slash
	apiRouter.HandleFunc("/prompts", getPrompts).Methods("GET")
	apiRouter.HandleFunc("/prompts/", getPrompts).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/prompts/{id}", getPrompt).Methods("GET")
	apiRouter.HandleFunc("/prompts/{id}/", getPrompt).Methods("GET") // With trailing slash
	apiRouter.HandleFunc("/prompts/{id}", updatePrompt).Methods("PUT")
	apiRouter.HandleFunc("/prompts/{id}/", updatePrompt).Methods("PUT") // With trailing slash
	apiRouter.HandleFunc("/prompts/{id}", deletePrompt).Methods("DELETE")
	apiRouter.HandleFunc("/prompts/{id}/", deletePrompt).Methods("DELETE") // With trailing slash

	// Also keep the original non-prefixed endpoints for backward compatibility
	router.HandleFunc("/policies", createPolicy).Methods("POST")
	router.HandleFunc("/policies", getPolicies).Methods("GET")
	router.HandleFunc("/policies/{id}", getPolicy).Methods("GET")
	router.HandleFunc("/policies/{id}", updatePolicy).Methods("PUT")
	router.HandleFunc("/policies/{id}", deletePolicy).Methods("DELETE")

	router.HandleFunc("/model_profiles", createModelProfile).Methods("POST")
	router.HandleFunc("/model_profiles", getModelProfiles).Methods("GET")
	router.HandleFunc("/model_profiles/{id}", getModelProfile).Methods("GET")
	router.HandleFunc("/model_profiles/{id}", updateModelProfile).Methods("PUT")
	router.HandleFunc("/model_profiles/{id}", deleteModelProfile).Methods("DELETE")

	// NEW: Prompt Management API Endpoints
	router.HandleFunc("/prompts", createPrompt).Methods("POST")
	router.HandleFunc("/prompts", getPrompts).Methods("GET")
	router.HandleFunc("/prompts/{id}", getPrompt).Methods("GET")
	router.HandleFunc("/prompts/{id}", updatePrompt).Methods("PUT")
	router.HandleFunc("/prompts/{id}", deletePrompt).Methods("DELETE")

	// Set up CORS for all routes (to allow frontend dashboard to access)
	corsHandler := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			// Make sure to include X-User-ID, X-User-Roles if you plan to re-enable RBAC
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-LLM-API-Key, X-Preferred-LLM-ID, X-Conversation-ID, X-User-ID, X-User-Roles")
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
	router.Use(corsHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8083" // Default port for policy-manager
	}
	log.Printf("Policy Manager starting on port %s...", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

// loadPoliciesFromRedis fetches all policies from Redis into the cache.
func loadPoliciesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPolicies := make(map[string]Policy)
	keys, err := redisClient.Keys(ctx, REDIS_POLICIES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get policy keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting policy %s from Redis: %v", key, err)
			continue
		}
		var policy Policy
		if err := json.Unmarshal([]byte(val), &policy); err != nil {
			log.Printf("Error unmarshalling policy %s: %v", key, err)
			continue
		}
		newPolicies[policy.ID] = policy
	}
	policies = newPolicies
	log.Printf("Loaded %d policies from Redis.", len(policies))
	return nil
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		// *** FIX: Ensure ModelProfile.BackendURL uses `json:"url"` for unmarshalling
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))
	return nil
}

// NEW: loadPromptsFromRedis fetches all prompts from Redis into the cache.
func loadPromptsFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPrompts := make(map[string]Prompt)
	keys, err := redisClient.Keys(ctx, REDIS_PROMPT_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get prompt keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting prompt %s from Redis: %v", key, err)
			continue
		}
		var prompt Prompt
		if err := json.Unmarshal([]byte(val), &prompt); err != nil {
			log.Printf("Error unmarshalling prompt %s: %v", key, err)
			continue
		}
		newPrompts[prompt.ID] = prompt
	}
	prompts = newPrompts
	log.Printf("Loaded %d prompts from Redis.", len(prompts))
	return nil
}

// listenForRedisPolicyUpdates listens for Redis Pub/Sub messages for policy updates.
func listenForRedisPolicyUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), POLICY_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis policy updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPoliciesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading policies from Redis: %v", err)
		} else {
			log.Println("Policies cache refreshed due to pending updates...")
		}
	}
}

// listenForRedisModelProfileUpdates listens for Redis Pub/Sub messages for model profile updates.
func listenForRedisModelProfileUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis model profile updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadModelProfilesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading model profiles from Redis: %v", err)
		} else {
			log.Println("Model profiles cache refreshed due to pending updates...")
		}
	}
}

// NEW: listenForRedisPromptUpdates listens for Redis Pub/Sub messages for prompt updates.
func listenForRedisPromptUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), PROMPT_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis prompt updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPromptsFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading prompts from Redis: %v", err)
		} else {
			log.Println("Prompts cache refreshed due to pending updates...")
		}
	}
}

// --- Policy API Handlers ---
func createPolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var policy Policy
	if err := json.NewDecoder(r.Body).Decode(&policy); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if policy.ID == "" {
		http.Error(w, "Policy ID is required.", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if _, exists := policies[policy.ID]; exists {
		http.Error(w, "Policy with this ID already exists.", http.StatusConflict)
		return
	}

	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()
	policies[policy.ID] = policy

	// Store in Redis - Check if it's a routing strategy or a policy
	policyJSON, _ := json.Marshal(policy)
	redisKey := REDIS_POLICIES_KEY_PREFIX + policy.ID
	if policy.Action == "ROUTE" || policy.Action == "OPTIMIZE" {
		redisKey = "routing_strategy:" + policy.ID // Store routing strategies with a different prefix
	}
	if err := redisClient.Set(context.Background(), redisKey, policyJSON, 0).Err(); err != nil {
		log.Printf("Error setting policy in Redis: %v", err)
		http.Error(w, "Failed to store policy", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, policy.ID)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(policy)
	log.Printf("Policy '%s' created.", policy.ID)
}

func getPolicies(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	policyList := []Policy{}
	for _, p := range policies {
		policyList = append(policyList, p)
	}
	json.NewEncoder(w).Encode(policyList)
}

func getPolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	policy, ok := policies[id]
	if !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(policy)
}

func updatePolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedPolicy Policy
	if err := json.NewDecoder(r.Body).Decode(&updatedPolicy); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if updatedPolicy.ID != "" && updatedPolicy.ID != id {
		http.Error(w, "Policy ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedPolicy.ID = id // Ensure the ID from the URL is used

	mu.Lock()
	defer mu.Unlock()

	existingPolicy, ok := policies[id]
	if !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}

	updatedPolicy.CreatedAt = existingPolicy.CreatedAt // Preserve original creation timestamp
	updatedPolicy.UpdatedAt = time.Now()
	policies[id] = updatedPolicy

	// Update in Redis
	policyJSON, _ := json.Marshal(updatedPolicy)
	if err := redisClient.Set(context.Background(), REDIS_POLICIES_KEY_PREFIX+id, policyJSON, 0).Err(); err != nil {
		log.Printf("Error updating policy in Redis: %v", err)
		http.Error(w, "Failed to update policy in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, id)

	json.NewEncoder(w).Encode(updatedPolicy)
	log.Printf("Policy '%s' updated.", id)
}

func deletePolicy(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	if _, ok := policies[id]; !ok {
		http.Error(w, "Policy not found.", http.StatusNotFound)
		return
	}

	delete(policies, id)

	// Delete from Redis
	if err := redisClient.Del(context.Background(), REDIS_POLICIES_KEY_PREFIX+id).Err(); err != nil {
		log.Printf("Error deleting policy from Redis: %v", err)
		http.Error(w, "Failed to delete policy from Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), POLICY_UPDATES_CHANNEL, id)

	w.WriteHeader(http.StatusNoContent) // 204 No Content for successful deletion
	log.Printf("Policy '%s' deleted.", id)
}

// --- Model Profile (LLM Registry) API Handlers ---
func createModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var profile ModelProfile
	if err := json.NewDecoder(r.Body).Decode(&profile); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if profile.ID == "" {
		http.Error(w, "Model Profile ID is required.", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if _, exists := modelProfiles[profile.ID]; exists {
		http.Error(w, "Model Profile with this ID already exists.", http.StatusConflict)
		return
	}

	profile.CreatedAt = time.Now()
	profile.UpdatedAt = time.Now()
	modelProfiles[profile.ID] = profile

	// Store in Redis
	profileJSON, _ := json.Marshal(profile)
	if err := redisClient.Set(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+profile.ID, profileJSON, 0).Err(); err != nil {
		log.Printf("Error setting model profile in Redis: %v", err)
		http.Error(w, "Failed to store model profile", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, profile.ID)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(profile)
	log.Printf("Model Profile '%s' created.", profile.ID)
}

func getModelProfiles(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	profileList := []ModelProfile{}
	for _, mp := range modelProfiles {
		profileList = append(profileList, mp)
	}
	json.NewEncoder(w).Encode(profileList)
}

func getModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.RLock()
	defer mu.RUnlock()

	profile, ok := modelProfiles[id]
	if !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(profile)
}

func updateModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedProfile ModelProfile
	if err := json.NewDecoder(r.Body).Decode(&updatedProfile); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if updatedProfile.ID != "" && updatedProfile.ID != id {
		http.Error(w, "Model Profile ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedProfile.ID = id // Ensure the ID from the URL is used

	mu.Lock()
	defer mu.Unlock()

	_, ok := modelProfiles[id]
	if !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}

	// updatedProfile.CreatedAt = time.Now() // Update creation timestamp
	updatedProfile.UpdatedAt = time.Now()
	modelProfiles[id] = updatedProfile

	// Update in Redis
	profileJSON, _ := json.Marshal(updatedProfile)
	if err := redisClient.Set(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+id, profileJSON, 0).Err(); err != nil {
		log.Printf("Error updating model profile in Redis: %v", err)
		http.Error(w, "Failed to update model profile in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, id)

	json.NewEncoder(w).Encode(updatedProfile)
	log.Printf("Model Profile '%s' updated.", id)
}

func deleteModelProfile(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	mu.Lock()
	defer mu.Unlock()

	if _, ok := modelProfiles[id]; !ok {
		http.Error(w, "Model Profile not found.", http.StatusNotFound)
		return
	}

	delete(modelProfiles, id)

	// Delete from Redis
	if err := redisClient.Del(context.Background(), REDIS_MODEL_PROFILES_KEY_PREFIX+id).Err(); err != nil {
		log.Printf("Error deleting model profile from Redis: %v", err)
		http.Error(w, "Failed to delete model profile from Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, id)

	w.WriteHeader(http.StatusNoContent) // 204 No Content for successful deletion
	log.Printf("Model Profile '%s' deleted.", id)
}

// --- NEW: Prompt Management API Handlers ---
func createPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	var prompt Prompt
	if err := json.NewDecoder(r.Body).Decode(&prompt); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Basic validation for required fields
	if prompt.ID == "" || prompt.Name == "" || prompt.Version == "" || prompt.Content == "" {
		http.Error(w, "Prompt ID, Name, Version, and Content are required.", http.StatusBadRequest)
		return
	}

	// Combine ID and Version to create a unique key for the prompt
	// This supports versioning and allows for multiple versions of the same logical prompt name
	promptKey := fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)

	mu.Lock()
	defer mu.Unlock()

	if _, exists := prompts[promptKey]; exists {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' already exists.", prompt.ID, prompt.Version), http.StatusConflict)
		return
	}

	prompt.CreatedAt = time.Now()
	prompt.UpdatedAt = time.Now()
	prompts[prompt.ID] = prompt

	// Store in Redis
	promptJSON, _ := json.Marshal(prompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error setting prompt in Redis: %v", err)
		http.Error(w, "Failed to store prompt", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(prompt)
	log.Printf("Prompt '%s' (Version: %s) created.", prompt.ID, prompt.Version)
}

func getPrompts(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	mu.RLock()
	defer mu.RUnlock()

	promptList := []Prompt{}
	for _, p := range prompts {
		promptList = append(promptList, p)
	}
	json.NewEncoder(w).Encode(promptList)
}

func getPrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"] // This ID refers to the prompt ID (e.g., "summarization-template")

	// Allow querying by prompt ID (returns all versions) or specific ID:Version
	version := r.URL.Query().Get("version")

	mu.RLock()
	defer mu.RUnlock()

	foundPrompts := []Prompt{}
	if version != "" {
		// Specific version requested: use the exact key "ID:Version"
		promptKey := fmt.Sprintf("%s:%s", id, version)
		if p, ok := prompts[promptKey]; ok {
			foundPrompts = append(foundPrompts, p)
		}
	} else {
		// No specific version, return all versions for this ID
		for key, p := range prompts {
			if !strings.Contains(key, ":") {
				continue
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
			if !strings.Contains(key, ":") {
				continue
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
			// Check if the key starts with the desired ID and a colon for the version
			if strings.HasPrefix(key, id+":") {
				foundPrompts = append(foundPrompts, p)
			}
		}
	}

	if len(foundPrompts) == 0 {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' (and version '%s' if specified) not found.", id, version), http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(foundPrompts)
}

func updatePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	var updatedPrompt Prompt
	if err := json.NewDecoder(r.Body).Decode(&updatedPrompt); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Ensure ID and Version in body match the URL ID and the prompt's own version
	if updatedPrompt.ID != "" && updatedPrompt.ID != id {
		http.Error(w, "Prompt ID in body must match URL ID.", http.StatusBadRequest)
		return
	}
	updatedPrompt.ID = id // Ensure the ID from the URL is used

	if updatedPrompt.Version == "" {
		http.Error(w, "Prompt Version is required for update.", http.StatusBadRequest)
		return
	}

	promptKey := fmt.Sprintf("%s:%s", id, updatedPrompt.Version)

	mu.Lock()
	defer mu.Unlock()

	existingPrompt, ok := prompts[promptKey]
	if !ok {
		http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' not found.", id, updatedPrompt.Version), http.StatusNotFound)
		return
	}

	updatedPrompt.CreatedAt = existingPrompt.CreatedAt // Preserve original creation timestamp
	updatedPrompt.UpdatedAt = time.Now()
	prompts[promptKey] = updatedPrompt

	// Update in Redis
	promptJSON, _ := json.Marshal(updatedPrompt)
	if err := redisClient.Set(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey, promptJSON, 0).Err(); err != nil {
		log.Printf("Error updating prompt in Redis: %v", err)
		http.Error(w, "Failed to update prompt in Redis", http.StatusInternalServerError)
		return
	}

	// Publish update event
	redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)

	json.NewEncoder(w).Encode(updatedPrompt)
	log.Printf("Prompt '%s' (Version: %s) updated.", id, updatedPrompt.Version)
}

func deletePrompt(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	vars := mux.Vars(r)
	id := vars["id"]

	version := r.URL.Query().Get("version") // Allow deleting specific version via query param

	mu.Lock()
	defer mu.Unlock()

	if version != "" {
		// Delete specific version
		promptKey := fmt.Sprintf("%s:%s", id, version)
		if _, ok := prompts[promptKey]; !ok {
			http.Error(w, fmt.Sprintf("Prompt with ID '%s' and Version '%s' not found.", id, version), http.StatusNotFound)
			return
		}
		delete(prompts, promptKey)
		if err := redisClient.Del(context.Background(), REDIS_PROMPT_KEY_PREFIX+promptKey).Err(); err != nil {
			log.Printf("Error deleting specific prompt from Redis: %v", err)
			http.Error(w, "Failed to delete prompt from Redis", http.StatusInternalServerError)
			return
		}
		redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, promptKey)
		log.Printf("Prompt '%s' (Version: %s) deleted.", id, version)

	} else {
		// Delete all versions for this ID (use with caution!)
		deletedCount := 0
		keysToDelete := []string{}
		for key := range prompts {
			// Check if the key starts with the desired ID and a colon, indicating a versioned prompt
			if strings.HasPrefix(key, id+":") {
				keysToDelete = append(keysToDelete, key)
				delete(prompts, key)
				deletedCount++
			}
		}

		if deletedCount == 0 {
			http.Error(w, fmt.Sprintf("No prompts found with ID '%s' to delete.", id), http.StatusNotFound)
			return
		}

		redisKeys := make([]string, len(keysToDelete))
		for i, k := range keysToDelete {
			redisKeys[i] = REDIS_PROMPT_KEY_PREFIX + k
		}

		if len(redisKeys) > 0 {
			if err := redisClient.Del(context.Background(), redisKeys...).Err(); err != nil {
				log.Printf("Error deleting multiple prompts from Redis: %v", err)
				http.Error(w, "Failed to delete prompts from Redis", http.StatusInternalServerError)
				return
			}
		}

		// Publish updates for each deleted key (or a single batch update signal)
		for _, k := range keysToDelete {
			redisClient.Publish(context.Background(), PROMPT_UPDATES_CHANNEL, k)
		}
		log.Printf("Deleted %d versions of prompt '%s'.", deletedCount, id)
	}

	w.WriteHeader(http.StatusNoContent)
}

// Add a simple health check endpoint
func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status":    "ok",
		"service":   "policy-manager",
		"timestamp": time.Now().Format(time.RFC3339),
	})
	log.Printf("Health check request received from %s", r.RemoteAddr)
}
