package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-redis/redis/v8"
)

// ModelProfile defines the structure for an LLM model's profile
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"`
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"` // Must be "url" for consistency with populate_redis.py
	BackendType        string    `json:"backend_type"`
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"` // API Key for external models
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Tier               int       `json:"tier"` // MODEL_TIER_BASIC, MODEL_TIER_INTERMEDIATE, MODEL_TIER_ADVANCED
}

// Constants for routing strategies
const (
	ROUTING_STRATEGY_DEFAULT  = "default"
	ROUTING_STRATEGY_CASCADE  = "cascade"
	ROUTING_STRATEGY_PARALLEL = "parallel"
	ROUTING_STRATEGY_HYBRID   = "hybrid"
)

// RoutingStrategy defines how requests should be routed
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
}

// ModelCapabilityRequirement defines what capabilities are needed for a specific task
type ModelCapabilityRequirement struct {
	RequiredCapabilities []string `json:"required_capabilities"`
	MinimumTier          int      `json:"minimum_tier"`
	PreferredProvider    string   `json:"preferred_provider,omitempty"`
}

// RouterDecision captures the routing decision process for logging and analysis
type RouterDecision struct {
	RequestID            string        `json:"request_id"`
	StrategyUsed         string        `json:"strategy_used"`
	AttemptedModels      []string      `json:"attempted_models,omitempty"`
	SelectedModelID      string        `json:"selected_model_id"`
	FallbackUsed         bool          `json:"fallback_used"`
	DecisionTime         time.Time     `json:"decision_time"`
	DecisionRationale    string        `json:"decision_rationale"`
	TaskType             string        `json:"task_type"`
	ExecutionTime        time.Duration `json:"execution_time,omitempty"`
	CircuitBreakerEvents []string      `json:"circuit_breaker_events,omitempty"`
	PolicyApplied        string        `json:"policy_applied,omitempty"`
	ModelTier            int           `json:"model_tier,omitempty"`
}

// OptimizationRequest is the request body received from the proxy-gateway
type OptimizationRequest struct {
	Prompt          string   `json:"prompt"`
	Model           string   `json:"model"` // The model requested by the client (e.g., "gpt-3.5-turbo")
	UserID          string   `json:"user_id,omitempty"`
	UserRoles       []string `json:"user_roles,omitempty"`
	DataSensitivity string   `json:"data_sensitivity,omitempty"`
	PreferredLLMID  string   `json:"preferred_llm_id,omitempty"` // NEW: For X-Preferred-LLM-ID header
	TaskType        string   `json:"task_type,omitempty"`
}

// AIOptimizerRouteResponse is the response body sent to the proxy-gateway
type AIOptimizerRouteResponse struct {
	SelectedBackendID  string `json:"selected_backend_id"`
	BackendType        string `json:"backend_type"`
	BackendURL         string `json:"backend_url"`
	PolicyIDApplied    string `json:"policy_id_applied"`
	ModelUsed          string `json:"model_used"`
	TaskType           string `json:"task_type"`
	Error              string `json:"error,omitempty"`
	FromCache          bool   `json:"from_from_cache,omitempty"`      // If AI Optimizer served from its cache
	CachedResponseBody string `json:"cached_response_body,omitempty"` // Cached body if FromCache is true
}

var (
	redisClient       *redis.Client
	modelProfiles     map[string]ModelProfile
	routingStrategies []RoutingStrategy
)

func main() {
	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: "redis:6379", // TODO: Make this configurable
		DB:   0,            // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize model profiles
	modelProfiles = make(map[string]ModelProfile)

	// Load initial model profiles from Redis
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	}

	// Load routing strategies from Redis
	if err := loadRoutingStrategiesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load routing strategies from Redis on startup: %v", err)
		routingStrategies = []RoutingStrategy{}
	}

	http.HandleFunc("/route", handleRouteRequest)
	log.Println("AI Optimizer starting on port 8085...")
	log.Fatal(http.ListenAndServe(":8085", nil))
}

func handleRouteRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var optReq OptimizationRequest
	if err := json.NewDecoder(r.Body).Decode(&optReq); err != nil {
		log.Printf("Error decoding request: %v", err)
		http.Error(w, "Error decoding request", http.StatusBadRequest)
		return
	}

	// Apply routing strategies
	var routeResp AIOptimizerRouteResponse

	if len(routingStrategies) == 0 {
		routeResp = AIOptimizerRouteResponse{
			Error: "No routing strategies found",
		}
	} else {
		// Find the first matching strategy based on TaskType and Model
	strategyLoop:
		for _, strategy := range routingStrategies {
			if strategy.TaskType != "" && strategy.TaskType != optReq.TaskType {
				continue // Skip if TaskType doesn't match
			}

			switch strategy.Strategy {
			case ROUTING_STRATEGY_DEFAULT:
				routeResp = applyDefaultStrategy(strategy, modelProfiles)
				if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
					break strategyLoop // Strategy found or error occurred, exit loop
				}
			case ROUTING_STRATEGY_CASCADE:
				routeResp = applyCascadeStrategy(strategy, modelProfiles)
				if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
					break strategyLoop // Strategy found or error occurred, exit loop
				}
			case ROUTING_STRATEGY_PARALLEL:
				routeResp = applyParallelStrategy(strategy, modelProfiles)
				if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
					break strategyLoop // Strategy found or error occurred, exit loop
				}
			default:
				log.Printf("Strategy not supported: %s", strategy.Strategy) // Log unsupported strategies
				continue                                                    // Skip to the next strategy
			}

			if routeResp.SelectedBackendID != "" || routeResp.Error != "" {
				break // Strategy found or error occurred, exit loop
			}
		}
	}

	// If no strategy was applied, return an error
	if routeResp.SelectedBackendID == "" && routeResp.Error == "" {
		routeResp = AIOptimizerRouteResponse{
			Error: "No suitable routing strategy found",
		}
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(routeResp); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
		return
	}
	log.Println("Successfully routed request")
}

func applyDefaultStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse
	if len(strategy.ModelPriorities) > 0 {
		selectedModelID := strategy.ModelPriorities[0]
		if profile, ok := modelProfiles[selectedModelID]; ok {
			// Check if the model meets the minimum tier requirement
			if profile.Tier >= strategy.ModelRequirements.MinimumTier {
				log.Printf("Applying default strategy: routing to model %s (Tier: %d)", profile.ID, profile.Tier)
				routeResp = AIOptimizerRouteResponse{
					SelectedBackendID: profile.ID,
					BackendType:       profile.BackendType,
					BackendURL:        profile.BackendURL,
					PolicyIDApplied:   strategy.PolicyID,
					ModelUsed:         profile.ID,
					TaskType:          strategy.TaskType,
				}
			} else {
				routeResp.Error = fmt.Sprintf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", selectedModelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
				log.Println(routeResp.Error)
			}
		} else {
			routeResp.Error = fmt.Sprintf("Model profile not found: %s", selectedModelID)
			log.Println(routeResp.Error)
		}
	} else {
		routeResp.Error = "No model priorities defined for default strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

func applyCascadeStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse
	if len(strategy.ModelPriorities) > 0 {
		for _, modelID := range strategy.ModelPriorities {
			if profile, ok := modelProfiles[modelID]; ok {
				// Check if the model meets the minimum tier requirement
				if profile.Tier >= strategy.ModelRequirements.MinimumTier {
					log.Printf("Applying cascade strategy: attempting model %s (Tier: %d)", profile.ID, profile.Tier)
					routeResp = AIOptimizerRouteResponse{
						SelectedBackendID: profile.ID,
						BackendType:       profile.BackendType,
						BackendURL:        profile.BackendURL,
						PolicyIDApplied:   strategy.PolicyID,
						ModelUsed:         profile.ID,
						TaskType:          strategy.TaskType,
					}
					break
				} else {
					log.Printf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
					continue
				}
			} else {
				log.Printf("Model profile not found: %s", modelID)
				continue
			}
		}
		if routeResp.SelectedBackendID == "" {
			routeResp.Error = "No suitable model found in cascade strategy"
			log.Println(routeResp.Error)
		}
	} else {
		routeResp.Error = "No model priorities defined for cascade strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

func applyParallelStrategy(strategy RoutingStrategy, modelProfiles map[string]ModelProfile) AIOptimizerRouteResponse {
	var routeResp AIOptimizerRouteResponse
	if len(strategy.ParallelModels) > 0 {
		var wg sync.WaitGroup
		responseChan := make(chan struct {
			ModelID  string
			Response string
			Error    error
		}, len(strategy.ParallelModels))

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		for _, modelID := range strategy.ParallelModels {
			wg.Add(1)
			go func(modelID string) {
				defer wg.Done()
				if profile, ok := modelProfiles[modelID]; ok {
					// Check if the model meets the minimum tier requirement
					if profile.Tier >= strategy.ModelRequirements.MinimumTier {
						log.Printf("Applying parallel strategy: sending request to model %s (Tier: %d)", profile.ID, profile.Tier)
						// TODO: Implement the actual request to the backend
						// For now, simulate a response
						time.Sleep(time.Duration(profile.ExpectedLatencyMs) * time.Millisecond)
						// Simulate an error
						var err error
						if time.Now().Unix()%3 == 0 {
							err = fmt.Errorf("simulated error for model %s", modelID)
						}
						select {
						case responseChan <- struct {
							ModelID  string
							Response string
							Error    error
						}{ModelID: modelID, Response: fmt.Sprintf("Response from %s", modelID), Error: err}:
						case <-ctx.Done():
							log.Printf("Request to %s cancelled due to timeout", modelID)
							return
						}
					} else {
						log.Printf("Model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)
						select {
						case responseChan <- struct {
							ModelID  string
							Response string
							Error    error
						}{ModelID: modelID, Response: "", Error: fmt.Errorf("model %s does not meet minimum tier requirement (Required: %d, Actual: %d)", modelID, strategy.ModelRequirements.MinimumTier, profile.Tier)}:
						case <-ctx.Done():
							log.Printf("Request to %s cancelled due to timeout", modelID)
							return
						}
					}
				} else {
					log.Printf("Model profile not found: %s", modelID)
					select {
					case responseChan <- struct {
						ModelID  string
						Response string
						Error    error
					}{ModelID: modelID, Response: "", Error: fmt.Errorf("model profile not found: %s", modelID)}:
					case <-ctx.Done():
						log.Printf("Request to %s cancelled due to timeout", modelID)
						return
					}
				}
			}(modelID)
		}

		wg.Wait()
		close(responseChan)

		responses := make([]struct {
			ModelID  string
			Response string
			Error    error
		}, 0)
		for resp := range responseChan {
			responses = append(responses, resp)
		}

		bestResponse := compareResponses(responses)
		if bestResponse.Error == nil {
			// Access ModelID from bestResponse, not profile
			if bestResponse.ModelID != "" {
				if profile, ok := modelProfiles[bestResponse.ModelID]; ok {
					log.Printf("Parallel strategy: selecting model %s (Tier: %d)", profile.ID, profile.Tier)
					routeResp = AIOptimizerRouteResponse{
						SelectedBackendID: profile.ID,
						BackendType:       profile.BackendType,
						BackendURL:        profile.BackendURL,
						PolicyIDApplied:   strategy.PolicyID,
						ModelUsed:         profile.ID,
						TaskType:          strategy.TaskType,
					}
				} else {
					routeResp.Error = fmt.Sprintf("Model profile not found: %s", bestResponse.ModelID)
					log.Println(routeResp.Error)
				}

			} else {
				routeResp.Error = "bestResponse.ModelID is empty"
				log.Println(routeResp.Error)
			}

		} else {
			routeResp.Error = bestResponse.Error.Error()
			log.Println(routeResp.Error)
		}

	} else {
		routeResp.Error = "No parallel models defined for parallel strategy"
		log.Println(routeResp.Error)
	}
	return routeResp
}

// GetModelProfileByID safely retrieves a model profile from the cache.
func GetModelProfileByID(id string) (ModelProfile, bool) {
	mp, ok := modelProfiles[id]
	return mp, ok
}

// compareResponses compares the responses from different models based on the specified method.
func compareResponses(responses []struct {
	ModelID  string
	Response string
	Error    error
}) struct {
	ModelID  string
	Response string
	Error    error
} {
	// Implement different comparison methods (e.g., latency, cost, quality)
	// For now, select the first successful response
	for _, resp := range responses {
		if resp.Error == nil {
			return resp
		}
	}
	// If all responses have errors, return the first error
	if len(responses) > 0 {
		return responses[0]
	}
	// If no responses, return an error
	return struct {
		ModelID  string
		Response string
		Error    error
	}{Error: fmt.Errorf("no responses received from parallel models")}
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, "model_profile:*").Result()
	if err != nil {
		err = fmt.Errorf("failed to get model profile keys from Redis: %w", err)
		log.Println(err)
		return err
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))

	// Run a simple test
	runRoutingTest()

	return nil
}

// loadRoutingStrategiesFromRedis fetches all routing strategies from Redis.
func loadRoutingStrategiesFromRedis(ctx context.Context) error {
	newRoutingStrategies := []RoutingStrategy{}
	keys, err := redisClient.Keys(ctx, "routing_strategy:*").Result()
	if err != nil && err != redis.Nil {
		err = fmt.Errorf("failed to get routing strategy keys from Redis: %w", err)
		log.Println(err)
		return err
	}

	if len(keys) == 0 {
		log.Println("No routing strategies found in Redis.")
		return nil
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting routing strategy %s from Redis: %v", key, err)
			continue
		}
		var strategy RoutingStrategy
		if err := json.Unmarshal([]byte(val), &strategy); err != nil {
			log.Printf("Error unmarshalling routing strategy %s: %v", key, err)
			continue
		}
		// Basic validation of the loaded strategy
		if strategy.Strategy == "" {
			log.Printf("Routing strategy %s has no strategy defined, skipping", key)
			continue
		}
		newRoutingStrategies = append(newRoutingStrategies, strategy)
	}
	routingStrategies = newRoutingStrategies
	log.Printf("Loaded %d routing strategies from Redis.", len(routingStrategies))
	return nil
}

func runRoutingTest() {
	log.Println("Running routing test...")

	// Simulate a few different scenarios
	testCases := []struct {
		TaskType        string
		ModelPriorities []string
		ExpectedModel   string
		MinimumTier     int
	}{
		{
			TaskType:        "simple_task",
			ModelPriorities: []string{"model1", "model2", "model3"},
			ExpectedModel:   "model1",
			MinimumTier:     1,
		},
		{
			TaskType:        "complex_task",
			ModelPriorities: []string{"model3", "model2", "model1"},
			ExpectedModel:   "model3",
			MinimumTier:     2,
		},
		{
			TaskType:        "unsupported_task",
			ModelPriorities: []string{"model1", "model2", "model3"},
			ExpectedModel:   "",
			MinimumTier:     3,
		},
	}

	// Create dummy routing strategy
	strategy := RoutingStrategy{
		ID:       "test_strategy",
		Name:     "Test Strategy",
		Strategy: ROUTING_STRATEGY_DEFAULT,
		ModelRequirements: ModelCapabilityRequirement{
			MinimumTier: 0,
		},
		ModelPriorities: []string{},
		TaskType:        "",
	}

	// Create dummy model profiles
	modelProfiles["model1"] = ModelProfile{ID: "model1", Name: "Model 1", Tier: 1, BackendType: "test", BackendURL: "test"}
	modelProfiles["model2"] = ModelProfile{ID: "model2", Name: "Model 2", Tier: 2, BackendType: "test", BackendURL: "test"}
	modelProfiles["model3"] = ModelProfile{ID: "model3", Name: "Model 3", Tier: 3, BackendType: "test", BackendURL: "test"}

	for _, tc := range testCases {
		strategy.TaskType = tc.TaskType
		strategy.ModelPriorities = tc.ModelPriorities
		strategy.ModelRequirements.MinimumTier = tc.MinimumTier

		// optReq := OptimizationRequest{
		// 	TaskType: tc.TaskType,
		// }

		routeResp := applyDefaultStrategy(strategy, modelProfiles)

		if routeResp.SelectedBackendID != tc.ExpectedModel {
			log.Printf("Test failed for task type %s: expected model %s, got %s", tc.TaskType, tc.ExpectedModel, routeResp.SelectedBackendID)
		} else {
			log.Printf("Test passed for task type %s: selected model %s", tc.TaskType, routeResp.SelectedBackendID)
		}
	}

	log.Println("Routing test completed.")
}
