# LLM Cost-Performance Optimizer: Routing Examples

This document provides practical examples of how routing works in the LLM Cost-Performance Optimizer, including policy definitions, classification, and decision flows.

## Routing Decision Flow

The system follows this routing decision flow:

1. User preference check (if X-Preferred-Backend-ID header is provided)
2. Prompt classification (determining task type)
3. Policy evaluation (finding matching policies based on criteria)
4. Fallback mechanisms (when preferred options aren't available)
5. Caching (for appropriate task types)

## Example 1: User-Preferred Model Routing

When a client explicitly specifies a preferred backend:

**Request:**
```
POST /v1/chat/completions
Content-Type: application/json
X-Preferred-Backend-ID: gemini-pro-mock

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Write a Python function to calculate factorial recursively."}
  ]
}
```

**Routing Decision:**
```
Router: User explicitly preferred LLM 'gemini-pro-mock'. Routing directly.
Selected Backend ID: gemini-pro-mock
Backend Type: google
Backend URL: http://mock-google:8090/v1/models/gemini-pro:generateContent
Policy ID Applied: user-preferred-llm
Task Type: code_generation (inferred from model capabilities)
```

## Example 2: Policy-Based Routing

When a specific policy matches the request criteria:

**Policy Definition:**
```json
{
  "id": "code-to-codellama",
  "name": "Route Code Tasks to CodeLlama",
  "description": "Send all code generation tasks to CodeLlama for optimal performance",
  "criteria": {
    "task_type": "code_generation"
  },
  "action": "ROUTE",
  "backend_id": "codellama-mock",
  "priority": 10,
  "status": "active"
}
```

**Request:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list using bubble sort."}
  ]
}
```

**Classification & Routing:**
```
Classifier: Prompt classified as 'code_generation' using model 'gemini-1.0-pro-classifier'.
Router: Policy 'code-to-codellama' applied. Routing directly to backend: codellama-mock
Selected Backend ID: codellama-mock
Backend Type: vllm
Backend URL: http://mock-codellama:8092/v1/chat/completions
Policy ID Applied: code-to-codellama
```

## Example 3: Optimization-Based Routing

When a policy specifies optimization:

**Policy Definition:**
```json
{
  "id": "optimize-for-factual-queries",
  "name": "Optimize Factual Queries",
  "description": "Use cost/performance optimization for factual queries",
  "criteria": {
    "task_type": "factual_query"
  },
  "action": "OPTIMIZE",
  "priority": 20,
  "status": "active"
}
```

**Request:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "What is the capital of France?"}
  ]
}
```

**Classification & Routing:**
```
Classifier: Prompt classified as 'factual_query' using model 'gemini-1.0-pro-classifier'.
Router: Policy 'optimize-for-factual-queries' applied. Routing to optimal backend from Redis: gemini-2.5-flash-preview-05-20
Selected Backend ID: gemini-2.5-flash-preview-05-20
Backend Type: google
Backend URL: http://mock-google:8090/v1/models/gemini-2.5-flash-preview:generateContent
Policy ID Applied: optimize-for-factual-queries
```

## Example 4: Cache Hit Routing

When a request matches a cached response:

**Request:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "What is the capital of Germany?"}
  ]
}
```

**Classification & Cache Check:**
```
Classifier: Prompt classified as 'factual_query' using model 'gemini-1.0-pro-classifier'.
Router: Cache HIT for prompt 'What is the capital of Germany?'. Serving from cache.
Selected Backend ID: cache
Backend Type: cache
Policy ID Applied: cache-hit
```

## Example 5: Multiple Fallback Levels

When preferred options are unavailable:

**Request:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "gpt-4-turbo",
  "messages": [
    {"role": "user", "content": "Explain quantum computing in simple terms."}
  ]
}
```

**Routing with Fallbacks:**
```
Classifier: Prompt classified as 'complex_reasoning' using model 'gemini-1.0-pro-classifier'.
Router Debug: Client-requested model 'gpt-4-turbo' is NOT active. Skipping.
Router Debug: AI Optimizer optimal backend 'claude-3-opus-********-mock' found in profiles. Status: 'inactive'. Skipping.
Router Debug: Gemini 2.5 Flash (gemini-2.5-flash-preview-05-20) found in profiles. Status: 'active'. SELECTED.
Selected Backend ID: gemini-2.5-flash-preview-05-20
Backend Type: google
Backend URL: http://mock-google:8090/v1/models/gemini-2.5-flash-preview:generateContent
Policy ID Applied: gemini-flash-ultimate-fallback
```

## Task Type Classification

The system classifies prompts into these categories:

1. `factual_query`: Requests for factual information
2. `code_generation`: Requests to write or explain code
3. `creative_writing`: Requests for creative content 
4. `summarization`: Requests to summarize content
5. `translation`: Requests to translate content
6. `sentiment_analysis`: Requests to analyze sentiment
7. `data_extraction`: Requests to extract structured data
8. `simple_chat`: General conversation
9. `long_context`: Requests requiring extensive context
10. `thinking`: Requests requiring reasoning
11. `other`: Fallback category

## Policy Criteria Types

Policies can match on multiple criteria:

1. `user_id`: Specific user identifier
2. `user_roles`: User role/permission level
3. `task_type`: Classified task category
4. `requested_model`: Model specified in request
5. `capability`: Specific model capability
6. `data_sensitivity_level`: Data sensitivity requirement

## Routing Actions

Policies can specify these actions:

1. `ROUTE`: Send directly to a specific backend
2. `OPTIMIZE`: Use the AI Optimizer to select best backend
3. `BLOCK`: Block the request entirely
4. `ALLOW_ACCESS`: Explicitly allow access
5. `DENY_ACCESS`: Explicitly deny access

## Advanced Routing: Capabilities-Based

This example routes based on model capabilities:

**Policy Definition:**
```json
{
  "id": "translation-capability",
  "name": "Translation Capability",
  "description": "Route to models with translation capability",
  "criteria": {
    "task_type": "translation",
    "capability": "translation"
  },
  "action": "ROUTE",
  "backend_id": "gemini-pro-mock", 
  "priority": 5,
  "status": "active"
}
```

## Multi-Turn Conversation Example

Maintaining context across multiple turns:

**First Turn:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "claude-3-opus-********",
  "messages": [
    {"role": "user", "content": "Hi, who are you and what do you do?"}
  ]
}
```

**System Response:**
```
Generated new ID: conv-12345-abcde
```

**Second Turn:**
```
POST /v1/chat/completions
Content-Type: application/json

{
  "conversation_id": "conv-12345-abcde",
  "model": "claude-3-opus-********",
  "messages": [
    {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
  ]
}
```

**Behind the Scenes:**
```
Appended user message to conversation conv-12345-abcde using model claude-3-opus-********
Retrieved previous messages from Redis
Sending full conversation context to LLM backend
