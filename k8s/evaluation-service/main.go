package main

// Run the following command to update dependencies:
// go mod tidy

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"regexp" // Import for regex to extract text
	"strings"
	"time"

	"github.com/google/uuid" // For generating UUIDs for evaluation results
)

// --- Constants ---
const (
	// ClickHouse table for storing evaluation results
	CLICKHOUSE_EVALUATION_RESULTS_TABLE = "llm_evaluation_results"
	CLICKHOUSE_DB                       = "default" // Default ClickHouse database name
)

// --- Structs ---

// EvaluationRequest defines the structure of an incoming request to the evaluation service.
// It should match the data sent by the data-processor for evaluation.
type EvaluationRequest struct {
	RequestID        string          `json:"request_id"`                  // Original request ID from proxy-gateway
	Prompt           string          `json:"prompt"`                      // The prompt that was sent to the LLM
	LLMResponse      string          `json:"llm_response"`                // The response received from the LLM
	ModelID          string          `json:"model_id"`                    // The ID of the model that generated the response
	TaskType         string          `json:"task_type,omitempty"`         // e.g., "factual_query", "creative_gen", "summarization", "code_generation"
	ExpectedResponse string          `json:"expected_response,omitempty"` // Optional: ground truth or expected answer
	EvaluationType   string          `json:"evaluation_type,omitempty"`   // e.g., "accuracy", "fluency", "safety", "relevance"
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`       // Raw metrics from the evaluation (JSON)
	Metadata         json.RawMessage `json:"metadata,omitempty"`          // Additional metadata (JSON)
}

// LLMEvaluationResult defines the structure for storing LLM evaluation results.
// This is the response format from the evaluation service.
type LLMEvaluationResult struct {
	ID               string          `json:"id"`                          // Unique ID for the evaluation result
	RequestID        string          `json:"request_id"`                  // Original request ID from proxy-gateway
	Prompt           string          `json:"prompt"`                      // The prompt that was sent to the LLM
	LLMResponse      string          `json:"llm_response"`                // The response received from the LLM
	ModelID          string          `json:"model_id"`                    // The ID of the model that generated the response
	TaskType         string          `json:"task_type,omitempty"`         // e.g., "factual_query", "creative_gen"
	EvaluationType   string          `json:"evaluation_type"`             // e.g., "accuracy", "fluency", "safety", "relevance"
	Score            float64         `json:"score"`                       // Numerical score (e.g., 0.0 to 1.0)
	Passed           bool            `json:"passed"`                      // Whether the evaluation passed based on criteria
	Feedback         string          `json:"feedback,omitempty"`          // Detailed feedback or reason for failure
	EvaluatedAt      time.Time       `json:"evaluated_at"`                // Timestamp of the evaluation
	ExpectedResponse string          `json:"expected_response,omitempty"` // Optional: ground truth or expected answer
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`       // Raw metrics from the evaluation (JSON)
	Metadata         json.RawMessage `json:"metadata,omitempty"`          // Additional metadata (JSON)
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	http.HandleFunc("/evaluate", handleEvaluate)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8087" // Default port for evaluation-service
	}
	log.Printf("Evaluation Service starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}

// handleEvaluate processes incoming evaluation requests.
func handleEvaluate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is supported", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Error reading request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusInternalServerError)
		return
	}

	var evalReq EvaluationRequest
	if err := json.Unmarshal(body, &evalReq); err != nil {
		log.Printf("Error unmarshalling evaluation request: %v, Body: %s", err, string(body))
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	log.Printf("Received evaluation request for RequestID: %s, Model: %s, TaskType: %s",
		evalReq.RequestID, evalReq.ModelID, evalReq.TaskType)

	log.Printf("Performing evaluation for RequestID: %s", evalReq.RequestID)

	// Perform evaluation based on TaskType
	evalResult := performEvaluation(evalReq)

	w.Header().Set("Content-Type", "application/json")
	// Use StatusCreated (201) to indicate that a new resource (evaluation result) has been created.
	w.WriteHeader(http.StatusCreated)
	if err := json.NewEncoder(w).Encode(evalResult); err != nil {
		log.Printf("Error encoding evaluation response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
	log.Printf("Evaluation complete for RequestID %s. Score: %.2f, Passed: %t", evalReq.RequestID, evalResult.Score, evalResult.Passed)
}

// performEvaluation contains the core evaluation logic.
func performEvaluation(req EvaluationRequest) LLMEvaluationResult {
	result := LLMEvaluationResult{
		ID:          uuid.New().String(),
		RequestID:   req.RequestID,
		Prompt:      req.Prompt,
		LLMResponse: req.LLMResponse,
		ModelID:     req.ModelID,
		EvaluatedAt: time.Now(),
		TaskType:    req.TaskType,
		Metadata:    req.Metadata,
	}

	// Default values
	result.Score = 0.50
	result.Passed = false
	result.Feedback = "Default evaluation: no specific logic applied."
	result.EvaluationType = "generic"

	// Task-specific evaluation logic
	switch req.TaskType {
	case "factual_query":
		result.EvaluationType = "accuracy"
		// Example: Evaluate if response contains expected answer for known prompts
		if strings.Contains(strings.ToLower(req.Prompt), "capital of france") {
			if strings.Contains(strings.ToLower(req.LLMResponse), "paris") {
				result.Score = 0.95
				result.Passed = true
				result.Feedback = "Accurately identified capital of France."
			} else {
				result.Score = 0.10
				result.Passed = false
				result.Feedback = "Failed to identify capital of France."
			}
		} else if strings.Contains(strings.ToLower(req.Prompt), "capital of japan") {
			if strings.Contains(strings.ToLower(req.LLMResponse), "tokyo") {
				result.Score = 0.95
				result.Passed = true
				result.Feedback = "Accurately identified capital of Japan."
			} else {
				result.Score = 0.10
				result.Passed = false
				result.Feedback = "Failed to identify capital of Japan."
			}
		} else {
			result.Feedback += " No specific factual evaluation rule for this prompt."
		}

	case "summarization":
		result.EvaluationType = "conciseness_accuracy"
		originalText := extractTextForSummarizationEval(req.Prompt)
		responseSentences := countSentences(req.LLMResponse)

		expectedSentences := 3 // Both summary prompts ask for 3 sentences

		// Check if response length is as expected
		lengthAdherenceScore := 0.0
		feedbackLength := ""
		if responseSentences == expectedSentences {
			lengthAdherenceScore = 0.4 // High score for exact match
			feedbackLength = "Response has exactly the expected number of sentences. "
		} else if responseSentences >= expectedSentences-1 && responseSentences <= expectedSentences+1 {
			lengthAdherenceScore = 0.2 // Moderate score for close match
			feedbackLength = fmt.Sprintf("Response length (%d sentences) is close to expected %d sentences. ", responseSentences, expectedSentences)
		} else {
			feedbackLength = fmt.Sprintf("Response length (%d sentences) deviates significantly from expected %d sentences. ", responseSentences, expectedSentences)
		}

		// Simple keyword overlap check
		keywords := getKeywords(originalText)
		overlapScore := calculateKeywordOverlap(req.LLMResponse, keywords) * 0.4 // Weight keyword overlap

		result.Score = lengthAdherenceScore + overlapScore + 0.1 // Add a base score for general coherence
		result.Feedback = feedbackLength

		// Determine if passed based on a combined heuristic
		if result.Score >= 0.70 && responseSentences == expectedSentences { // stricter pass for exact sentence count & reasonable score
			result.Passed = true
			result.Feedback += "Summary is concise and contains relevant keywords."
		} else if result.Score >= 0.60 && (responseSentences >= expectedSentences-1 && responseSentences <= expectedSentences+1) {
			result.Passed = true // Slightly lenient if score is good and close to target
			result.Feedback += "Summary is concise and contains relevant keywords (approx. sentence count)."
		} else {
			result.Passed = false
			result.Feedback += "Summary failed to meet quality or conciseness criteria."
		}
		result.Score = min(result.Score, 1.0) // Cap score at 1.0

	case "creative_writing":
		result.EvaluationType = "creativity_coherence"
		// For creative writing, evaluating "correctness" is hard.
		// We can check for basic adherence to the prompt, e.g., "contains paragraphs", "is a story".
		if len(req.LLMResponse) > 100 && strings.Contains(strings.ToLower(req.LLMResponse), "mars") && strings.Contains(strings.ToLower(req.LLMResponse), "discovery") {
			result.Score = 0.75
			result.Passed = true
			result.Feedback = "Response is a reasonable length and appears to be a sci-fi story about Mars."
		} else {
			result.Score = 0.40
			result.Passed = false
			result.Feedback = "Creative writing did not fully adhere to prompt or was too short."
		}

	case "code_generation":
		result.EvaluationType = "code_quality"
		// Example: Check for presence of "def", "return", "import", "docstrings", "type hints"
		score := 0.0 // Start from 0 for code quality
		feedbackParts := []string{}

		if strings.Contains(req.LLMResponse, "def ") && strings.Contains(req.LLMResponse, "return ") {
			score += 0.40 // Significant score for basic function structure
			feedbackParts = append(feedbackParts, "Basic function structure detected.")
		} else {
			feedbackParts = append(feedbackParts, "Missing basic function structure (def/return).")
		}

		if strings.Contains(req.LLMResponse, "'''") || strings.Contains(req.LLMResponse, "\"\"\"") {
			score += 0.30
			feedbackParts = append(feedbackParts, "Docstrings detected.")
		} else {
			feedbackParts = append(feedbackParts, "Missing docstrings.")
		}

		if strings.Contains(req.LLMResponse, ": ") && strings.Contains(req.LLMResponse, " -> ") { // Basic type hint detection
			score += 0.30
			feedbackParts = append(feedbackParts, "Type hints detected.")
		} else {
			feedbackParts = append(feedbackParts, "Missing type hints.")
		}

		result.Score = min(score, 1.0) // Cap score at 1.0

		if result.Score >= 0.85 { // Arbitrary threshold for passing code quality
			result.Passed = true
			result.Feedback = strings.Join(feedbackParts, " ") + " Generated code meets quality standards."
		} else {
			result.Passed = false
			result.Feedback = strings.Join(feedbackParts, " ") + " Generated code needs improvement."
		}

	default:
		// Use default score and feedback
	}

	return result
}

// Helper functions for evaluation logic:

// extractTextForSummarizationEval attempts to extract the text to be summarized from the prompt.
// This is more robust now, handling both formats by looking for quotes or just taking everything after the instruction.
func extractTextForSummarizationEval(prompt string) string {
	// Regex to find content enclosed in single quotes after specific phrases
	re := regexp.MustCompile(`(?:Summarize the following text in \d+ sentences: '|Condense the following content into exactly three concise sentences: ')(.*)'`)
	matches := re.FindStringSubmatch(prompt)
	if len(matches) > 1 {
		return matches[1] // Return the captured group (the text inside quotes)
	}

	// Fallback: If no quotes are found, assume the content is everything after the initial instruction.
	// This is a simple heuristic and might need refinement for more complex prompt structures.
	if strings.Contains(prompt, "Summarize the following text in") {
		parts := strings.SplitN(prompt, ":", 2)
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	}
	if strings.Contains(prompt, "Condense the following content into") {
		parts := strings.SplitN(prompt, ":", 2)
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	}

	return prompt // Fallback to full prompt if extraction fails
}

// countSentences is a simple heuristic for counting sentences.
func countSentences(text string) int {
	// Split by common sentence terminators followed by a space or end of string
	sentences := strings.FieldsFunc(text, func(r rune) bool {
		return r == '.' || r == '!' || r == '?'
	})
	count := 0
	for _, s := range sentences {
		trimmed := strings.TrimSpace(s)
		if len(trimmed) > 0 {
			count++
		}
	}
	return count
}

// getKeywords is a very basic keyword extractor.
func getKeywords(text string) []string {
	// Split by spaces and convert to lowercase. Filter out common stop words.
	words := strings.Fields(strings.ToLower(text))
	var keywords []string
	stopwords := map[string]bool{
		"a": true, "an": true, "the": true, "is": true, "and": true, "or": true, "of": true, "in": true, "to": true, "for": true, "that": true, "it": true, "by": true, "this": true, "sentence": true, "be": true,
	}
	for _, word := range words {
		// Remove punctuation for better keyword matching
		word = strings.Trim(word, ".,!?;:'\"()")
		if !stopwords[word] && len(word) > 2 {
			keywords = append(keywords, word)
		}
	}
	return keywords
}

// calculateKeywordOverlap calculates a simple score based on keyword presence.
func calculateKeywordOverlap(response string, keywords []string) float64 {
	if len(keywords) == 0 {
		return 0.0
	}
	responseLower := strings.ToLower(response)
	matched := 0
	for _, keyword := range keywords {
		if strings.Contains(responseLower, keyword) {
			matched++
		}
	}
	return float64(matched) / float64(len(keywords))
}

// min returns the smaller of two float64 numbers.
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
