# AI Cost-Performance Optimizer: Developer Guide

This guide provides developers with detailed instructions for integrating applications with the AI Cost-Performance Optimizer.

## Integration Overview

The AI Cost-Performance Optimizer simplifies how developers use Large Language Models (LLMs) in their applications. Instead of directly calling various LLM providers (OpenAI, Anthropic, Google, etc.), you send all LLM requests through our intelligent Proxy Gateway, which handles routing, optimization, and policy enforcement transparently.

## Getting Started

### Core Endpoint

The fundamental change for developers is directing all LLM requests to our Proxy Gateway instead of provider-specific endpoints:

**Main API Endpoint for Chat Completions:**
```
POST https://scale-llm.com/v1/chat/completions
```

### Request Format

The request body should conform to the standard LLM Chat Completion API format (following the OpenAI API specification):

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Your prompt here"}
  ],
  "temperature": 0.7,
  "max_tokens": 150
}
```

## Key Features

### Intelligent Routing

Based on configured policies, the proxy automatically selects the most appropriate backend LLM for each request. Routing decisions can be based on:
- Prompt content
- User roles
- Token length
- Performance/cost requirements

### Cost & Performance Optimization

Developers don't need to manually switch between LLMs to find the cheapest or fastest option. The optimizer automatically routes requests based on predefined preferences:
- `OPTIMIZE_LATENCY`: Prioritize response speed
- `OPTIMIZE_COST`: Prioritize lower cost
- `OPTIMIZE_BALANCED`: Balance performance and cost

### Conversational Context Management

For multi-turn conversations, the proxy maintains context automatically:

1. For the first turn, omit `conversation_id` - the proxy will generate one and include it in the response
2. Include this `conversation_id` in subsequent requests
3. The proxy automatically retrieves prior messages before sending to the chosen LLM

### Policy Application

Benefit from centrally defined policies without implementing complex routing logic in your application. Policies can be configured through the admin dashboard.

### Manual Overrides

For specific use cases, explicitly request a particular backend LLM by setting the `X-Preferred-LLM-ID` header.

## API Usage Examples

### Basic Chat Completion

The proxy will automatically select the best LLM based on configured policies:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

### Conversational Turn 1 (New Conversation)

For the first turn, omit `conversation_id`:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
```

The response will contain a `conversation_id` in the payload.

### Conversational Turn 2 (Continuing the Conversation)

Use the `conversation_id` from the first turn:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID_FROM_PREVIOUS_RESPONSE>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'
```

### Manual Model Override

Override automatic routing by specifying a preferred LLM ID:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gpt-4-turbo-us-east" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'
```

**Note:** The `model` field in the request body still acts as a hint or fallback if no policy matches or if the preferred LLM is unavailable.

## Client Libraries

For improved developer experience, we provide client libraries that abstract the HTTP calls and handle the API details.

### Python Client Library

```python
import requests
import json
from typing import List, Dict, Any, Optional

class LLMOptimizerClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.chat_completions_endpoint = f"{self.base_url}/v1/chat/completions"

    def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        conversation_id: Optional[str] = None,
        preferred_llm_id: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Sends a chat completion request to the LLM Optimizer Proxy.

        Args:
            messages (List[Dict[str, str]]): List of message objects.
            model (str): The model name (used as a hint/fallback by the proxy).
            temperature (float): Sampling temperature.
            max_tokens (Optional[int]): Maximum number of tokens to generate.
            conversation_id (Optional[str]): ID to continue a conversation.
            preferred_llm_id (Optional[str]): Forces routing to a specific LLM.
            **kwargs: Additional parameters to pass to the LLM API.

        Returns:
            Dict[str, Any]: The response payload from the LLM.
        """
        payload = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            **kwargs
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if conversation_id:
            payload["conversation_id"] = conversation_id

        headers = {"Content-Type": "application/json"}
        if preferred_llm_id:
            headers["X-Preferred-LLM-ID"] = preferred_llm_id

        try:
            response = requests.post(
                self.chat_completions_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            raise
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            raise
        except Exception as err:
            print(f"An unexpected error occurred: {err}")
            raise
```

### Usage Examples

```python
# Replace with your actual deployed frontend IP
OPTIMIZER_BASE_URL = "https://scale-llm.com"  # Or your actual IP/hostname

client = LLMOptimizerClient(OPTIMIZER_BASE_URL)

# Example 1: Basic Request (Proxy chooses LLM)
try:
    response_basic = client.create_chat_completion(
        messages=[{"role": "user", "content": "What is the capital of France?"}],
        model="gpt-3.5-turbo"  # Hint model
    )
    print("Basic Request Response:", json.dumps(response_basic, indent=2))
    first_conversation_id = response_basic.get("conversation_id") or response_basic.get("id")
    if first_conversation_id:
        print(f"Initial conversation ID: {first_conversation_id}")
except Exception as e:
    print(f"Error during basic request: {e}")

# Example 2: Conversational Request (Turn 2)
if first_conversation_id:
    try:
        response_conv = client.create_chat_completion(
            messages=[{"role": "user", "content": "And what is its population?"}],
            model="gpt-3.5-turbo",
            conversation_id=first_conversation_id
        )
        print("\nConversational Request Response:", json.dumps(response_conv, indent=2))
    except Exception as e:
        print(f"Error during conversational request: {e}")

# Example 3: Request with LLM Override
try:
    response_override = client.create_chat_completion(
        messages=[{"role": "user", "content": "Write a haiku about a coding bug."}],
        model="claude-3-opus-********",  # Hint model
        preferred_llm_id="google-gemini-pro"  # Forces routing to a specific LLM profile ID
    )
    print("\nOverride Request Response:", json.dumps(response_override, indent=2))
except Exception as e:
    print(f"Error during override request: {e}")
```

### Other Available Client Libraries

- **JavaScript/TypeScript**: For Node.js backends or browser-based applications
- **Go**: Native integration for Go services
- **Ruby, Java, C#**: Available based on your development ecosystem

## Benefits of Integration

### "Set it and Forget it" Optimization
Write code once to hit our proxy, and the backend infrastructure automatically handles cost and performance decisions.

### Future-Proofing
As new, better, or cheaper LLMs emerge, we can update backend policies and LLM profiles without requiring any code changes in your applications.

### Simplified LLM Management
No need to manage API keys for multiple LLMs in your own app, or to implement complex failover logic.

### Centralized Observability
Use our dashboard to see how your requests are performing and costing across all LLMs.

### Reduced Development Time
Less boilerplate code for LLM integration.

## Next Steps

- Review the [Setup Guide](setup_guide.md) for installation details
- Explore [Routing Examples](routing_examples.md) for policy configurations
- Learn about the [Cost Model](cost_model.md) for optimization strategies
- Understand the [System Architecture](architecture.md) for integration planning

## Prompt Management API

The Policy Manager provides API endpoints for managing LLM prompts.

**Create a Prompt**
```
POST /prompts
```
**Get All Prompts**
```
GET /prompts
```
**Get a Specific Prompt**
```
GET /prompts/{id}
```
**Update a Prompt**
```
PUT /prompts/{id}
```
**Delete a Prompt**
```
DELETE /prompts/{id}
```

For more details, refer to the Policy Manager documentation.

## Synthetic Data Generation

The data processor includes a synthetic data generation loop that periodically generates synthetic LLM inference data. This data is used for testing and evaluation purposes.

The synthetic data generation loop is configured using the following environment variables:

*   `SYNTHETIC_DATA_GENERATION_MODEL_ID`: Model ID for synthetic data generation.
*   `FEEDBACK_LOOP_INTERVAL`: Interval between synthetic data generation runs.

## Evaluation Results API

The Dashboard API provides endpoints for retrieving evaluation results and curated data.

**Get Evaluation Results**
```
GET /evaluation-results
```
**Get Curated Data**
```
GET /curated-data
```

For more details, refer to the Dashboard API documentation.