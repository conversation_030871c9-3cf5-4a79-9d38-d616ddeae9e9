# mock-backend-gpu2.yaml
# Kubernetes YAML definitions for Mock AI Backend GPU2

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-backend-gpu2
  labels:
    app: mock-backend
    backend-id: mock-backend-gpu2 # Specific label for this backend instance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-backend-gpu2
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-backend-gpu2
        com.docker.compose.service: mock-backend-gpu2 # This label is crucial for data-processor
    spec:
      containers:
      - name: mock-backend-gpu2 # CORRECTED: Container name now matches backend-id
        image: ai-cost-performance-optimizer-mock-backend-gpu2:latest # <--- Placeholder for substitution
        imagePullPolicy: Always
        ports:
        - containerPort: 5002
          name: http-gpu2
        env:
        - name: LISTEN_ADDR
          value: ":5002"
        - name: BACKEND_ID
          value: "mock-backend-gpu2"
        - name: DELAY_MS
          value: "150"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        args: ["mock_backend_gpu2"]
---
apiVersion: v1
kind: Service
metadata:
  name: mock-backend-gpu2 # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-backend-gpu2 # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5002
    targetPort: 5002
  type: ClusterIP # Use ClusterIP for in-cluster access
