# Autonomous Task Decomposition & Planning Engine

The Planning Service is a core component of the AI Cost-Performance Optimizer that enables goal-driven execution and agentic behavior. It transforms high-level user goals into executable task plans and orchestrates their execution across multiple LLM calls and tool invocations.

## Features

### 🎯 Goal-Driven Planning
- **Natural Language Goals**: Accept high-level objectives in natural language
- **Intelligent Parsing**: Extract success criteria, constraints, and context automatically
- **Multi-Strategy Decomposition**: Template-based, LLM-assisted, and hybrid planning approaches

### 🔄 Execution Orchestration
- **Dependency Management**: Handle complex task dependencies and execution order
- **Parallel Execution**: Run independent tasks concurrently for optimal performance
- **Adaptive Retry Logic**: Intelligent retry mechanisms with exponential backoff
- **Resource Management**: Efficient allocation and monitoring of computational resources

### 📊 State Management
- **Persistent Context**: Maintain execution state across multi-step workflows
- **Real-time Tracking**: Monitor progress and intermediate results
- **Recovery Mechanisms**: Resume execution after failures or interruptions

### 🔧 Integration
- **AI Optimizer Integration**: Leverage existing cost optimization and routing
- **Policy Compliance**: Respect governance policies and access controls
- **Analytics Pipeline**: Comprehensive logging and performance metrics

## Quick Start

### 1. Build and Deploy

```bash
# Build the service
cd k8s/planning-service
docker build -t planning-service:latest .

# Deploy to Kubernetes
kubectl apply -f deployment.yaml
```

### 2. Create Your First Goal

```bash
curl -X POST http://planning-service:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "description": "Analyze customer feedback from the last month and generate actionable insights",
    "priority": 8,
    "constraints": [
      {
        "type": "time",
        "description": "Complete within 2 hours",
        "limit": "2h",
        "operator": "<=",
        "severity": "hard"
      }
    ]
  }'
```

### 3. Generate Execution Plan

```bash
# Replace {goal_id} with the ID from step 2
curl -X POST http://planning-service:8080/v1/goals/{goal_id}/plan \
  -H "X-User-ID: your-user-id"
```

### 4. Execute the Plan

```bash
curl -X POST http://planning-service:8080/v1/goals/{goal_id}/execute \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "max_concurrency": 3,
    "timeout": "30m"
  }'
```

### 5. Monitor Progress

```bash
# Check execution status
curl http://planning-service:8080/v1/goals/{goal_id}/status

# View task details
curl http://planning-service:8080/v1/goals/{goal_id}/tasks

# Get final results
curl http://planning-service:8080/v1/goals/{goal_id}/results
```

## API Reference

### Goals

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/v1/goals` | POST | Create a new goal |
| `/v1/goals` | GET | List user's goals |
| `/v1/goals/{id}` | GET | Get goal details |
| `/v1/goals/{id}` | PUT | Update goal |
| `/v1/goals/{id}` | DELETE | Cancel goal |

### Planning

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/v1/goals/{id}/plan` | POST | Generate execution plan |
| `/v1/goals/{id}/plan` | GET | Get current plan |
| `/v1/goals/{id}/replan` | POST | Create new plan |

### Execution

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/v1/goals/{id}/execute` | POST | Start plan execution |
| `/v1/goals/{id}/status` | GET | Get execution status |
| `/v1/goals/{id}/tasks` | GET | List tasks and progress |
| `/v1/goals/{id}/context` | GET | Get execution context |
| `/v1/goals/{id}/results` | GET | Get final results |

### Health

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Service health check |

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8080` | Service port |
| `AI_OPTIMIZER_URL` | `http://ai-optimizer:8080` | AI Optimizer service URL |
| `REDIS_URL` | `redis://redis:6379` | Redis connection string |
| `KAFKA_BROKERS` | `kafka:9092` | Kafka broker addresses |
| `LOG_LEVEL` | `info` | Logging level |

### Task Executors

The service includes built-in executors for common task types:

- **LLM Tasks**: Route through AI Optimizer for cost optimization
- **Data Queries**: Execute database queries and data retrieval
- **API Calls**: Make HTTP requests to external services
- **Analysis**: Perform data analysis and statistical operations
- **Validation**: Verify results against criteria
- **Aggregation**: Combine results from multiple tasks

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Goal Parser   │    │ Task Decomposer  │    │ Execution Engine│
│                 │    │                  │    │                 │
│ • NL Processing │    │ • LLM Planning   │    │ • Orchestration │
│ • Validation    │    │ • Templates      │    │ • Dependencies  │
│ • Enrichment    │    │ • Optimization   │    │ • Monitoring    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ State Manager   │
                    │                 │
                    │ • Persistence   │
                    │ • Context       │
                    │ • Recovery      │
                    └─────────────────┘
```

## Testing

### Run Unit Tests

```bash
go test -v ./...
```

### Run Integration Tests

```bash
# Start dependencies (Redis, Kafka, AI Optimizer)
docker-compose up -d

# Run integration tests
go test -v -tags=integration ./...
```

### Run Benchmarks

```bash
go test -bench=. -benchmem ./...
```

### Load Testing

```bash
# Install dependencies
go install github.com/rakyll/hey@latest

# Test goal creation
hey -n 100 -c 10 -m POST \
  -H "Content-Type: application/json" \
  -H "X-User-ID: load-test" \
  -d '{"description":"Load test goal","priority":5}' \
  http://localhost:8080/v1/goals
```

## Development

### Prerequisites

- Go 1.21+
- Docker
- Kubernetes (for deployment)
- Redis (for state storage)
- Kafka (for event streaming)

### Local Development

```bash
# Clone the repository
git clone <repository-url>
cd k8s/planning-service

# Install dependencies
go mod download

# Run locally
export AI_OPTIMIZER_URL=http://localhost:8080
export PORT=8085
go run .
```

### Adding Custom Task Executors

```go
// Implement the TaskExecutor interface
type CustomExecutor struct{}

func (e *CustomExecutor) CanExecute(taskType TaskType) bool {
    return taskType == "custom_task"
}

func (e *CustomExecutor) Execute(ctx context.Context, task Task, execCtx ExecutionContext) (*TaskResult, error) {
    // Your custom execution logic
    return &TaskResult{
        TaskID:  task.ID,
        Success: true,
        Output:  "Custom result",
    }, nil
}

func (e *CustomExecutor) EstimateResources(task Task) ResourceRequirement {
    return ResourceRequirement{
        CPU:    0.1,
        Memory: 1024 * 1024, // 1MB
        Cost:   0.05,
        Time:   5 * time.Second,
    }
}

// Register the executor
engine.RegisterExecutor(&CustomExecutor{})
```

## Monitoring

### Metrics

The service exposes Prometheus metrics:

- `planning_goals_created_total`: Total goals created
- `planning_execution_duration_seconds`: Plan execution time
- `planning_task_cost_dollars`: Task execution costs
- `planning_success_rate`: Overall success rate

### Logging

Structured JSON logging with configurable levels:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "info",
  "message": "Plan execution completed",
  "goal_id": "goal-123",
  "plan_id": "plan-456",
  "duration_ms": 45000,
  "cost": 2.35,
  "success": true
}
```

## Troubleshooting

### Common Issues

1. **Goal parsing fails**: Check LLM connectivity and prompt formatting
2. **Task execution timeout**: Increase timeout or reduce task complexity
3. **High costs**: Review task decomposition and LLM usage patterns
4. **Memory issues**: Monitor execution context size and implement cleanup

### Debug Mode

```bash
export LOG_LEVEL=debug
./planning-service
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is part of the AI Cost-Performance Optimizer and follows the same licensing terms.
