# --------------------------
# Stage 1: Build the Vite App
# --------------------------
FROM node:20-alpine AS build-step

WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
# No need for ARG or ENV for API_BASE_URL here; frontend uses relative paths.
RUN npm run build -- --mode production --base=/

# --------------------------
# Stage 2: Serve with Nginx
# --------------------------
FROM nginx:alpine

# Copy built app to Nginx's HTML directory
COPY --from=build-step /app/dist /usr/share/nginx/html

# The default nginx:alpine image's main nginx.conf typically includes /etc/nginx/conf.d/*.conf
# We will rely on the ConfigMap to provide default.conf into /etc/nginx/nginx.conf (direct mount)

EXPOSE 80
ENTRYPOINT ["nginx", "-g", "daemon off;"]

