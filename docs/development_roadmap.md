# AI Cost-Performance Optimizer: Development Roadmap

This document outlines the development priorities and next steps for the AI Cost-Performance Optimizer platform, based on the current implementation status and business requirements.

## 1. Intelligent Routing Engine

**Current State:** 
We have a foundational policy management system in `main.go` and `App.jsx` that supports PolicyCriteria (field, operator, value) and Rules (JSON string). This provides a good starting point for a rule-based engine.

**Next Steps:**

- **Prompt Analysis and Optimization in Proxy Gateway** (`main.go`):
  - Enhance the Go proxy gateway to parse incoming prompts
  - Extract features like prompt\_length, keywords, and task\_categories
  - Implement prompt optimization techniques (whitespace trimming, repetition removal)
  - Support prompt templates for reusable prompt patterns

- **Extended Policy Criteria**:
  - Extend the PolicyCriteria in `main.go` to support operators like "prompt length > X" 
  - Add support for "prompt contains 'keyword'" conditions
  - Leverage the existing rules JSON string for more complex, custom logic based on these prompt features

- **Rule Evaluation Logic**: 
  - Enhance core routing logic in `main.go` to evaluate prompt-based criteria
  - Implement efficient backend selection from ModelProfile based on policy matching
  - Implement in-memory and Redis caching to reduce latency and cost
  - Support prompt templates for reusable prompt patterns

## 2. Backend Integrations

**Current State:**
Our ModelProfile already has a backend_url field, designed to point to any LLM API endpoint. The `main.go` proxy can forward requests to this URL.

**Next Steps:**

- **Implement Proxying Logic** (`main.go`):
  - Ensure the proxy can dynamically construct and forward requests to diverse LLM providers
  - Support OpenAI, Google, Anthropic, and vLLM APIs
  - Handle differences in API request/response formats between providers

- **Authentication Management**:
  - Develop secure storage and injection of API keys/credentials
  - Implement per-provider authentication mechanisms

- **Model Profiles for Specific LLMs**:
  - Create ModelProfile entries for:
    - OpenAI (GPT-4o mini/nano)
    - Google (Gemini Flash)
    - Anthropic (Haiku)
    - Generic vLLM instance (for self-hosted option)
  - Include provider-specific metadata:
    - backend_url
    - cost_per_input_token
    - cost_per_output_token
    - expected_latency_ms
    - expected_cost

## 3. Cost & Performance Metrics Dashboard


**Current State:**
The `App.jsx` dashboard displays request count, average latency, total input/output tokens, and total cost over time. The time-series data from ClickHouse provides the foundation. The `k8s/dashboard-api/main.go` provides API endpoints for the dashboard. The `k8s/data-processor/main.go` consumes inference logs from Kafka and inserts them into ClickHouse.

**Next Steps:**

- **Enhance Data Processing** (`k8s/data-processor/main.go`):
  - Implement mechanisms for handling malformed data and ensuring data quality
  - Explore options for data aggregation and summarization to improve dashboard performance

- **Expand Dashboard API** (`k8s/dashboard-api/main.go`):
  - Add API endpoints for retrieving evaluation results and curated data from ClickHouse
  - Implement caching strategies to improve API response times

- **Implement Evaluation Service** (`k8s/evaluation-service/main.go`):
  - [x] Create a service to evaluate LLM responses based on task type
  - [x] Implement evaluation logic for factual queries, summarization, creative writing, and code generation

- **Dashboard Visualization** (`App.jsx`):
  - [x] Enhance UI to display evaluation results and curated data
  - [x] Add filtering and sorting options for all data displayed on the dashboard
## 4. OpenAI API Compatibility

**Current State:**
The `main.go` proxy now accepts generic HTTP requests and supports OpenAI API compatibility.

**Next Steps:**

- **Further Refine Request/Response Transformation** (`main.go`):
  - Continue improving the request/response transformation logic for different LLM providers

- **Model Mapping**:
  - Maintain and update the logic to map OpenAI-style model names to internal ModelProfile IDs

## 5. Prompt Optimization Integration

**Current State:**
Prompt optimization is now integrated into the Proxy Gateway (`main.go`).

**Next Steps:**

- **Enhance Optimization Logic**:
  - Explore more advanced prompt optimization techniques (semantic compression, structure preservation)
  - Implement dynamic adjustment of optimization level based on cost/performance tradeoffs

- **Configuration**:
  - Design user interface for enabling/configuring prompt optimizations in `App.jsx`
  - Consider boolean flags in Policy or ModelProfile for enabling/disabling optimizations

## Immediate Priorities

Given the current foundation, we recommend prioritizing:

1. **Evaluation Service Integration**:
   - [x] Integrate the evaluation service (`k8s/evaluation-service/main.go`) to automatically evaluate LLM responses
   - [x] Modify the data processor (`k8s/data-processor/main.go`) to send evaluation requests and store results
   - [x] Display evaluation results in the dashboard (`App.jsx`)

2. **Intelligent Routing Engine Enhancements** (`main.go`):
   - Adding support for more complex policy criteria (prompt length, keywords)
   - Implementing routing strategies beyond the default strategy (cascade, parallel)
   - Extracting UserID and UserRoles from headers for RBAC

3. **Prompt Optimization**:
   - Enhancing the prompt optimization logic with advanced techniques
   - Providing configuration options for prompt optimization in the UI