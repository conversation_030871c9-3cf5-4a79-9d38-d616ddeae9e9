#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Get the directory where the script is located
# If New_Autostart.sh is at /workspace/k8s/New_Autostart.sh,
# then SCRIPT_DIR will be /workspace/k8s.
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Script execution started."
echo "Script is running from: ${SCRIPT_DIR}"

# Define the root directory of your project based on the script's location
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
echo "Project root: ${PROJECT_ROOT}"

# --- Configuration Variables from Cloud Build Environment ---
# These variables are now read directly as environment variables,
# as they are passed via the 'env:' block in cloudbuild.yaml.
# No need for positional arguments ($1, $2, etc.) here.
# They are automatically available because Cloud Build sets them as ENV vars.

# These are already exported by Cloud Build via the 'env:' block in cloudbuild.yaml.
# We just need to refer to them directly.
echo "GCP_PROJECT_ID: ${GCP_PROJECT_ID}"
echo "GCP_REGION: ${GCP_REGION}"
echo "GKE_CLUSTER_NAME: ${GKE_CLUSTER_NAME}"
echo "ARTIFACT_REGISTRY_REPO: ${ARTIFACT_REGISTRY_REPO}"

# Full image paths (also passed as environment variables)
echo "MOCK_BACKEND_GPU1_IMAGE: ${MOCK_BACKEND_GPU1_IMAGE}"
echo "MOCK_BACKEND_GPU2_IMAGE: ${MOCK_BACKEND_GPU2_IMAGE}"
echo "PROXY_GATEWAY_IMAGE: ${PROXY_GATEWAY_IMAGE}"
echo "FRONTEND_IMAGE: ${FRONTEND_IMAGE}"
echo "KAFKA_TOPIC_CREATOR_IMAGE: ${KAFKA_TOPIC_CREATOR_IMAGE}"
echo "DATA_PROCESSOR_IMAGE: ${DATA_PROCESSOR_IMAGE}"
echo "DASHBOARD_API_IMAGE: ${DASHBOARD_API_IMAGE}"
echo "POLICY_MANAGER_IMAGE: ${POLICY_MANAGER_IMAGE}"
echo "AI_OPTIMIZER_IMAGE: ${AI_OPTIMIZER_IMAGE}"
echo "MOCK_OPENAI_IMAGE: ${MOCK_OPENAI_IMAGE}"
echo "MOCK_GOOGLE_IMAGE: ${MOCK_GOOGLE_IMAGE}"
echo "MOCK_ANTHROPIC_IMAGE: ${MOCK_ANTHROPIC_IMAGE}"

# Also export BUILD_ID for use in creating unique job names
export BUILD_ID="${BUILD_ID}"


# --- Set gcloud project and region (important for subsequent gcloud commands) ---
echo "--- Configuring gcloud ---"
gcloud config set project "${GCP_PROJECT_ID}"
gcloud config set compute/region "${GCP_REGION}"
echo "gcloud project and region configured."

# --- Install necessary tools: yq (kubectl and curl expected to be pre-installed) ---
echo "--- Installing required tools: yq (kubectl and curl are expected to be pre-installed by the builder image) ---"

YQ_FINAL_PATH="/usr/local/bin/yq"
YQ_VERSION="v4.45.4" # Using the latest stable version
YQ_BINARY_NAME="yq_linux_amd64"
YQ_DOWNLOAD_URL="https://github.com/mikefarah/yq/releases/download/${YQ_VERSION}/${YQ_BINARY_NAME}"
TEMP_YQ_PATH=$(mktemp) # Create a temporary file for download

# Install yq
if ! command -v yq &> /dev/null || ! [ -f "${YQ_FINAL_PATH}" ]; then
    echo "yq not found or not present at final path, attempting to install/reinstall from ${YQ_DOWNLOAD_URL}..."

    curl -L --fail-with-body --show-error --retry 5 --retry-delay 10 --output "${TEMP_YQ_PATH}" "${YQ_DOWNLOAD_URL}" || {
        echo "Error: Failed to download yq from ${YQ_DOWNLOAD_URL}."
        echo "Curl exited with status $?."
        echo "Showing potentially downloaded content for debugging (first 20 lines):"
        head -n 20 "${TEMP_YQ_PATH}"
        rm -f "${TEMP_YQ_PATH}" # Clean up temp file
        exit 1
    }

    mv "${TEMP_YQ_PATH}" "${YQ_FINAL_PATH}" || { echo "Failed to move yq to ${YQ_FINAL_PATH}. Exiting."; exit 1; }
    chmod +x "${YQ_FINAL_PATH}" || { echo "Failed to make yq executable. Exiting."; exit 1; }
    echo "yq installed and verified."
else
    echo "yq already found and is presumably a valid executable."
fi
echo "--- Required tools installed ---"


# --- NO API KEY FETCHING FROM SECRET MANAGER HERE ---
# API keys are now injected directly into the redis-populator job via create_redis_populator_job.sh
echo "--- Skipping API Key fetching from Secret Manager in New_Autostart.sh ---"
echo "API keys for model profiles are now populated directly by the redis-populator job."
echo "-------------------------------------------------------------------------"


# --- Kubernetes Manifests Directory for modified files ---
MODIFIED_MANIFESTS_DIR="${SCRIPT_DIR}/modified_manifests"
mkdir -p "${MODIFIED_MANIFESTS_DIR}"

# --- Define exact source paths for each manifest file ---
# Based on your 'ls' output and original script.
# Core Infrastructure
CLICKHOUSE_MANIFEST_SRC="${PROJECT_ROOT}/k8s/clickhouse/clickhouse-k8s.yaml"
KAFKA_SERVER_PROPERTIES_CONFIGMAP_SRC="${PROJECT_ROOT}/k8s/kafka/kafka-server-properties-configmap.yaml"
KAFKA_LOG4J_CONFIGMAP_SRC="${PROJECT_ROOT}/k8s/kafka/kafka-log4j-configmap.yaml"
KAFKA_ZOOKEEPER_MANIFEST_SRC="${PROJECT_ROOT}/k8s/kafka/kafka-zookeeper-k8s.yaml"
REDIS_MANIFEST_SRC="${PROJECT_ROOT}/k8s/redis/redis-k8s.yaml"
PROMETHEUS_MANIFEST_SRC="${PROJECT_ROOT}/k8s/prometheus/prometheus-k8s.yaml"

# Application Services
AI_OPTIMIZER_MANIFEST_SRC="${PROJECT_ROOT}/k8s/ai-optimizer/ai-optimizer.yaml"
DASHBOARD_API_MANIFEST_SRC="${PROJECT_ROOT}/k8s/dashboard-api/dashboard-api.yaml"
POLICY_MANAGER_MANIFEST_SRC="${PROJECT_ROOT}/k8s/policy-manager/policy-manager-k8s.yaml"
PROXY_GATEWAY_MANIFEST_SRC="${PROJECT_ROOT}/k8s/proxy-gateway/proxy-gateway.yaml"
FRONTEND_MANIFEST_SRC="${PROJECT_ROOT}/k8s/frontend/frontend.yaml"
DATA_PROCESSOR_MANIFEST_SRC="${PROJECT_ROOT}/k8s/data-processor/data-processor.yaml"
KAFKA_TOPIC_CREATOR_MANIFEST_SRC="${PROJECT_ROOT}/k8s/kafka_topic_creator/kafka-topic-creator-job.yaml"
MOCK_BACKEND_GPU1_MANIFEST_SRC="${PROJECT_ROOT}/k8s/mock_backend/mock-backend-gpu1.yaml"
MOCK_BACKEND_GPU2_MANIFEST_SRC="${PROJECT_ROOT}/k8s/mock_backend/mock-backend-gpu2.yaml"
MOCK_OPENAI_MANIFEST_SRC="${PROJECT_ROOT}/k8s/mock_backend/mock-openai.yaml"
MOCK_GOOGLE_MANIFEST_SRC="${PROJECT_ROOT}/k8s/mock_backend/mock-google.yaml"
MOCK_ANTHROPIC_MANIFEST_SRC="${PROJECT_ROOT}/k8s/mock_backend/mock-anthropic.yaml"


# --- BEGIN COMPREHENSIVE CLEANUP OF PREVIOUS DEPLOYMENT ---
echo "--- Cleaning up previous Kubernetes deployment (if any) ---"

# List all manifests that will be applied to ensure they are deleted first
# NOTE: Ensure this list matches the manifests applied later in the script
MANIFESTS_TO_DELETE=(
    "${CLICKHOUSE_MANIFEST_SRC}"
    "${KAFKA_ZOOKEEPER_MANIFEST_SRC}"
    "${KAFKA_TOPIC_CREATOR_MANIFEST_SRC}"
    "${DATA_PROCESSOR_MANIFEST_SRC}"
    "${PROXY_GATEWAY_MANIFEST_SRC}"
    "${PROMETHEUS_MANIFEST_SRC}"
    "${DASHBOARD_API_MANIFEST_SRC}"
    "${FRONTEND_MANIFEST_SRC}"
    "${MOCK_BACKEND_GPU1_MANIFEST_SRC}"
    "${MOCK_BACKEND_GPU2_MANIFEST_SRC}"
    "${MOCK_OPENAI_MANIFEST_SRC}"
    "${MOCK_GOOGLE_MANIFEST_SRC}"
    "${MOCK_ANTHROPIC_MANIFEST_SRC}"
    "${KAFKA_SERVER_PROPERTIES_CONFIGMAP_SRC}"
    "${KAFKA_LOG4J_CONFIGMAP_SRC}"
    "${REDIS_MANIFEST_SRC}"
    "${POLICY_MANAGER_MANIFEST_SRC}"
    "${AI_OPTIMIZER_MANIFEST_SRC}"
)

echo "Attempting to delete existing Kubernetes resources from manifests..."
for manifest in "${MANIFESTS_TO_DELETE[@]}"; do
    if [ -f "$manifest" ]; then
        echo "Deleting resources from $manifest..."
        kubectl delete -f "$manifest" --ignore-not-found --force --grace-period=0 || {
            echo "Warning: Failed to delete resources from $manifest. Check kubectl output above."
        }
    else
        echo "Warning: Manifest file not found (for deletion): $manifest. Skipping deletion for this file."
    fi
done
echo "Attempted deletion of specified Kubernetes resources from manifests."

# Also attempt to delete specific resources by name if they are not covered by manifests
echo "Attempting to delete specific secrets and configmaps by name..."
# Removed API key secrets as they are no longer created by this script
kubectl delete secret clickhouse-credentials --ignore-not-found=true
kubectl delete secret gcp-service-account-key --ignore-not-found=true
kubectl delete configmap proxy-gateway-config --ignore-not-found=true

# Explicitly delete jobs by name if they are not covered by the manifest deletion loop (e.g., if you only deploy Jobs via kubectl apply -f the file, not managing their state via manifest itself during subsequent applies)
kubectl delete job kafka-topic-creator --ignore-not-found --grace-period=0 --force || true
echo "Attempted to delete existing kafka-topic-creator Job by name."

# Also delete any lingering redis-populator jobs
echo "Attempting to delete any lingering redis-populator jobs..."
kubectl delete job -l app=redis-populator --ignore-not-found --grace-period=0 --force || true
echo "Attempted to delete lingering redis-populator jobs."

echo "--- Cleanup Complete ---"
# --- END COMPREHENSIVE CLEANUP ---


# --- Workload Identity Setup for Policy Manager ---
echo "--- Setting up Workload Identity for Policy Manager ---"

POLICY_MANAGER_KSA_NAME="policy-manager-ksa"
POLICY_MANAGER_GSA_NAME="policy-manager-gsa"

echo "Creating Google Service Account ${POLICY_MANAGER_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${POLICY_MANAGER_GSA_NAME}" \
        --display-name="Service Account for Policy Manager"
else
    echo "Google Service Account ${POLICY_MANAGER_GSA_NAME} already exists."
fi

# NOTE: If Policy Manager needs other GCP services later, grant specific roles here.
# For Redis interaction, it usually doesn't need GCP IAM roles itself.

echo "Creating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}..."
kubectl create serviceaccount "${POLICY_MANAGER_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}..."
kubectl annotate serviceaccount "${POLICY_MANAGER_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite || true

echo "Binding GSA ${POLICY_MANAGER_GSA_NAME} to KSA ${POLICY_MANAGER_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${POLICY_MANAGER_KSA_NAME}]" \
    --condition=None || true

echo "--- Workload Identity Setup for Policy Manager Complete ---"


# --- Workload Identity Setup for Dashboard API ---
echo "--- Setting up Workload Identity for Dashboard API ---"

DASHBOARD_API_KSA_NAME="dashboard-api-ksa"
DASHBOARD_API_GSA_NAME="dashboard-api-gsa"

echo "Creating Google Service Account ${DASHBOARD_API_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${DASHBOARD_API_GSA_NAME}" \
        --display-name="Service Account for Dashboard API"
else
    echo "Google Service Account ${DASHBOARD_API_GSA_NAME} already exists."
fi

# NOTE: If Dashboard API needs other GCP services (e.g., ClickHouse, but you use direct connection), grant specific roles here.
# For ClickHouse interaction, it usually doesn't need GCP IAM roles itself unless ClickHouse is on a GCP managed service.

echo "Creating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}..."
kubectl create serviceaccount "${DASHBOARD_API_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}..."
kubectl annotate serviceaccount "${DASHBOARD_API_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite || true

echo "Binding GSA ${DASHBOARD_API_GSA_NAME} to KSA ${DASHBOARD_API_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${DASHBOARD_API_KSA_NAME}]" \
    --condition=None || true

echo "--- Workload Identity Setup for Dashboard API Complete ---"


# --- Create Kubernetes Secrets and ConfigMaps (non-GCP managed, or those explicitly created) ---
echo "--- Creating Kubernetes Secrets and ConfigMaps ---"

# ClickHouse Credentials Secret
SECRET_NAME="clickhouse-credentials"
SECRET_USER="test"
SECRET_PASSWORD="test"
SECRET_CLICKHOUSE_DB="default"

echo "Checking if secret '${SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${SECRET_NAME}" \
            --from-literal=user="${SECRET_USER}" \
            --from-literal=password="${SECRET_PASSWORD}" \
            --from-literal=clickhouse_db="${SECRET_CLICKHOUSE_DB}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create ClickHouse credentials secret. Exiting."
      exit 1
    fi
    echo "Secret '${SECRET_NAME}' created."
fi

# Proxy Gateway ConfigMap from local file
PROXY_GATEWAY_CONFIG_PATH="${PROJECT_ROOT}/k8s/proxy-gateway/config.yaml" # Corrected path for config.yaml
CONFIGMAP_NAME="proxy-gateway-config"

echo "Creating ConfigMap '${CONFIGMAP_NAME}' from ${PROXY_GATEWAY_CONFIG_PATH}..."
if [ -f "${PROXY_GATEWAY_CONFIG_PATH}" ]; then
    # Delete existing configmap if it exists, as configmaps are often immutable
    kubectl delete configmap "${CONFIGMAP_NAME}" --ignore-not-found --grace-period=0 --force || true
    echo "Attempted to delete existing ${CONFIGMAP_NAME} ConfigMap."

    kubectl create configmap "${CONFIGMAP_NAME}" --from-file="${PROXY_GATEWAY_CONFIG_PATH}" --dry-run=client -o yaml | kubectl apply -f -
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create ConfigMap '${CONFIGMAP_NAME}'. Exiting."
        exit 1
    fi
    echo "ConfigMap '${CONFIGMAP_NAME}' created."
else
    echo "Error: Proxy Gateway config.yaml not found at ${PROXY_GATEWAY_CONFIG_PATH}. Cannot create ConfigMap. Exiting."
    exit 1
fi

echo "--- Kubernetes Secrets and ConfigMaps Creation Complete ---"

# --- API Key Secrets creation removed from here ---
# They are now directly passed to the redis-populator job.
# The policy-manager will read them from Redis after the populator runs.
echo "--- API Key Secrets creation skipped in New_Autostart.sh ---"
echo "--- They are handled by the redis-populator job directly. ---"
echo "------------------------------------------------------------"


# --- Kubernetes Manifests Directory for modified files ---
MODIFIED_MANIFESTS_DIR="${SCRIPT_DIR}/modified_manifests"
mkdir -p "${MODIFIED_MANIFESTS_DIR}"

# --- DEBUGGING STEP: Verify all assumed manifest source paths ---
echo "--- DEBUG: Verifying assumed manifest source paths ---"
for manifest_path in \
    "${CLICKHOUSE_MANIFEST_SRC}" \
    "${KAFKA_SERVER_PROPERTIES_CONFIGMAP_SRC}" \
    "${KAFKA_LOG4J_CONFIGMAP_SRC}" \
    "${KAFKA_ZOOKEEPER_MANIFEST_SRC}" \
    "${REDIS_MANIFEST_SRC}" \
    "${PROMETHEUS_MANIFEST_SRC}" \
    "${AI_OPTIMIZER_MANIFEST_SRC}" \
    "${DASHBOARD_API_MANIFEST_SRC}" \
    "${POLICY_MANAGER_MANIFEST_SRC}" \
    "${PROXY_GATEWAY_MANIFEST_SRC}" \
    "${FRONTEND_MANIFEST_SRC}" \
    "${DATA_PROCESSOR_MANIFEST_SRC}" \
    "${KAFKA_TOPIC_CREATOR_MANIFEST_SRC}" \
    "${MOCK_BACKEND_GPU1_MANIFEST_SRC}" \
    "${MOCK_BACKEND_GPU2_MANIFEST_SRC}" \
    "${MOCK_OPENAI_MANIFEST_SRC}" \
    "${MOCK_GOOGLE_MANIFEST_SRC}" \
    "${MOCK_ANTHROPIC_MANIFEST_SRC}"; do
    if [ ! -f "$manifest_path" ]; then
        echo "Error: Manifest file not found: ${manifest_path}. This file must exist in your repository. Please check its name and path."
        exit 1
    else
        echo "Found: ${manifest_path}"
    fi
done
echo "-----------------------------------------------------"


# Function to perform sed replacement safely (handling different sed versions)
perform_sed_replacement() {
  local filename=$1
  local search_string=$2
  local replace_string=$3

  # Detect if gnu-sed is available (often 'gsed' on macOS, 'sed' on Linux)
  if which gsed >/dev/null; then
    gsed -i "s|${search_string}|${replace_string}|g" "${filename}"
  else
    sed -i "s|${search_string}|${replace_string}|g" "${filename}"
  fi
}

# --- Modify Kubernetes Manifests with Image Paths ---
echo "--- Modifying Kubernetes Manifests with Image Paths ---"

# Create temporary manifest files for modification by copying from their respective source paths
# Core Infrastructure
cp "${CLICKHOUSE_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/clickhouse-k8s.yaml"
cp "${KAFKA_SERVER_PROPERTIES_CONFIGMAP_SRC}" "${MODIFIED_MANIFESTS_DIR}/kafka-server-properties-configmap.yaml"
cp "${KAFKA_LOG4J_CONFIGMAP_SRC}" "${MODIFIED_MANIFESTS_DIR}/kafka-log4j-configmap.yaml"
cp "${KAFKA_ZOOKEEPER_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/kafka-zookeeper-k8s.yaml"
cp "${REDIS_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/redis-k8s.yaml"
cp "${PROMETHEUS_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/prometheus-k8s.yaml"

# Application Services (using their corrected names for target files)
cp "${AI_OPTIMIZER_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/ai-optimizer.yaml"
cp "${DASHBOARD_API_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/dashboard-api.yaml"
cp "${POLICY_MANAGER_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/policy-manager-k8s.yaml"
cp "${PROXY_GATEWAY_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/proxy-gateway.yaml"
cp "${FRONTEND_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/frontend.yaml"
cp "${DATA_PROCESSOR_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/data-processor.yaml"
cp "${KAFKA_TOPIC_CREATOR_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/kafka-topic-creator-job.yaml"
cp "${MOCK_BACKEND_GPU1_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu1.yaml"
cp "${MOCK_BACKEND_GPU2_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu2.yaml"
cp "${MOCK_OPENAI_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/mock-openai.yaml"
cp "${MOCK_GOOGLE_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/mock-google.yaml"
cp "${MOCK_ANTHROPIC_MANIFEST_SRC}" "${MODIFIED_MANIFESTS_DIR}/mock-anthropic.yaml"


# Replace image placeholders with actual image paths.
# The placeholder strings here MUST exactly match what's in your YAML files.
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/ai-optimizer.yaml" "ai-cost-performance-optimizer-ai-optimizer:latest" "${AI_OPTIMIZER_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/dashboard-api.yaml" "ai-cost-performance-optimizer-dashboard-api:latest" "${DASHBOARD_API_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/policy-manager-k8s.yaml" "ai-cost-performance-optimizer-policy-manager:latest" "${POLICY_MANAGER_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/proxy-gateway.yaml" "ai-cost-performance-optimizer-proxy-gateway:latest" "${PROXY_GATEWAY_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/frontend.yaml" "ai-cost-performance-optimizer-frontend:latest" "${FRONTEND_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/data-processor.yaml" "ai-cost-performance-optimizer-data-processor:latest" "${DATA_PROCESSOR_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/kafka-topic-creator-job.yaml" "ai-cost-performance-optimizer-kafka-topic-creator:latest" "${KAFKA_TOPIC_CREATOR_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu1.yaml" "ai-cost-performance-optimizer-mock-backend-gpu1:latest" "${MOCK_BACKEND_GPU1_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu2.yaml" "ai-cost-performance-optimizer-mock-backend-gpu2:latest" "${MOCK_BACKEND_GPU2_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/mock-openai.yaml" "ai-cost-performance-optimizer-mock-openai:latest" "${MOCK_OPENAI_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/mock-google.yaml" "ai-cost-performance-optimizer-mock-google:latest" "${MOCK_GOOGLE_IMAGE}"
perform_sed_replacement "${MODIFIED_MANIFESTS_DIR}/mock-anthropic.yaml" "ai-cost-performance-optimizer-mock-anthropic:latest" "${MOCK_ANTHROPIC_IMAGE}"

echo "Kubernetes manifests modified with image paths."


# --- Workload Identity Service Account Name Injection ---
# IMPORTANT: This must be done AFTER copying to MODIFIED_MANIFESTS_DIR and BEFORE applying.
# It ensures the Deployment/Pod is linked to the correct KSA for Workload Identity.

POLICY_MANAGER_KSA_NAME="policy-manager-ksa"
DASHBOARD_API_KSA_NAME="dashboard-api-ksa"

echo "Injecting serviceAccountName into policy-manager and dashboard-api manifests..."

# Modify policy-manager-k8s.yaml for Workload Identity Service Account
TEMP_POLICY_MANAGER_MANIFEST="${MODIFIED_MANIFESTS_DIR}/policy-manager-k8s.yaml"
# Insert 'serviceAccountName' after the 'spec:' line within the 'template' block.
# This sed command assumes 'spec:' is indented by 4 spaces (standard for Deployment.spec.template.spec).
# The inserted line 'serviceAccountName:' will be indented by 6 spaces.
perform_sed_replacement "${TEMP_POLICY_MANAGER_MANIFEST}" \
  "    spec:" \
  "    spec:\n      serviceAccountName: ${POLICY_MANAGER_KSA_NAME}"

echo "--- DEBUG: Content of modified policy-manager-k8s.yaml after KSA injection ---"
cat "${TEMP_POLICY_MANAGER_MANIFEST}"
echo "-----------------------------------------------------------------------------"


# Modify dashboard-api.yaml for Workload Identity Service Account
TEMP_DASHBOARD_API_MANIFEST="${MODIFIED_MANIFESTS_DIR}/dashboard-api.yaml"
# Same logic for dashboard-api
perform_sed_replacement "${TEMP_DASHBOARD_API_MANIFEST}" \
  "    spec:" \
  "    spec:\n      serviceAccountName: ${DASHBOARD_API_KSA_NAME}"

echo "--- DEBUG: Content of modified dashboard-api.yaml after KSA injection ---"
cat "${TEMP_DASHBOARD_API_MANIFEST}"
echo "-----------------------------------------------------------------------"


echo "Workload Identity serviceAccountName injected into relevant manifests."

# --- Frontend API URL Environment Variable Injection using yq ---
# IMPORTANT: This must be done AFTER copying to MODIFIED_MANIFESTS_DIR and BEFORE applying.
# It ensures the frontend knows the correct internal service URLs for its API calls.

echo "Injecting API_URL environment variables into frontend manifest using yq..."

FRONTEND_MANIFEST="${MODIFIED_MANIFESTS_DIR}/frontend.yaml"

# Use yq to add the environment variables to the frontend container, targeting ONLY the Deployment kind.
# This is crucial for avoiding 'unknown field "spec.template"' errors on Service or ConfigMap objects.
yq e 'select(.kind == "Deployment").spec.template.spec.containers[0].env = ([.spec.template.spec.containers[0].env // []] | .[] + [{"name": "REACT_APP_POLICY_MANAGER_API_URL", "value": "http://policy-manager:8081"}, {"name": "REACT_APP_DASHBOARD_API_URL", "value": "http://dashboard-api:8082"}])' -i "${FRONTEND_MANIFEST}"

echo "--- DEBUG: Content of modified frontend.yaml after API URL injection ---"
cat "${FRONTEND_MANIFEST}"
echo "-----------------------------------------------------------------------"

echo "Frontend API URLs injected into manifest using yq."


# --- Apply Kubernetes Manifests in a dependency-aware sequence ---
echo "--- Applying Kubernetes Manifests in a dependency-aware sequence ---"

echo "Ensuring kubectl context is GKE before applying manifests..."
if ! kubectl config current-context | grep -q "${GKE_CLUSTER_NAME}"; then
  echo "Error: kubectl context is not set to ${GKE_CLUSTER_NAME}. Exiting."
  exit 1
fi

# --- Stage 1: Core Data Infrastructure (Databases, Message Queues, Monitoring, Mock Backends) ---
echo "Applying core data infrastructure (ClickHouse, Kafka, Redis, Prometheus, Mock Backends)..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/clickhouse-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-server-properties-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-log4j-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-zookeeper-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/redis-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/prometheus-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu1.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu2.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-openai.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-google.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-anthropic.yaml"

echo "Waiting for core infrastructure to be available..."
kubectl wait --for=condition=Available deployment/clickhouse --timeout=600s
kubectl wait --for=condition=Available deployment/zookeeper --timeout=600s
kubectl wait --for=condition=Available deployment/kafka --timeout=600s
kubectl wait --for=condition=Available deployment/redis --timeout=600s
kubectl wait --for=condition=Available deployment/prometheus --timeout=600s
kubectl wait --for=condition=Available deployment/mock-backend-gpu1 --timeout=600s
kubectl wait --for=condition=Available deployment/mock-backend-gpu2 --timeout=600s
kubectl wait --for=condition=Available deployment/mock-openai --timeout=600s
kubectl wait --for=condition=Available deployment/mock-google --timeout=600s
kubectl wait --for=condition=Available deployment/mock-anthropic --timeout=600s
echo "Core infrastructure deployments are ready."

### Stage 1.5: Drop ClickHouse Table (per initial plan)
echo "--- Stage 1.5: Dropping ClickHouse table 'inference_logs' for a clean slate ---"
# Make sure drop_clickhouse_table.sh is executable and has correct paths
"${SCRIPT_DIR}/scripts/drop_clickhouse_table.sh"
echo "ClickHouse table drop attempt completed via drop_clickhouse_table.sh."

# --- Stage 2: Kafka Topic Creation ---
echo "Applying Kafka Topic Creator Job..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-topic-creator-job.yaml"

echo "Waiting for Kafka Topic Creator Job to complete..."
kubectl wait --for=condition=complete job/kafka-topic-creator --timeout=600s
echo "Kafka topics created."

# --- Stage 3: Data Processor Deployment ---
echo "Applying Data Processor deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/data-processor.yaml"

echo "Waiting for Data Processor to be available and ensure ClickHouse table..."
kubectl wait --for=condition=Available deployment/data-processor --timeout=600s
sleep 30
echo "Data Processor is ready and likely created the inference_logs table."

# --- Stage 4: Reset Kafka Offset ---
echo "Executing Kafka offset reset for data-processor-group..."
KAFKA_POD=$(kubectl get pod -l app=kafka -o jsonpath='{.items[0].metadata.name}' || true)
if [ -z "$KAFKA_POD" ]; then
  echo "Warning: Kafka pod not found, cannot reset offsets."
else
  kubectl exec "$KAFKA_POD" -- /usr/bin/kafka-consumer-groups \
    --bootstrap-server localhost:9092 \
    --group data-processor-group \
    --topic inference-logs \
    --reset-offsets --to-earliest --execute
  echo "Kafka offset reset completed for data-processor-group on inference-logs."
fi

# --- Stage 5: AI Optimizer Deployment ---
echo "Applying AI Optimizer deployment (including SA, Role, RoleBinding)..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/ai-optimizer.yaml"

echo "Waiting for AI Optimizer to be available..."
kubectl wait --for=condition=Available deployment/ai-optimizer --timeout=600s
echo "AI Optimizer is ready."

# --- Stage 6: Other Dependent Applications (APIs, Gateways, Frontends) ---
echo "Applying other dependent applications (Dashboard API, Proxy Gateway, Frontend)..." # Removed Policy Manager from this list
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/dashboard-api.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/proxy-gateway.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/frontend.yaml"

echo "Waiting for dashboard-api, proxy-gateway, frontend deployments to be available..."
kubectl wait --for=condition=Available deployment/dashboard-api --timeout=600s
kubectl wait --for=condition=Available deployment/proxy-gateway --timeout=600s
kubectl wait --for=condition=Available deployment/frontend-dashboard --timeout=600s
echo "All dashboard-api, proxy-gateway, frontend deployments are ready."

# --- Policy Manager Deployment (Moved to its own stage so it can be restarted) ---
echo "Applying Policy Manager deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/policy-manager-k8s.yaml"

echo "Waiting for policy-manager deployment to be available..."
kubectl wait --for=condition=Available deployment/policy-manager --timeout=600s
echo "Policy Manager deployment is ready."


# --- Redis Populator Job Creation (ADDED TO THE END OF New_Autostart.sh) ---
# This is now the ONLY place where the redis-populator-job should be created.
# It needs to happen AFTER Redis is fully up and ready.
echo "--- Starting Redis Populator Job creation (within New_Autostart.sh) ---"

# Ensure the script has execute permissions before running it
chmod +x "${SCRIPT_DIR}/scripts/create_redis_populator_job.sh"

# These variables should already be available in the environment of New_Autostart.sh
# from the 'env:' block in cloudbuild.yaml (Step 'Deploy to GKE').
# We explicitly export them here to make them available to the create_redis_populator_job.sh script.
export GCP_PROJECT_ID="${GCP_PROJECT_ID}"
export GCP_REGION="${GCP_REGION}"
export ARTIFACT_REGISTRY_REPO="${ARTIFACT_REGISTRY_REPO}"
export BUILD_ID="${BUILD_ID}" # Use the BUILD_ID that Cloud Build provides
export GKE_CLUSTER_NAME="${GKE_CLUSTER_NAME}"

# Execute the create_redis_populator_job.sh script
# This script itself will create the job and wait for its completion.
"${SCRIPT_DIR}/scripts/create_redis_populator_job.sh"

echo "--- Redis Populator Job creation (within New_Autostart.sh) complete ---"

# --- RESTART POLICY MANAGER AFTER REDIS POPULATOR ---
# This ensures policy-manager re-reads the Redis data with the correct API keys.
echo "--- Restarting Policy Manager to pick up updated API keys from Redis ---"
kubectl rollout restart deployment/policy-manager
echo "Policy Manager rollout restart initiated. Waiting for it to be ready..."
kubectl wait --for=condition=Available deployment/policy-manager --timeout=300s
echo "Policy Manager restarted and is ready."

echo "--- Kubernetes Manifest Application Complete ---"

echo "--- Setup Complete ---"
echo "Your application components are being deployed to GKE."
echo "You can check the status of your pods with: kubectl get pods"
echo "You can check the status of your services with: kubectl get services"

echo "To access services exposed via LoadBalancer (e.g., proxy-gateway, frontend-dashboard, prometheus):"
echo "Run: kubectl get services -o wide"
echo "Look for the EXTERNAL-IP of the desired service. It might take a few minutes for the IP to provision."

echo "To access Policy Manager API (if exposed via LoadBalancer):"
echo "kubectl get service policy-manager -o jsonpath='{.status.loadBalancer.ingress[0].ip}'"

echo "Script execution finished."

