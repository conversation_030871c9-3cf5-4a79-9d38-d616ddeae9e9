# proxy-gateway-config.yaml
# Kubernetes ConfigMap for the Proxy Gateway's configuration

apiVersion: v1
kind: ConfigMap
metadata:
  name: proxy-gateway-config # Name for the ConfigMap
data:
  config.yaml: |
    # Configuration for the AI Cost & Performance Optimization Proxy Gateway

    # The address and port the proxy will listen on *inside the container*
    listen_addr: ":8080" # Listen internally on port 8080

    # List of backend AI model endpoints (using Kubernetes service names)
    backends:
      - id: "mock-backend-gpu1"
        url: "http://mock-backend-gpu1:5001" # Use the Kubernetes service name and internal port
        weight: 70
        cost: 0.0000001
        latency: 100

      - id: "mock-backend-gpu2"
        url: "http://mock-backend-gpu2:5002" # Use the Kubernetes service name and internal port
        weight: 30
        cost: 0.00000015
        latency: 150

    # Kafka Configuration for logging inference events (using Kubernetes service name)
    kafka:
      brokers: ["kafka:9092"] # Use the Kafka service name and internal port
      topic: "inference-logs"

    # Redis Configuration for fetching live latency metrics (using Kubernetes service name)
    redis:
      addr: "redis:6379" # Use the Redis service name and internal port
      password: "" # No password for Redis in this MVP (adjust if you secure Redis)
      db: 0 # Default Redis database

