<!DOCTYPE html>
<html>
<head>
    <title>API Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    
    <div>
        <button onclick="testApi('/api/policies')">Test Policies API</button>
        <button onclick="testApi('/api/model-profiles')">Test Model Profiles API</button>
        <button onclick="testApi('/api/summary')">Test Summary API</button>
        <button onclick="testApi('/api/time-series')">Test Time Series API</button>
        <button onclick="testApi('/api/recent-logs')">Test Recent Logs API</button>
        <button onclick="testApi('/api/backend-latencies')">Test Backend Latencies API</button>
        <button onclick="testApi('/api/optimal-backend')">Test Optimal Backend API</button>
    </div>
    
    <h2>Results:</h2>
    <pre id="results">Click a button to test an API endpoint</pre>
    
    <script>
        async function testApi(endpoint) {
            const resultsElement = document.getElementById('results');
            resultsElement.textContent = `Testing ${endpoint}...`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(endpoint, {
                    headers: {
                        'Cache-Control': 'no-cache',
                        'X-Debug-Timestamp': Date.now().toString()
                    }
                });
                const endTime = Date.now();
                
                const statusLine = `Status: ${response.status} ${response.statusText}`;
                const timeLine = `Time: ${endTime - startTime}ms`;
                
                let responseText;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    responseText = JSON.stringify(data, null, 2);
                } else {
                    responseText = await response.text();
                }
                
                resultsElement.textContent = `${statusLine}\n${timeLine}\n\nResponse:\n${responseText}`;
            } catch (error) {
                resultsElement.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>