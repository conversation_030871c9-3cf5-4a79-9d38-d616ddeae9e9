# AI Cost-Performance Optimizer Architecture

## System Overview

The AI Cost-Performance Optimizer is an intelligent platform that provides both optimized LLM routing and autonomous task execution capabilities. The system operates in two modes:

1. **Direct LLM Optimization**: Intelligent proxy that routes individual LLM requests to optimal backends
2. **Autonomous Workflow Execution**: Goal-driven planning engine that decomposes complex objectives into executable task workflows

The platform makes real-time decisions about LLM routing and task orchestration based on dynamic data, user preferences, configurable policies, and autonomous planning algorithms.

## Core Components

The system uses a microservices architecture deployed on Kubernetes with these key components:

### 0. Planning Service (Autonomous Execution Engine) 🆕

**Purpose**: Enables goal-driven autonomous execution of complex multi-step workflows.

**Key Functions**:
- **Goal Parsing**: Converts natural language objectives into structured goals with success criteria and constraints
- **Task Decomposition**: Breaks down complex goals into executable tasks using LLM-assisted planning and templates
- **Execution Orchestration**: Manages task dependencies, parallel execution, and resource allocation
- **State Management**: Maintains persistent execution context across multi-step workflows
- **Progress Monitoring**: Provides real-time tracking of goal execution and task completion
- **Adaptive Planning**: Supports re-planning and failure recovery during execution

**Technical Details**:
- Exposes RESTful API endpoints for goal management (/v1/goals, /v1/goals/{id}/plan, /v1/goals/{id}/execute)
- Integrates with AI Optimizer for cost-optimized LLM routing during task execution
- Supports multiple task types: LLM calls, data queries, API calls, analysis, validation, aggregation
- Implements dependency-aware scheduling with parallel execution capabilities
- Uses Redis for state persistence and recovery mechanisms

### 1. Proxy Gateway (Entry Point)

**Purpose**: Acts as the primary entry point for both direct LLM requests and planning workflows.

**Key Functions**:
- **Direct LLM Mode**: Intercepts and routes individual LLM requests for cost optimization
- **Planning Integration**: Routes planning-related requests to the Planning Service
- Communicates with the AI Optimizer for intelligent routing decisions
- Maintains conversation context across multiple turns in Redis
- Applies user-controlled routing overrides via headers (X-Preferred-Backend-ID)
- Handles request/response transformation between different LLM APIs
- Proxies requests to the chosen LLM backend with appropriate authentication
- Implements response caching for frequently asked queries
- Calculates tokens and costs for each request
- Publishes comprehensive inference logs to Kafka for analysis

**Technical Details**:
- Exposes unified API endpoints (/v1/chat/completions for direct LLM, /v1/goals/* for planning)
- Compatible with OpenAI, Google, and Anthropic request formats
- Handles both streaming and non-streaming responses
- Processes conversation_id for maintaining multi-turn context
- Routes planning requests to Planning Service for autonomous execution

### 2. AI Optimizer (Intelligence Hub)

**Purpose**: Makes intelligent routing decisions for both direct LLM requests and planning-driven tasks.

**Key Functions**:
- **Direct LLM Routing**: Classifies prompts and routes individual requests to optimal backends
- **Planning-Aware Routing**: Optimizes LLM calls within autonomous workflows for cost and performance
- Uses a tiered classification system with primary, secondary, and fallback classifiers
- Evaluates routing policies against requests with priority-based processing
- Applies user preferences via X-Preferred-LLM-ID header
- Performs dynamic routing optimization based on latency or cost metrics
- **Workflow Optimization**: Considers entire goal execution for cost optimization decisions
- Maintains optimal backend selections in Redis
- Implements sophisticated fallback mechanisms when preferred backends are unavailable
- Caches routing decisions to improve response time

**Technical Details**:
- Exposes a /route endpoint for both proxy gateway and planning service
- Supports different optimization preferences: cost_priority, latency_priority, balanced
- Monitors real-time backend performance metrics
- Health-checks classifier availability on startup
- Provides planning-specific routing optimizations for task execution

### 3. Policy Manager (Configuration Service)

**Purpose**: Manages routing policies and model profiles.

**Key Functions**:
- Stores and retrieves routing policies
- Maintains detailed model profiles with performance characteristics
- Notifies other services about configuration updates via Redis Pub/Sub

**Technical Details**:
- Manages model API keys securely
- Supports multiple policy types (ROUTE, OPTIMIZE, BLOCK, ALLOW_ACCESS, DENY_ACCESS)
- Handles policy criteria based on user roles, task type, capabilities, etc.

### 4. Data Processor (Metrics Collection & Enrichment)

**Purpose**: Processes inference logs and planning metrics for comprehensive analytics.

**Key Functions**:
- **LLM Metrics**: Consumes raw inference logs from Kafka for direct LLM requests
- **Planning Metrics**: Processes goal execution logs, task performance, and workflow analytics
- Enriches logs with system-level metrics and planning context
- Calculates cost information for both individual requests and complete workflows
- **Planning Analytics**: Tracks goal success rates, task execution patterns, and optimization opportunities
- Persists enriched data to ClickHouse for historical analysis and planning optimization

**Technical Details**:
- Processes metrics like latency, token counts, costs, and planning execution data
- Maintains historical performance data for both LLM routing and planning optimization
- Supports planning-specific metrics: goal completion rates, task dependency analysis, workflow costs

### 5. Dashboard API and Frontend

**Purpose**: Provides administrative interface and visualization for both LLM optimization and planning workflows.

**Key Functions**:
- **LLM Analytics**: Displays real-time performance metrics for direct LLM requests
- **Planning Dashboard**: Shows goal execution progress, task analytics, and workflow performance
- Allows policy and model profile management
- **Workflow Insights**: Provides planning effectiveness metrics and optimization recommendations
- Shows usage patterns and cost information across both modes
- **Goal Management**: Interface for creating, monitoring, and managing autonomous workflows

**Technical Details**:
- React-based frontend with interactive components for both LLM and planning analytics
- Visualizes data from ClickHouse via the dashboard API
- Real-time planning execution monitoring with task dependency visualization
- Planning-specific UI components for goal creation and workflow management

## Supporting Infrastructure

- **Redis**: Used for:
  - Caching policy and model profile information
  - Storing conversation history for multi-turn interactions
  - Caching LLM responses for frequently asked queries
  - **Planning State**: Persistent storage for goal execution context and task results
  - Publishing optimal backend IDs
  - Redis Pub/Sub for configuration updates

- **ClickHouse**: Columnar database for:
  - Storing enriched inference logs for direct LLM requests
  - **Planning Analytics**: Goal execution metrics, task performance, and workflow analytics
  - Supporting analytical queries for both LLM and planning dashboards
  - Maintaining historical performance data for optimization

- **Kafka**: Message broker for:
  - Reliable communication of inference logs
  - **Planning Events**: Goal creation, task execution, and workflow completion events
  - Asynchronous data processing for both LLM and planning metrics

- **Prometheus**: Monitoring system for:
  - Collecting system-level metrics
  - Monitoring service health
  - **Planning Metrics**: Goal execution rates, task success rates, and workflow performance

## Data Flow

### Direct LLM Request Flow
1. Client sends an LLM request to the proxy-gateway
2. Proxy checks response cache (if applicable)
3. If cache miss, proxy requests routing decision from ai-optimizer
4. AI Optimizer classifies the prompt and evaluates policies
5. Proxy forwards request to selected backend with appropriate auth
6. Response is returned to client and cached if appropriate
7. Inference logs are published to Kafka
8. Data processor enriches logs and stores in ClickHouse
9. Metrics inform future optimization decisions

### Autonomous Planning Flow
1. Client submits a goal to the planning service via proxy-gateway
2. Planning service parses goal and extracts success criteria/constraints
3. Task decomposer breaks down goal into executable tasks using LLM assistance
4. Execution engine orchestrates task execution with dependency management
5. Individual tasks route through AI Optimizer for cost-optimized LLM calls
6. Task results are stored in execution context and Redis
7. Planning events and metrics are published to Kafka
8. Data processor enriches planning logs and stores analytics in ClickHouse
9. Goal completion status and results are returned to client
10. Planning metrics inform future decomposition and optimization strategies

## Advanced Features

### LLM Optimization Features
- **Prompt Classification**: LLM-based classification of requests into categories for specialized handling
- **Semantic Routing**: Understanding the meaning of prompts for context-aware routing
- **Conversational Context Management**: Maintaining history across multiple turns
- **Dynamic Routing Preferences**: Adjustable optimization objectives (cost vs. latency)
- **Response Caching**: Storing responses for frequent queries to reduce costs and latency
- **Multi-Provider Support**: Compatible with OpenAI, Google, Anthropic, and custom backends
- **User-Controlled Routing**: Application developers can explicitly override routing decisions
- **Model Status Awareness**: System understands active vs. inactive models for reliable routing
- **Multi-Tier Fallback Mechanisms**: Graceful degradation when preferred models are unavailable

### Autonomous Planning Features 🆕
- **Natural Language Goal Processing**: Convert high-level objectives into structured execution plans
- **Intelligent Task Decomposition**: LLM-assisted breakdown of complex goals into executable tasks
- **Multi-Strategy Planning**: Template-based, LLM-assisted, and hybrid planning approaches
- **Dependency-Aware Execution**: Automatic handling of task dependencies with parallel execution
- **Adaptive Re-planning**: Dynamic plan adjustment based on execution results and failures
- **State Persistence**: Maintain execution context across multi-step workflows with recovery
- **Resource Optimization**: Intelligent allocation of computational resources across tasks
- **Workflow Cost Optimization**: End-to-end cost optimization for complete goal execution
- **Real-time Progress Tracking**: Monitor goal execution and task completion in real-time
- **Task Template Library**: Reusable patterns for common workflow types
- **Multi-Goal Orchestration**: Concurrent execution of multiple goals with resource sharing

## Routing Strategies

The AI Optimizer supports different routing strategies to determine the optimal backend for a request.

### 1. Default

Routes to a specific model based on the `ModelPriorities` list.

### 2. Cascade

Tries models in the `ModelPriorities` list in order, until a suitable model is found.

### 3. Parallel

Sends the request to multiple models in parallel and compares the results using the `ComparisonMethod`.

### 4. Hybrid

Combines different routing strategies based on the request characteristics.

The routing strategy is defined in the `RoutingStrategy` struct in `k8s/ai-optimizer/main.go`:

```go
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
}
```

## Policy Structure

The Policy struct defines the routing policies and includes fields for Role-Based Access Control (RBAC):

```go
type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`   // JSON string of conditions
	Action       string          `json:"action"`     // e.g., "ROUTE", "OPTIMIZE", "BLOCK"
	BackendID    string          `json:"backend_id"` // Specific backend ID if action is ROUTE
	Priority     int             `json:"priority"`   // Order of evaluation
	Rules        json.RawMessage `json:"rules"`      // JSON string of task-specific rules or parameters for OPTIMIZE
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata"`      // Additional arbitrary metadata
	RateLimit    int             `json:"rate_limit"`    // For rate limiting policies (e.g., requests per minute)
	Budget       float64         `json:"budget"`        // For cost budgeting policies (e.g., max USD per hour)
	Effect       string          `json:"effect"`        // "ALLOW" or "DENY" for RBAC
	Subjects     []string        `json:"subjects"`      // Users or roles
	ResourceType string          `json:"resource_type"` // e.g., "model", "endpoint"
	ResourceIDs  []string        `json:"resource_ids"`  // Specific resource IDs or "*"
	Permissions  []string        `json:"permissions"`   // Actions allowed/denied
	Status       string          `json:"status"`        // "active", "inactive", etc.
}
```

*   **Effect**: "ALLOW" or "DENY" for RBAC.
*   **Subjects**: List of user IDs, role IDs, or group IDs.
*   **ResourceType**: Type of resource being controlled (e.g., "model", "endpoint").
*   **ResourceIDs**: List of specific resource IDs (e.g., model IDs) or "\*" for all.
*   **Permissions**: List of actions allowed or denied (e.g., "read", "write", "execute").

## Semantic Cache

The proxy gateway implements a semantic cache to store LLM responses for similar queries.

1.  **Embedding Generation**: The proxy generates an embedding vector for each incoming prompt using an embedding model.
2.  **Similarity Search**: The proxy searches the cache for prompts with similar embeddings.
3.  **Response Retrieval**: If a similar prompt is found in the cache, the proxy returns the cached response.

The semantic cache uses cosine similarity to measure the similarity between embeddings.

## Planning Engine Architecture

### Goal Processing Pipeline

```
Natural Language Goal → Goal Parser → Task Decomposer → Execution Engine → Results
                           ↓              ↓               ↓
                    Success Criteria   Task Templates   State Manager
                    Constraints        Dependencies     Progress Tracking
                    Context           LLM Planning      Recovery Mechanisms
```

### Task Execution Framework

The planning engine supports multiple task types with specialized executors:

- **LLM Tasks**: Route through AI Optimizer for cost-optimized language model calls
- **Data Tasks**: Execute database queries and data retrieval operations
- **API Tasks**: Make HTTP requests to external services and APIs
- **Analysis Tasks**: Perform data analysis and statistical operations
- **Validation Tasks**: Verify results against success criteria
- **Aggregation Tasks**: Combine results from multiple tasks

### Planning Strategies

1. **Template-Based Planning**: Use predefined task templates for common workflow patterns
2. **LLM-Assisted Planning**: Leverage language models for intelligent task decomposition
3. **Hybrid Planning**: Combine templates with LLM customization for optimal results

### State Management Architecture

```
Execution Context (Redis)
├── Goal Information
├── Plan Definition
├── Task Results
├── Variables & Context
└── Progress Tracking

Auto-Save → Persistent Storage
Recovery → Context Restoration
```

## Integration Patterns

### Unified API Gateway
- Single entry point for both LLM requests and planning workflows
- Consistent authentication and authorization across all features
- Unified logging and monitoring for comprehensive observability

### Cost Optimization Integration
- Planning tasks leverage existing LLM routing optimization
- Workflow-level cost analysis and budget management
- Cross-task resource sharing and optimization

### Analytics Integration
- Combined metrics for LLM usage and planning execution
- Unified dashboard for both optimization and planning insights
- Historical analysis for continuous improvement
