terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

# Accepts JSON service account content as a string
variable "GOOGLE_CREDENTIALS_JSON" {
  description = "Content of the Google service account key JSO<PERSON> file as a string."
  type        = string
  sensitive   = true
}

provider "google" {
  project     = "silken-zenith-460615-s7"
  region      = "us-central1"
  credentials = var.GOOGLE_CREDENTIALS_JSON
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth[0].cluster_ca_certificate)
}

data "google_container_cluster" "primary" {
  name     = "ai-optimizer-cluster"
  location = "us-central1"
  project  = "silken-zenith-460615-s7"
}

data "google_client_config" "default" {}

