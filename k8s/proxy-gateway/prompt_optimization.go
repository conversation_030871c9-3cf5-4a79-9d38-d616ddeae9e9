package main

import (
	"log"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

// Semantic compression constants - these don't conflict with existing constants
// since they have different names
const (
	// Preservation factors for different content types
	InstructionPreservationWeight = 2.0  // Weight for preserving instructions
	ContextCompressionRatio       = 0.6  // Target ratio for context compression
	MaxCompressionPasses          = 3    // Maximum compression passes
	KeyConceptScoreThreshold      = 0.75 // Score threshold for key concepts
	MinCompressionChunkSize       = 150  // Minimum chunk size for compression
	CompressionAggressiveness     = 0.85 // Compression aggressiveness factor
)

// semanticPromptCompression implements a semantic-aware compression algorithm
// that preserves meaning while reducing token count
func semanticPromptCompression(text string) string {
	// Split the text into semantic sections based on structure
	sections := splitIntoSemanticSections(text)

	// Process each section appropriately
	var compressedSections []string
	for _, section := range sections {
		// Identify the type of section (instruction, context, example, etc.)
		sectionType := identifySectionType(section)

		var compressedSection string
		switch sectionType {
		case "instruction":
			// Preserve instructions with minimal compression
			compressedSection = compressInstructionSection(section)
		case "context":
			// Apply more aggressive compression to context
			compressedSection = compressContextSection(section)
		case "example":
			// Preserve structure of examples but compress content
			compressedSection = compressExampleSection(section)
		default:
			// Generic compression for other sections
			compressedSection = compressGenericSection(section)
		}

		compressedSections = append(compressedSections, compressedSection)
	}

	// Recombine the compressed sections
	return strings.Join(compressedSections, "\n\n")
}

// splitIntoSemanticSections attempts to split text into logical sections
// based on patterns like headings, paragraph breaks, etc.
func splitIntoSemanticSections(text string) []string {
	// First try to split by markdown-style headings
	headingRegex := regexp.MustCompile(`(?m)^#{1,6}\s+.+$`)
	headingMatches := headingRegex.FindAllStringIndex(text, -1)

	if len(headingMatches) > 0 {
		var sections []string
		startIdx := 0

		for _, match := range headingMatches {
			// If this isn't the first heading, add the previous section
			if match[0] > startIdx {
				sections = append(sections, text[startIdx:match[0]])
			}
			startIdx = match[0]
		}

		// Add the final section
		sections = append(sections, text[startIdx:])
		return sections
	}

	// If no headings found, try to split by double newlines (paragraphs)
	paragraphs := strings.Split(text, "\n\n")
	if len(paragraphs) > 1 {
		return paragraphs
	}

	// If no clear paragraphs, split by sentence groupings
	return splitBySentenceGroups(text)
}

// splitBySentenceGroups splits text into groups of related sentences
func splitBySentenceGroups(text string) []string {
	sentenceRegex := regexp.MustCompile(`[.!?]+\s+`)
	sentences := sentenceRegex.Split(text, -1)

	// If the text is short enough, return as a single section
	if len(sentences) <= 3 || len(text) < MinCompressionChunkSize {
		return []string{text}
	}

	// Group sentences into logical chunks
	var sections []string
	var currentSection []string
	var currentLength int

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if sentence == "" {
			continue
		}

		// Add a period if it was removed by the split
		if !strings.HasSuffix(sentence, ".") && !strings.HasSuffix(sentence, "!") && !strings.HasSuffix(sentence, "?") {
			sentence += "."
		}

		sentenceLength := len(sentence)

		// If adding this sentence would make the section too large, start a new section
		if currentLength+sentenceLength > MinCompressionChunkSize && len(currentSection) > 0 {
			sections = append(sections, strings.Join(currentSection, " "))
			currentSection = []string{sentence}
			currentLength = sentenceLength
		} else {
			currentSection = append(currentSection, sentence)
			currentLength += sentenceLength
		}
	}

	// Add the last section if it's not empty
	if len(currentSection) > 0 {
		sections = append(sections, strings.Join(currentSection, " "))
	}

	return sections
}

// identifySectionType determines the semantic type of a text section
func identifySectionType(section string) string {
	section = strings.ToLower(section)

	// Check for instruction patterns
	instructionPatterns := []string{
		"your task is", "please", "write", "create", "generate",
		"explain", "describe", "analyze", "summarize", "list",
		"implement", "solve", "find", "determine", "calculate",
		"you need to", "you should", "you must", "your job is",
	}

	for _, pattern := range instructionPatterns {
		if strings.Contains(section, pattern) {
			return "instruction"
		}
	}

	// Check for example patterns
	examplePatterns := []string{
		"example:", "examples:", "for example", "e.g.", "instance:",
		"sample:", "illustration:", "for instance", "case:", "scenario:",
	}

	for _, pattern := range examplePatterns {
		if strings.Contains(section, pattern) {
			return "example"
		}
	}

	// Check for code blocks (preserve these with minimal compression)
	if strings.Contains(section, "```") || strings.Contains(section, "    ") {
		return "code"
	}

	// Default to context type for longer sections
	if len(section) > 200 {
		return "context"
	}

	// Generic type for anything else
	return "generic"
}

// compressInstructionSection compresses instruction sections
// while preserving key directives
func compressInstructionSection(section string) string {
	// Instructions should be preserved with minimal compression
	// But we can still remove some redundancy

	// Remove redundant phrases while preserving the core instruction
	redundantPhrases := []string{
		"it would be great if you could",
		"I would like you to",
		"I need you to",
		"what I'm looking for is",
		"I want you to",
	}

	compressed := section
	for _, phrase := range redundantPhrases {
		compressed = strings.Replace(compressed, phrase, "", -1)
	}

	// Preserve imperative verbs and key instructions
	return compressed
}

// compressContextSection applies more aggressive compression
// to background context information
func compressContextSection(section string) string {
	// For context sections, we can apply more aggressive compression
	// since this is usually background information

	// Split into sentences
	sentenceRegex := regexp.MustCompile(`[.!?]+\s+`)
	sentences := sentenceRegex.Split(section, -1)

	// If there are only a few sentences, keep them all
	if len(sentences) <= 3 {
		return section
	}

	// For longer context sections, keep a subset of the most important sentences
	keepRatio := ContextCompressionRatio
	keepCount := max(2, int(float64(len(sentences))*keepRatio))

	// Always keep the first and last sentence for context
	var keptSentences []string

	// Add first sentence
	if len(sentences) > 0 && sentences[0] != "" {
		firstSentence := sentences[0]
		if !strings.HasSuffix(firstSentence, ".") && !strings.HasSuffix(firstSentence, "!") && !strings.HasSuffix(firstSentence, "?") {
			firstSentence += "."
		}
		keptSentences = append(keptSentences, firstSentence)
	}

	// Keep some middle sentences if we have room
	if keepCount > 2 && len(sentences) > 2 {
		// Score sentences by importance (this is simplified - a real implementation would use NLP)
		// We're estimating importance by length and presence of key terms

		type ScoredSentence struct {
			index int
			score float64
			text  string
		}

		var scoredSentences []ScoredSentence

		for i := 1; i < len(sentences)-1; i++ {
			sentence := strings.TrimSpace(sentences[i])
			if sentence == "" {
				continue
			}

			// Add period if missing
			if !strings.HasSuffix(sentence, ".") && !strings.HasSuffix(sentence, "!") && !strings.HasSuffix(sentence, "?") {
				sentence += "."
			}

			// Score based on length (longer sentences often contain more information)
			// but cap this to avoid overly preferring long sentences
			lengthScore := min(float64(len(sentence))/100.0, 1.0)

			// Score based on presence of likely important terms
			// This is a simplistic approach - a real implementation would use more sophisticated methods
			importantTerms := []string{"important", "critical", "necessary", "significant", "essential", "key", "vital", "crucial"}
			termScore := 0.0
			for _, term := range importantTerms {
				if strings.Contains(strings.ToLower(sentence), term) {
					termScore += 0.5
				}
			}

			// Combine scores
			totalScore := lengthScore + termScore

			scoredSentences = append(scoredSentences, ScoredSentence{
				index: i,
				score: totalScore,
				text:  sentence,
			})
		}

		// Sort sentences by score (highest first)
		sort.Slice(scoredSentences, func(i, j int) bool {
			return scoredSentences[i].score > scoredSentences[j].score
		})

		// Keep top N sentences, sorted by original position
		keepMiddleCount := keepCount - 2 // Subtract 2 for first and last sentences
		if keepMiddleCount > len(scoredSentences) {
			keepMiddleCount = len(scoredSentences)
		}

		selectedMiddleSentences := make([]ScoredSentence, keepMiddleCount)
		copy(selectedMiddleSentences, scoredSentences[:keepMiddleCount])

		// Sort by original position
		sort.Slice(selectedMiddleSentences, func(i, j int) bool {
			return selectedMiddleSentences[i].index < selectedMiddleSentences[j].index
		})

		// Add the selected middle sentences
		for _, s := range selectedMiddleSentences {
			keptSentences = append(keptSentences, s.text)
		}
	}

	// Add last sentence
	if len(sentences) > 1 && sentences[len(sentences)-1] != "" {
		lastSentence := strings.TrimSpace(sentences[len(sentences)-1])
		if lastSentence != "" {
			if !strings.HasSuffix(lastSentence, ".") && !strings.HasSuffix(lastSentence, "!") && !strings.HasSuffix(lastSentence, "?") {
				lastSentence += "."
			}
			keptSentences = append(keptSentences, lastSentence)
		}
	}

	// Join the kept sentences
	compressed := strings.Join(keptSentences, " ")

	// If we've removed a significant portion, add an indicator
	if float64(len(compressed))/float64(len(section)) < 0.7 {
		compressed += " [content summarized]"
	}

	return compressed
}

// compressExampleSection compresses example sections while
// preserving their illustrative function
func compressExampleSection(section string) string {
	// Examples are important for understanding, so we preserve their structure
	// but can simplify the content within examples

	// Check if it contains multiple examples or just one
	exampleMarkers := []string{"Example 1", "Example 2", "First example", "Second example"}
	containsMultipleExamples := false

	for _, marker := range exampleMarkers {
		if strings.Contains(section, marker) {
			containsMultipleExamples = true
			break
		}
	}

	if containsMultipleExamples {
		// If multiple examples, keep only the first one or two
		parts := strings.Split(section, "Example")
		if len(parts) > 2 {
			// Keep introduction and first example
			result := parts[0] + "Example" + parts[1]
			result += "\n[additional examples omitted for brevity]"
			return result
		}
	}

	// For single examples, we keep them mostly intact but can shorten lengthy explanations
	lines := strings.Split(section, "\n")
	var result []string

	for _, line := range lines {
		// Keep short lines intact
		if len(line) < 80 {
			result = append(result, line)
			continue
		}

		// For longer explanation lines, apply mild compression
		if !strings.Contains(line, ":") && !strings.Contains(line, "```") {
			// Apply simple sentence compression
			compressedLine := compressGenericSection(line)
			result = append(result, compressedLine)
		} else {
			// Keep structural elements intact
			result = append(result, line)
		}
	}

	return strings.Join(result, "\n")
}

// compressGenericSection applies standard compression to sections
// that don't fit specific categories
func compressGenericSection(section string) string {
	// Apply standard sentence simplification
	sentences := strings.Split(section, ". ")
	var compressedSentences []string

	for _, sentence := range sentences {
		if len(sentence) < 30 {
			// Keep short sentences intact
			compressedSentences = append(compressedSentences, sentence)
			continue
		}

		// Remove less important phrases
		lowValuePhrases := []string{
			"it is worth noting that",
			"it should be mentioned that",
			"it is important to note that",
			"it is interesting to observe that",
			"as you can see",
			"as one might expect",
		}

		compressed := sentence
		for _, phrase := range lowValuePhrases {
			compressed = strings.Replace(compressed, phrase, "", -1)
		}

		// Remove some adverbs and adjectives that don't add much meaning
		lowValueModifiers := []string{
			" very ", " extremely ", " quite ", " somewhat ", " rather ",
			" basically ", " essentially ", " actually ", " really ", " virtually ",
		}

		for _, modifier := range lowValueModifiers {
			compressed = strings.Replace(compressed, modifier, " ", -1)
		}

		compressedSentences = append(compressedSentences, compressed)
	}

	result := strings.Join(compressedSentences, ". ")
	if !strings.HasSuffix(result, ".") && !strings.HasSuffix(result, "!") && !strings.HasSuffix(result, "?") {
		result += "."
	}

	return result
}

// preserveStructureCompression compresses text while preserving overall structure
func preserveStructureCompression(text string, targetTokens int) string {
	// First attempt: preserve structure but apply moderate compression to each section
	lines := strings.Split(text, "\n")
	var compressedLines []string

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// Preserve empty lines and short structural elements
		if trimmed == "" || len(trimmed) < 20 || strings.HasPrefix(trimmed, "#") {
			compressedLines = append(compressedLines, line)
			continue
		}

		// Check if this is a code block or special formatting that should be preserved
		if strings.HasPrefix(trimmed, "```") || strings.HasPrefix(trimmed, "|") || strings.HasPrefix(trimmed, "-") || strings.HasPrefix(trimmed, "*") {
			compressedLines = append(compressedLines, line)
			continue
		}

		// Apply moderate compression to normal text
		compressedLine := compressGenericSection(line)
		compressedLines = append(compressedLines, compressedLine)
	}

	compressed := strings.Join(compressedLines, "\n")

	// If we're still over budget, try more aggressive techniques
	if estimateTokenCount(compressed) > targetTokens {
		// Remove some newlines between paragraphs to save space
		compressed = regexp.MustCompile(`\n\n\n+`).ReplaceAllString(compressed, "\n\n")
	}

	return compressed
}

// essentialContentCompression keeps only the most essential content
func essentialContentCompression(text string, targetTokens int) string {
	// Second level: more aggressive, focused on preserving only essential parts
	// Split by section and evaluate importance
	sections := splitIntoSemanticSections(text)

	// Score each section by importance
	type ScoredSection struct {
		text  string
		score float64
	}

	var scoredSections []ScoredSection

	for _, section := range sections {
		sectionType := identifySectionType(section)

		// Base score on section type
		var importanceScore float64

		switch sectionType {
		case "instruction":
			importanceScore = 0.9 // Instructions are highly important
		case "example":
			importanceScore = 0.7 // Examples are quite important
		case "code":
			importanceScore = 0.8 // Code is important to preserve
		case "context":
			importanceScore = 0.5 // Context is moderately important
		default:
			importanceScore = 0.4 // Generic text is less important
		}

		// Adjust score based on position (beginning and end tend to be more important)
		position := float64(indexOf(sections, section)) / float64(len(sections))
		positionFactor := 0.0
		if position < 0.2 || position > 0.8 {
			positionFactor = 0.2 // Beginning and end get a boost
		}

		// Adjust score based on length (very short or very long sections may be less essential)
		lengthFactor := 0.0
		sectionLength := len(section)
		if sectionLength > 50 && sectionLength < 500 {
			lengthFactor = 0.1 // Medium-length sections get a boost
		}

		// Final score
		finalScore := importanceScore + positionFactor + lengthFactor

		// Compress the section according to its type
		var compressedSection string
		switch sectionType {
		case "instruction":
			compressedSection = compressInstructionSection(section)
		case "context":
			compressedSection = compressContextSection(section)
		case "example":
			compressedSection = compressExampleSection(section)
		case "code":
			compressedSection = section // Preserve code with minimal changes
		default:
			compressedSection = compressGenericSection(section)
		}

		scoredSections = append(scoredSections, ScoredSection{
			text:  compressedSection,
			score: finalScore,
		})
	}

	// Sort sections by score (highest first)
	sort.Slice(scoredSections, func(i, j int) bool {
		return scoredSections[i].score > scoredSections[j].score
	})

	// Keep adding sections until we approach the token budget
	var result strings.Builder
	currentTokens := 0
	targetTokensWithBuffer := int(float64(targetTokens) * 0.9) // Leave a 10% buffer

	for _, section := range scoredSections {
		sectionTokens := estimateTokenCount(section.text)

		if currentTokens+sectionTokens <= targetTokensWithBuffer {
			if result.Len() > 0 {
				result.WriteString("\n\n")
			}
			result.WriteString(section.text)
			currentTokens += sectionTokens
		}
	}

	// If we couldn't fit everything, add a note
	if len(scoredSections) > 0 && currentTokens < estimateTokenCount(text) {
		result.WriteString("\n\n[Some content omitted to meet token budget]")
	}

	return result.String()
}

// indexOf returns the index of an item in a slice, or -1 if not found
func indexOf(slice []string, item string) int {
	for i, s := range slice {
		if s == item {
			return i
		}
	}
	return -1
}

// Helper functions that don't conflict with existing code
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// enhancedOptimizePrompt is an enhanced version of optimizePrompt that incorporates
// the semantic compression techniques
func enhancedOptimizePrompt(prompt string, enableSemanticCompression bool) string {
	if !ENABLE_PROMPT_OPTIMIZATION {
		return prompt
	}

	// Only optimize longer prompts
	if len(prompt) < PROMPT_OPTIMIZATION_THRESHOLD {
		return prompt
	}

	// Start with basic techniques
	// 1. Remove redundant whitespace
	spaceRegex := regexp.MustCompile(`\s+`)
	optimized := spaceRegex.ReplaceAllString(prompt, " ")

	// 2. Remove redundant punctuation
	punctRegex := regexp.MustCompile(`([.!?])\s*([.!?])+`)
	optimized = punctRegex.ReplaceAllString(optimized, "$1")

	// 3. Remove common filler phrases that don't add meaning
	fillerPhrases := []string{
		"I'm writing to ask about",
		"I would like to know",
		"I was wondering if you could help me with",
		"Can you please tell me",
		"I need information regarding",
		"As I mentioned earlier",
		"As previously stated",
		"Please provide me with",
		"I would appreciate it if you could",
		"I'm curious about",
		"If you could explain",
		"I'm trying to understand",
		// Additional filler phrases
		"For your information",
		"In other words",
		"It goes without saying that",
		"Let me just say that",
		"The point I'm trying to make is",
		"I just wanted to mention that",
		"Allow me to explain",
	}

	for _, phrase := range fillerPhrases {
		optimized = strings.Replace(optimized, phrase, "", -1)
	}

	// Apply advanced techniques if enabled
	if ADVANCED_OPTIMIZATION_ENABLED {
		// 4. Detect and reduce repetition
		optimized = detectAndReduceRepetition(optimized)

		// 5. Simplify long sentences
		longSentenceRegex := regexp.MustCompile(`[^.!?]+[.!?]`)
		sentences := longSentenceRegex.FindAllString(optimized, -1)

		// Process long sentences
		for i, sentence := range sentences {
			if len(sentence) > 150 {
				// Attempt to simplify by removing parenthetical expressions
				simplified := regexp.MustCompile(`\([^)]*\)`).ReplaceAllString(sentence, "")
				// And removing non-essential clauses with commas
				simplified = regexp.MustCompile(`, [^,.]*(,|$)`).ReplaceAllString(simplified, "")

				// Only use simplified version if it's substantially shorter
				if float64(len(simplified))/float64(len(sentence)) < 0.8 {
					optimized = strings.Replace(optimized, sentence, simplified, 1)
					sentences[i] = simplified // Update for later reference
				}
			}
		}

		// 6. Compress lists
		listItemRegex := regexp.MustCompile(`(?m)^[-*•]\s+.*$`)
		listItems := listItemRegex.FindAllString(optimized, -1)
		if len(listItems) > 5 {
			compressedList := strings.Join(listItems[:3], " ") + " (plus " +
				strconv.Itoa(len(listItems)-3) + " more items)"
			optimized = listItemRegex.ReplaceAllString(optimized, "")
			optimized = optimized + "\n" + compressedList
		}

		// 7. Apply semantic compression if enabled
		if enableSemanticCompression {
			optimized = semanticPromptCompression(optimized)
		}
	}

	// Apply token budget constraints if enabled
	if TOKEN_BUDGET_ENABLED {
		// Rough token estimation
		estimatedTokens := estimateTokenCount(optimized)

		if estimatedTokens > DEFAULT_TOKEN_BUDGET {
			// Multi-level compression approach
			compressionLevel := 1

			// Try progressively more aggressive compression until we meet the budget
			// or reach maximum compression iterations
			for compressionLevel <= MaxCompressionPasses &&
				estimateTokenCount(optimized) > DEFAULT_TOKEN_BUDGET {

				// Apply different strategies based on compression level
				switch compressionLevel {
				case 1:
					// First level: preserve structure but trim content
					optimized = preserveStructureCompression(optimized, DEFAULT_TOKEN_BUDGET)
				case 2:
					// Second level: keep only essential parts with some structure
					optimized = essentialContentCompression(optimized, DEFAULT_TOKEN_BUDGET)
				case 3:
					// Final level: aggressive shortening for very long prompts
					words := strings.Fields(optimized)
					if len(words) > 100 {
						// Keep beginning and end, remove middle
						targetWordCount := float64(DEFAULT_TOKEN_BUDGET) / 1.2 // Convert token budget to approximate word count
						targetLength := int(targetWordCount)                   // Explicit conversion to int
						keepFromEachEnd := targetLength / 2

						if keepFromEachEnd*2 < len(words) {
							beginning := strings.Join(words[:keepFromEachEnd], " ")
							end := strings.Join(words[len(words)-keepFromEachEnd:], " ")
							optimized = beginning + " [...content removed to meet token budget...] " + end
						}
					}
				}

				compressionLevel++
				log.Printf("Token budget compression level %d: reduced to ~%d tokens",
					compressionLevel-1, estimateTokenCount(optimized))
			}
		}
	}

	// Only return the optimized version if it's significantly shorter
	if float64(len(optimized))/float64(len(prompt)) < 0.9 {
		log.Printf("Optimized prompt from %d to %d characters (%.1f%% reduction)",
			len(prompt), len(optimized), 100*(1-float64(len(optimized))/float64(len(prompt))))
		return optimized
	}

	// If optimization didn't yield significant reduction, return original
	return prompt
}
