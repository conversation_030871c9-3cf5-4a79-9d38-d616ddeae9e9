package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings" // <--- ADDED THIS IMPORT!
	"time"
)

// --- Google Gemini API Request/Response Structs ---

// GeminiPart represents a part of content in Gemini.
type GeminiPart struct {
	Text string `json:"text"`
	// Can include other types like "inlineData" for images
}

// GeminiContent represents a content block in Gemini.
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role,omitempty"` // "user" or "model"
}

// GeminiRequestGenerationConfig is the named struct for GenerationConfig in incoming requests.
// This matches the structure in ai-optimizer's request payload more precisely.
type GeminiRequestGenerationConfig struct {
	ResponseMimeType string          `json:"responseMimeType"`
	ResponseSchema   json.RawMessage `json:"responseSchema"` // Use RawMessage to handle varied schemas
}

// GeminiGenerateContentRequest represents the incoming request body for Gemini.
type GeminiGenerateContentRequest struct {
	Contents       []GeminiContent         `json:"contents"`
	GenerationConfig GeminiRequestGenerationConfig `json:"generationConfig,omitempty"` // Use the named struct here
	// Add other Gemini fields like "safetySettings"
}

// GeminiCandidate represents a generated candidate response.
type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
	// Add "finishReason", "safetyRatings" if needed
}

// GeminiUsageMetadata represents token usage metadata.
type GeminiUsageMetadata struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// GeminiGenerateContentResponse represents the outgoing response body for Gemini.
type GeminiGenerateContentResponse struct {
	Candidates    []GeminiCandidate   `json:"candidates"`
	UsageMetadata GeminiUsageMetadata `json:"usageMetadata"`
}

// --- Common Helper Functions ---

// countWords is a simple helper to estimate token count.
func countWords(s string) int {
	return len(strings.Fields(s))
}

// generateSimulatedEmbeddingJSON creates a JSON string of a simulated embedding vector.
func generateSimulatedEmbeddingJSON(dim int) string {
	embedding := make([]float64, dim)
	for i := 0; i < dim; i++ {
		// Generate somewhat random but consistent float values between -1.0 and 1.0
		embedding[i] = float64(i%200-100) / 100.0 // Simple pattern for simulation
	}
	// Marshal the float slice into a JSON array string
	jsonBytes, _ := json.Marshal(embedding)
	return string(jsonBytes)
}


// handleGoogleRequest simulates the Gemini API's generateContent endpoint.
func handleGoogleRequest(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = "UNKNOWN_REQUEST"
	}
	backendID := os.Getenv("BACKEND_ID") // Get backend ID from environment variable
	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, _ := strconv.Atoi(delayMsStr) // Already validated in main

	log.Printf("[%s] [%s] Received Google Gemini API request.", backendID, requestID)

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		log.Printf("[%s] [%s] Method not allowed: %s", backendID, requestID, r.Method)
		return
	}

	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusInternalServerError)
		log.Printf("[%s] [%s] Error reading request body: %v", backendID, requestID, err)
		return
	}

	var request GeminiGenerateContentRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		log.Printf("[%s] [%s] ERROR: Invalid JSON in request body: %v. Body: %s", backendID, requestID, err, string(bodyBytes))
		http.Error(w, "Invalid JSON in request body", http.StatusBadRequest)
		return
	}

	// Extract the main prompt from the request
	prompt := ""
	if len(request.Contents) > 0 && len(request.Contents[0].Parts) > 0 {
		prompt = request.Contents[0].Parts[0].Text
	}

	// *** DEBUG LOGGING FOR GENERATION CONFIG ***
	log.Printf("[%s] [%s] Mock Google Parsed Request - Prompt: '%s'", backendID, requestID, prompt)
	if request.GenerationConfig.ResponseMimeType != "" {
		log.Printf("[%s] [%s] Mock Google Parsed Request - ResponseMimeType: '%s'", backendID, requestID, request.GenerationConfig.ResponseMimeType)
	}
	if request.GenerationConfig.ResponseSchema != nil {
		log.Printf("[%s] [%s] Mock Google Parsed Request - ResponseSchema: %s", backendID, requestID, string(request.GenerationConfig.ResponseSchema))
	}
	// ********************************************

	// Simulate delay
	time.Sleep(time.Duration(delayMs) * time.Millisecond)

	var simulatedResponseContent string
	var inputTokens int = 0
	var outputTokens int = 0

	// Check if the request is for structured JSON (like embeddings)
	if request.GenerationConfig.ResponseMimeType == "application/json" {
		// Attempt to parse the schema to determine if it's an array of numbers
		var schemaMap map[string]interface{}
		if json.Unmarshal(request.GenerationConfig.ResponseSchema, &schemaMap) == nil {
			if schemaType, ok := schemaMap["type"].(string); ok && schemaType == "ARRAY" {
				if items, ok := schemaMap["items"].(map[string]interface{}); ok {
					if itemType, ok := items["type"].(string); ok && itemType == "NUMBER" {
						log.Printf("[%s] [%s] Generating simulated embedding for prompt (requested JSON array of numbers).", backendID, requestID)
						// This is a request for an embedding (simulated 768-dim vector)
						simulatedResponseContent = generateSimulatedEmbeddingJSON(768)
						// Estimate tokens based on prompt length, plus fixed for embedding response
						inputTokens = countWords(prompt)
						outputTokens = 768 // Fixed token count for simulated embedding
					}
				}
			}
		}
	}

	// If not an embedding request or schema didn't match, default to a general text response
	if simulatedResponseContent == "" {
		simulatedResponseContent = fmt.Sprintf("This is a simulated Google Gemini response from %s for prompt: '%s'.", backendID, prompt)
		inputTokens = countWords(prompt)
		outputTokens = countWords(simulatedResponseContent)
		log.Printf("[%s] [%s] Generating standard text response.", backendID, requestID)
	}


	response := GeminiGenerateContentResponse{
		Candidates: []GeminiCandidate{
			{
				Content: GeminiContent{
					Parts: []GeminiPart{
						{Text: simulatedResponseContent},
					},
					Role: "model",
				},
			},
		},
		UsageMetadata: GeminiUsageMetadata{
			PromptTokenCount:     inputTokens,
			CandidatesTokenCount: outputTokens,
			TotalTokenCount:      inputTokens + outputTokens,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)

	log.Printf("[%s] [%s] Sent response. Input Tokens: %d, Output Tokens: %d", backendID, requestID, inputTokens, outputTokens)
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile) // Add file/line to logs
	listenAddr := os.Getenv("LISTEN_ADDR")
	if listenAddr == "" {
		listenAddr = ":5004" // Default port for mock-google
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-google"
	}

	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 150 // Default delay for Google mock
	}

	log.Printf("Starting Mock AI Backend '%s' (Google Gemini API) on %s with simulated initial delay of %d ms", backendID, listenAddr, delayMs)

	// Previously: http.HandleFunc("/v1beta/models/gemini-pro:generateContent", handleGoogleRequest)
	// Now, listen on the generic chat completions path that proxy-gateway routes to:
	http.HandleFunc("/v1/chat/completions", handleGoogleRequest)

	log.Fatal(http.ListenAndServe(listenAddr, nil))
}

