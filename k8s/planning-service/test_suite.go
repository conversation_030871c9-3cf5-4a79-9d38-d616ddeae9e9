package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
)

// MockLLMClient for testing
type MockLLMClient struct {
	responses map[string]string
}

func NewMockLLMClient() *MockLLMClient {
	return &MockLLMClient{
		responses: map[string]string{
			"classification": "data_analysis",
			"decomposition": `{
				"tasks": [
					{
						"name": "Data Query",
						"description": "Query the database",
						"type": "data_query",
						"parameters": {"query": "SELECT * FROM data"},
						"tools": ["database"],
						"estimated_minutes": 5,
						"estimated_cost": 0.10
					},
					{
						"name": "Analysis",
						"description": "Analyze the data",
						"type": "analysis",
						"parameters": {"analysis_type": "statistical"},
						"tools": ["analytics"],
						"estimated_minutes": 15,
						"estimated_cost": 0.25
					}
				],
				"dependencies": [
					{
						"from_index": 0,
						"to_index": 1,
						"type": "finish_to_start",
						"description": "Data query must complete before analysis"
					}
				]
			}`,
		},
	}
}

func (m *MockLLMClient) GenerateResponse(ctx context.Context, prompt string, model string, options map[string]interface{}) (string, error) {
	if strings.Contains(prompt, "Classify the following goal") {
		return m.responses["classification"], nil
	}
	if strings.Contains(prompt, "Break down the following goal") {
		return m.responses["decomposition"], nil
	}
	return "Mock response", nil
}

// Test Goal Parser
func TestGoalParser(t *testing.T) {
	mockClient := NewMockLLMClient()
	parser := NewGoalParser(mockClient)

	request := GoalRequest{
		Description: "Analyze sales data and generate insights",
		Priority:    5,
	}

	goal, err := parser.ParseGoal(context.Background(), request, "test-user")
	if err != nil {
		t.Fatalf("Failed to parse goal: %v", err)
	}

	if goal.UserID != "test-user" {
		t.Errorf("Expected user ID 'test-user', got '%s'", goal.UserID)
	}

	if goal.Description != request.Description {
		t.Errorf("Expected description '%s', got '%s'", request.Description, goal.Description)
	}

	if goal.Priority != 5 {
		t.Errorf("Expected priority 5, got %d", goal.Priority)
	}

	if goal.Status != GoalStatusPending {
		t.Errorf("Expected status 'pending', got '%s'", goal.Status)
	}
}

// Test Task Decomposer
func TestTaskDecomposer(t *testing.T) {
	mockClient := NewMockLLMClient()
	decomposer := NewTaskDecomposer(mockClient)

	goal := Goal{
		ID:          "test-goal-1",
		Description: "Analyze sales data and generate insights",
		Metadata:    map[string]interface{}{"complexity": "medium", "classification": "data_analysis"},
	}

	plan, err := decomposer.DecomposeTasks(context.Background(), goal)
	if err != nil {
		t.Fatalf("Failed to decompose tasks: %v", err)
	}

	if plan.GoalID != goal.ID {
		t.Errorf("Expected goal ID '%s', got '%s'", goal.ID, plan.GoalID)
	}

	if len(plan.Tasks) == 0 {
		t.Error("Expected at least one task in the plan")
	}

	if plan.Status != PlanStatusDraft {
		t.Errorf("Expected plan status 'draft', got '%s'", plan.Status)
	}

	// Verify task structure
	for _, task := range plan.Tasks {
		if task.ID == "" {
			t.Error("Task ID should not be empty")
		}
		if task.PlanID != plan.ID {
			t.Errorf("Task plan ID should match plan ID")
		}
		if task.Status != TaskStatusPending {
			t.Errorf("Expected task status 'pending', got '%s'", task.Status)
		}
	}
}

// Test State Manager
func TestStateManager(t *testing.T) {
	storage := NewMemoryStateStorage()
	stateManager := NewStateManager(storage)
	defer stateManager.Close()

	// Test goal operations
	goal := Goal{
		ID:          "test-goal-1",
		UserID:      "test-user",
		Description: "Test goal",
		Status:      GoalStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Save goal
	err := stateManager.SaveGoal(goal)
	if err != nil {
		t.Fatalf("Failed to save goal: %v", err)
	}

	// Retrieve goal
	retrievedGoal, err := stateManager.GetGoal(goal.ID)
	if err != nil {
		t.Fatalf("Failed to retrieve goal: %v", err)
	}

	if retrievedGoal.ID != goal.ID {
		t.Errorf("Expected goal ID '%s', got '%s'", goal.ID, retrievedGoal.ID)
	}

	// Test execution context
	execCtx, err := stateManager.CreateExecutionContext(goal.ID, "test-plan-1")
	if err != nil {
		t.Fatalf("Failed to create execution context: %v", err)
	}

	if execCtx.GoalID != goal.ID {
		t.Errorf("Expected goal ID '%s', got '%s'", goal.ID, execCtx.GoalID)
	}

	// Test variable operations
	err = stateManager.SetVariable(execCtx, "test_var", "test_value")
	if err != nil {
		t.Fatalf("Failed to set variable: %v", err)
	}

	value, exists := stateManager.GetVariable(execCtx, "test_var")
	if !exists {
		t.Error("Variable should exist")
	}
	if value != "test_value" {
		t.Errorf("Expected variable value 'test_value', got '%v'", value)
	}
}

// Test Execution Engine
func TestExecutionEngine(t *testing.T) {
	mockClient := NewMockLLMClient()
	storage := NewMemoryStateStorage()
	stateManager := NewStateManager(storage)
	defer stateManager.Close()

	engine := NewExecutionEngine(mockClient, stateManager)

	// Create a simple plan
	plan := Plan{
		ID:     "test-plan-1",
		GoalID: "test-goal-1",
		Tasks: []Task{
			{
				ID:          "task-1",
				PlanID:      "test-plan-1",
				Name:        "Test Task",
				Description: "A test task",
				Type:        TaskTypeLLMCall,
				Parameters: map[string]interface{}{
					"prompt": "Test prompt",
				},
				Status:    TaskStatusPending,
				CreatedAt: time.Now(),
			},
		},
		Dependencies: []Dependency{},
		Status:       PlanStatusDraft,
		CreatedAt:    time.Now(),
	}

	// Execute the plan
	options := ExecutionOptions{
		MaxConcurrency: 1,
		Timeout:        30 * time.Second,
		RetryPolicy: RetryPolicy{
			MaxRetries:    1,
			InitialDelay:  time.Second,
			BackoffFactor: 2.0,
			MaxDelay:      10 * time.Second,
		},
	}

	err := engine.ExecutePlan(context.Background(), plan, options)
	if err != nil {
		t.Fatalf("Plan execution failed: %v", err)
	}
}

// Test API Endpoints
func TestAPIEndpoints(t *testing.T) {
	service := &PlanningService{
		goalParser:      NewGoalParser(NewMockLLMClient()),
		taskDecomposer:  NewTaskDecomposer(NewMockLLMClient()),
		stateManager:    NewStateManager(NewMemoryStateStorage()),
	}

	// Test goal creation
	t.Run("CreateGoal", func(t *testing.T) {
		goalRequest := GoalRequest{
			Description: "Test API goal",
			Priority:    5,
		}

		body, _ := json.Marshal(goalRequest)
		req := httptest.NewRequest("POST", "/v1/goals", strings.NewReader(string(body)))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", "test-user")

		w := httptest.NewRecorder()
		service.createGoal(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("Expected status 200, got %d", w.Code)
		}

		var goal Goal
		err := json.NewDecoder(w.Body).Decode(&goal)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		if goal.Description != goalRequest.Description {
			t.Errorf("Expected description '%s', got '%s'", goalRequest.Description, goal.Description)
		}
	})

	// Test health check
	t.Run("HealthCheck", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()

		service.healthCheck(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("Expected status 200, got %d", w.Code)
		}

		var response map[string]interface{}
		err := json.NewDecoder(w.Body).Decode(&response)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		if response["status"] != "healthy" {
			t.Errorf("Expected status 'healthy', got '%v'", response["status"])
		}
	})
}

// Integration Test
func TestEndToEndWorkflow(t *testing.T) {
	// This test simulates a complete workflow from goal creation to execution
	mockClient := NewMockLLMClient()
	storage := NewMemoryStateStorage()
	stateManager := NewStateManager(storage)
	defer stateManager.Close()

	service := &PlanningService{
		goalParser:      NewGoalParser(mockClient),
		taskDecomposer:  NewTaskDecomposer(mockClient),
		executionEngine: NewExecutionEngine(mockClient, stateManager),
		stateManager:    stateManager,
	}

	// Step 1: Create goal
	goalRequest := GoalRequest{
		Description: "End-to-end test goal",
		Priority:    7,
	}

	goal, err := service.goalParser.ParseGoal(context.Background(), goalRequest, "test-user")
	if err != nil {
		t.Fatalf("Failed to parse goal: %v", err)
	}

	err = service.stateManager.SaveGoal(*goal)
	if err != nil {
		t.Fatalf("Failed to save goal: %v", err)
	}

	// Step 2: Create plan
	plan, err := service.taskDecomposer.DecomposeTasks(context.Background(), *goal)
	if err != nil {
		t.Fatalf("Failed to create plan: %v", err)
	}

	err = service.stateManager.SavePlan(*plan)
	if err != nil {
		t.Fatalf("Failed to save plan: %v", err)
	}

	// Step 3: Execute plan
	options := ExecutionOptions{
		MaxConcurrency: 2,
		Timeout:        60 * time.Second,
		RetryPolicy: RetryPolicy{
			MaxRetries:    2,
			InitialDelay:  time.Second,
			BackoffFactor: 2.0,
			MaxDelay:      30 * time.Second,
		},
	}

	err = service.executionEngine.ExecutePlan(context.Background(), *plan, options)
	if err != nil {
		t.Fatalf("Failed to execute plan: %v", err)
	}

	// Step 4: Verify results
	updatedGoal, err := service.stateManager.GetGoal(goal.ID)
	if err != nil {
		t.Fatalf("Failed to retrieve updated goal: %v", err)
	}

	if updatedGoal.Status != GoalStatusCompleted && updatedGoal.Status != GoalStatusFailed {
		t.Errorf("Expected goal to be completed or failed, got status: %s", updatedGoal.Status)
	}

	fmt.Printf("End-to-end test completed. Final goal status: %s\n", updatedGoal.Status)
}

// Benchmark Tests
func BenchmarkGoalParsing(b *testing.B) {
	mockClient := NewMockLLMClient()
	parser := NewGoalParser(mockClient)

	request := GoalRequest{
		Description: "Benchmark test goal for performance measurement",
		Priority:    5,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := parser.ParseGoal(context.Background(), request, "bench-user")
		if err != nil {
			b.Fatalf("Goal parsing failed: %v", err)
		}
	}
}

func BenchmarkTaskDecomposition(b *testing.B) {
	mockClient := NewMockLLMClient()
	decomposer := NewTaskDecomposer(mockClient)

	goal := Goal{
		ID:          "bench-goal",
		Description: "Benchmark test goal for task decomposition performance",
		Metadata:    map[string]interface{}{"complexity": "medium", "classification": "data_analysis"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := decomposer.DecomposeTasks(context.Background(), goal)
		if err != nil {
			b.Fatalf("Task decomposition failed: %v", err)
		}
	}
}
