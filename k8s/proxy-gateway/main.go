package main

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math"
	"net/http" // For URL manipulation
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	gocache "github.com/patrickmn/go-cache"
	"github.com/segmentio/kafka-go"
)

// --- Constants ---
const (
	kafkaBroker = "kafka:9092"
	kafkaTopic  = "inference-logs"
	redisAddr   = "redis:6379"
	// Policy Manager URL for fetching policies and model profiles
	// policyManagerURL = "http://policy-manager:8083"
	// AI Optimizer URL for routing decisions. Consider making this an environment variable.
	aiOptimizerURL = "http://ai-optimizer:8085" // Default, can be overridden by env var if desired

	// Redis keys for policies and model profiles (consistent with policy-manager)
	REDIS_POLICIES_KEY_PREFIX       = "policy:"
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"

	REDIS_CACHE_KEY_PREFIX = "llm_response_cache:" // Redis key prefix for LLM response cache
	CACHE_TTL              = 1 * time.Hour         // Time-to-live for cached responses

	// Redis channel for model profile updates (consistent with policy-manager)
	MODEL_PROFILE_UPDATES_CHANNEL = "model_profile_updates"
	// Redis channel for policy updates (consistent with policy-manager)
	POLICY_UPDATES_CHANNEL = "policy_updates"

	// Configuration for semantic caching
	SEMANTIC_CACHE_KEY_PREFIX     = "semantic_cache:" // Redis key prefix for semantic cache
	SEMANTIC_CACHE_TTL            = 24 * time.Hour    // Semantic cache entries live longer
	SEMANTIC_SIMILARITY_THRESHOLD = 0.85              // Threshold for semantic similarity (0.0-1.0)

	// Cache analytics keys
	CACHE_ANALYTICS_PREFIX         = "cache_analytics:" // Redis key prefix for cache analytics
	CACHE_HIT_COUNTER_KEY          = CACHE_ANALYTICS_PREFIX + "hits"
	CACHE_MISS_COUNTER_KEY         = CACHE_ANALYTICS_PREFIX + "misses"
	SEMANTIC_CACHE_HIT_COUNTER_KEY = CACHE_ANALYTICS_PREFIX + "semantic_hits"
	CACHE_ANALYTICS_TTL            = 30 * 24 * time.Hour // Cache analytics live for 30 days

	// In-memory cache configuration for hot items
	IN_MEMORY_CACHE_MAX_ITEMS = 1000 // Maximum number of items in the in-memory cache

	// Configuration for prompt optimization
	ENABLE_PROMPT_OPTIMIZATION    = true // Master switch for prompt optimization
	PROMPT_OPTIMIZATION_THRESHOLD = 500  // Only optimize prompts longer than this many characters
	ADVANCED_OPTIMIZATION_ENABLED = true // Enable more aggressive optimization techniques
	TOKEN_BUDGET_ENABLED          = true // Enable token budget controls
	DEFAULT_TOKEN_BUDGET          = 8000 // Default token budget (can be overridden per request)
	// Constants SEMANTIC_COMPRESSION_ENABLED, MAX_COMPRESSION_ITERATIONS, and COMPRESSION_MIN_CHUNK_SIZE
	// are defined in enhanced_functions.go
)

// --- Structs (consistent with other services) ---

// PromptTemplate represents a reusable prompt pattern with variable placeholders
type PromptTemplate struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Template    string            `json:"template"`
	Description string            `json:"description,omitempty"`
	Variables   map[string]string `json:"variables,omitempty"` // Default values for variables
	Category    string            `json:"category,omitempty"`  // E.g., "summarization", "translation", etc.
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// CacheAnalytics tracks cache performance metrics
type CacheAnalytics struct {
	Hits           int     `json:"hits"`
	Misses         int     `json:"misses"`
	SemanticHits   int     `json:"semantic_hits"`
	TokensSaved    int64   `json:"tokens_saved"`
	CostSaved      float64 `json:"cost_saved"`
	AverageLatency int     `json:"average_latency_ms"`
}

// TokenBudget defines limits on token usage for optimization
type TokenBudget struct {
	MaxInputTokens  int `json:"max_input_tokens"`
	MaxOutputTokens int `json:"max_output_tokens"`
	Priority        int `json:"priority"` // 1-10, with 10 being highest priority
}

// InMemoryCache implements a simple in-memory LRU cache
type InMemoryCache struct {
	items map[string]string
	mu    sync.RWMutex
	cap   int
}

// NewInMemoryCache creates a new in-memory cache with the given capacity
func NewInMemoryCache(capacity int) *InMemoryCache {
	return &InMemoryCache{
		items: make(map[string]string, capacity),
		cap:   capacity,
	}
}

// Get retrieves a value from the cache
func (c *InMemoryCache) Get(key string) (string, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	val, found := c.items[key]
	return val, found
}

// Set adds a value to the cache
func (c *InMemoryCache) Set(key, value string, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// If at capacity, remove a random item (simple eviction policy)
	if len(c.items) >= c.cap {
		for k := range c.items {
			delete(c.items, k)
			break
		}
	}

	c.items[key] = value
}

// Delete removes a value from the cache
func (c *InMemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.items, key)
}

// Policy defines a routing policy.
type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`             // Changed to json.RawMessage to handle array or object
	Action       string          `json:"action"`               // "ROUTE", "OPTIMIZE", "BLOCK", etc.
	BackendID    string          `json:"backend_id,omitempty"` // Only if Action is ROUTE
	Priority     int             `json:"priority"`
	Rules        json.RawMessage `json:"rules,omitempty"` // For more complex routing rules
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata,omitempty"`
	AllowedUsers []string        `json:"allowed_users,omitempty"`
	BlockedUsers []string        `json:"blocked_users,omitempty"`
	RateLimit    int             `json:"rate_limit,omitempty"` // Requests per minute
	Budget       float64         `json:"budget,omitempty"`     // Max cost per time period
}

// ModelProfile defines performance and cost characteristics for a specific model.
type ModelProfile struct {
	ID                 string    `json:"id"`
	Name               string    `json:"name"`
	Aliases            []string  `json:"aliases"`
	Capabilities       []string  `json:"capabilities"`
	PricingTier        string    `json:"pricing_tier"`
	DataSensitivity    string    `json:"data_sensitivity"`
	ExpectedLatencyMs  float64   `json:"expected_latency_ms"`
	ExpectedCost       float64   `json:"expected_cost"`
	BackendURL         string    `json:"url"`
	BackendType        string    `json:"backend_type"` // e.g., "openai", "google", "anthropic", "vllm"
	CostPerInputToken  float64   `json:"cost_per_input_token"`
	CostPerOutputToken float64   `json:"cost_per_output_token"`
	CPUCostPerHour     float64   `json:"cpu_cost_per_hour"`
	MemoryCostPerHour  float64   `json:"memory_cost_per_hour"`
	APIKey             string    `json:"api_key,omitempty"` // API Key for external models
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// RouteResponse matches the response from the AI Optimizer's /route endpoint.
type AIOptimizerRouteResponse struct {
	SelectedBackendID  string `json:"selected_backend_id"`
	BackendType        string `json:"backend_type"`
	BackendURL         string `json:"backend_url"`
	PolicyIDApplied    string `json:"policy_id_applied"`
	ModelUsed          string `json:"model_used"`
	TaskType           string `json:"task_type"`
	Error              string `json:"error,omitempty"`
	FromCache          bool   `json:"from_from_cache,omitempty"`      // If AI Optimizer served from its cache
	CachedResponseBody string `json:"cached_response_body,omitempty"` // Cached body if FromCache is true
}

// OptimizationRequest is the payload sent FROM the proxy-gateway TO the AI Optimizer.
// This now includes the PreferredLLMID.
type OptimizationRequest struct {
	Prompt          string       `json:"prompt"`
	Model           string       `json:"model"` // The model requested by the client (e.g., "gpt-3.5-turbo")
	UserID          string       `json:"user_id,omitempty"`
	UserRoles       []string     `json:"user_roles,omitempty"`
	DataSensitivity string       `json:"data_sensitivity,omitempty"`
	PreferredLLMID  string       `json:"preferred_llm_id,omitempty"` // NEW: For X-Preferred-LLM-ID header
	TokenBudget     *TokenBudget `json:"token_budget,omitempty"`
}

// InferenceLog matches the structure of the log messages sent to Kafka.
// It includes user identification fields for RBAC and auditing.
type InferenceLog struct {
	RequestID           string              `json:"request_id"`
	Timestamp           time.Time           `json:"timestamp"`
	Method              string              `json:"method"`
	Path                string              `json:"path"`
	ClientIP            string              `json:"client_ip"`
	UserAgent           string              `json:"user_agent"`
	SelectedBackendID   string              `json:"selected_backend_id"`
	BackendURL          string              `json:"backend_url"`
	BackendType         string              `json:"backend_type,omitempty"`
	RequestHeaders      map[string][]string `json:"request_headers,omitempty"`
	RequestBodySnippet  string              `json:"request_body_snippet,omitempty"`
	ResponseTimestamp   time.Time           `json:"response_timestamp"`
	LatencyMs           float64             `json:"latency_ms"`
	StatusCode          int32               `json:"status_code"`
	ResponseHeaders     map[string][]string `json:"response_headers,omitempty"`
	ResponseBodySnippet string              `json:"response_body_snippet,omitempty"`
	InputTokens         int64               `json:"input_tokens,omitempty"`
	OutputTokens        int64               `json:"output_tokens,omitempty"`
	TotalCost           float64             `json:"total_cost"` // Renamed from 'Cost' to 'TotalCost' for consistency
	Error               string              `json:"error,omitempty"`
	PolicyIDApplied     string              `json:"policy_id_applied,omitempty"`
	ModelRequested      string              `json:"model_requested,omitempty"`
	ModelUsed           string              `json:"model_used,omitempty"`
	Stream              bool                `json:"stream,omitempty"`
	CPUUsage            float64             `json:"cpu_usage_rate"`
	MemoryUsage         float64             `json:"memory_usage_bytes"`
	TaskType            string              `json:"task_type,omitempty"`
	ConversationID      string              `json:"conversation_id"`
	UserID              string              `json:"user_id,omitempty"`    // NEW: User ID from request header
	UserRoles           []string            `json:"user_roles,omitempty"` // NEW: User roles from request header
}

// OpenAI API request and response structs for parsing tokens/cost
// (Simplified for relevant fields for logging)
type OpenAICompletionRequest struct {
	Model     string              `json:"model"`
	Messages  []map[string]string `json:"messages"`
	MaxTokens int                 `json:"max_tokens,omitempty"`
	Stream    bool                `json:"stream,omitempty"` // Added stream field
}

type OpenAICompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int               `json:"index"`
		Message      map[string]string `json:"message"`
		LogProbs     interface{}       `json:"logprobs"`
		FinishReason string            `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// Google Gemini API request and response structs for parsing tokens/cost
// (These match the expected format for generateContent)
type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
	Role  string       `json:"role"` // "user" or "model"
}

type GeminiGenerationConfig struct {
	MaxOutputTokens int `json:"maxOutputTokens,omitempty"`
}

type GeminiGenerateContentRequest struct {
	Contents         []GeminiContent        `json:"contents"`
	GenerationConfig GeminiGenerationConfig `json:"generationConfig,omitempty"`
}

type GeminiGenerateContentResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
		} `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int `json:"promptTokenCount"`
		CandidatesTokenCount int `json:"candidatesTokenCount"`
		TotalTokenCount      int `json:"totalTokenCount"`
	} `json:"usageMetadata"`
}

// Anthropic API request and response structs for parsing tokens/cost
type AnthropicMessagesRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	MaxTokens int  `json:"max_tokens"`
	Stream    bool `json:"stream,omitempty"` // Added stream field
}

type AnthropicMessagesResponse struct {
	ID    string `json:"id"`
	Model string `json:"model"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
	} `json:"usage"`
	Content []struct {
		Type string `json:"type"`
		Text string `json:"text"`
	} `json:"content"`
}

// --- Global Variables ---
// EmbeddingRequest is the structure for requesting embeddings
type EmbeddingRequest struct {
	Model string   `json:"model"`
	Input []string `json:"input"`
}

// EmbeddingResponse is the structure returned by embedding APIs
type EmbeddingResponse struct {
	Object string `json:"object"`
	Data   []struct {
		Object    string    `json:"object"`
		Embedding []float64 `json:"embedding"`
		Index     int       `json:"index"`
	} `json:"data"`
	Model string `json:"model"`
	Usage struct {
		PromptTokens int `json:"prompt_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
}

// SemanticCacheEntry represents an entry in the semantic cache
type SemanticCacheEntry struct {
	Prompt        string          `json:"prompt"`
	Response      json.RawMessage `json:"response"`
	Embedding     []float64       `json:"embedding"`
	Model         string          `json:"model"`
	Timestamp     time.Time       `json:"timestamp"`
	InputTokens   int64           `json:"input_tokens"`
	OutputTokens  int64           `json:"output_tokens"`
	TotalCost     float64         `json:"total_cost"`
	EmbeddingHash string          `json:"embedding_hash"`
}

var (
	kafkaWriter     *kafka.Writer
	redisClient     *redis.Client
	policies        map[string]Policy
	modelProfiles   map[string]ModelProfile
	promptTemplates map[string]PromptTemplate // Store for prompt templates
	// conversationHistory map[string][]map[string]string // Removed duplicate declaration
	mu             sync.RWMutex
	semanticCache  sync.Map       // Thread-safe map for in-memory semantic cache layer
	embedderLock   sync.Mutex     // Lock for embedder to avoid parallel calls
	inMemoryCache  *gocache.Cache // Fast in-memory cache for hot items
	cacheAnalytics CacheAnalytics // Cache performance analytics
	analyticsLock  sync.RWMutex   // Lock for updating cache analytics
)

// Init function runs once when the application starts
func init() {
	// Initialize Kafka writer
	kafkaWriter = kafka.NewWriter(kafka.WriterConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaTopic,
		Balancer: &kafka.LeastBytes{},
	})
	log.Printf("Kafka writer initialized for topic %s on brokers %s", kafkaTopic, kafkaBroker)

	// Initialize Redis client
	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initialize caches and data structures
	policies = make(map[string]Policy)
	modelProfiles = make(map[string]ModelProfile)
	promptTemplates = make(map[string]PromptTemplate)
	inMemoryCache = gocache.New(5*time.Minute, 10*time.Minute)

	// Initialize cache analytics
	cacheAnalytics = CacheAnalytics{
		Hits:           0,
		Misses:         0,
		SemanticHits:   0,
		TokensSaved:    0,
		CostSaved:      0.0,
		AverageLatency: 0,
	}

	// Load initial policies and model profiles from Redis
	if err := loadPoliciesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load policies from Redis on startup: %v", err)
	}
	if err := loadModelProfilesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	}

	// Load prompt templates from Redis
	if err := loadPromptTemplatesFromRedis(ctx); err != nil {
		log.Printf("Warning: Failed to load prompt templates from Redis on startup: %v", err)
	}

	// Load cache analytics from Redis if available
	if err := loadCacheAnalyticsFromRedis(ctx); err != nil {
		log.Printf("Note: No existing cache analytics found in Redis or error loading: %v", err)
	}

	// Start goroutines to listen for Redis Pub/Sub updates
	go listenForRedisPolicyUpdates()
	go listenForRedisModelProfileUpdates()
	go listenForRedisPromptTemplateUpdates()

	// Start periodic cache analytics reporter
	go reportCacheAnalyticsRegularly(ctx)
}

// loadPromptTemplatesFromRedis fetches all prompt templates from Redis
func loadPromptTemplatesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPromptTemplates := make(map[string]PromptTemplate)
	keys, err := redisClient.Keys(ctx, "prompt_template:*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get prompt template keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting prompt template %s from Redis: %v", key, err)
			continue
		}

		var template PromptTemplate
		if err := json.Unmarshal([]byte(val), &template); err != nil {
			log.Printf("Error unmarshalling prompt template %s: %v", key, err)
			continue
		}

		newPromptTemplates[template.ID] = template
	}

	promptTemplates = newPromptTemplates
	log.Printf("Loaded %d prompt templates from Redis", len(promptTemplates))
	return nil
}

// listenForRedisPromptTemplateUpdates listens for Redis Pub/Sub messages for prompt template updates
func listenForRedisPromptTemplateUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), "prompt_template_updates")
	defer pubsub.Close()
	log.Println("Listening for Redis prompt template updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message for prompt templates: %s", msg.Payload)
		if err := loadPromptTemplatesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading prompt templates from Redis: %v", err)
		} else {
			log.Println("Prompt templates cache refreshed due to pending updates")
		}
	}
}

// loadCacheAnalyticsFromRedis loads cache analytics data from Redis
func loadCacheAnalyticsFromRedis(ctx context.Context) error {
	analyticsLock.Lock()
	defer analyticsLock.Unlock()

	// Get cache hit count
	hits, err := redisClient.Get(ctx, CACHE_HIT_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.Hits = hits
	}

	// Get cache miss count
	misses, err := redisClient.Get(ctx, CACHE_MISS_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.Misses = misses
	}

	// Get semantic cache hit count
	semanticHits, err := redisClient.Get(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY).Int()
	if err != nil && err != redis.Nil {
		return err
	} else if err == nil {
		cacheAnalytics.SemanticHits = semanticHits
	}

	// Get other analytics if they exist
	tokensStr, err := redisClient.Get(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved").Result()
	if err == nil {
		if tokens, parseErr := strconv.ParseInt(tokensStr, 10, 64); parseErr == nil {
			cacheAnalytics.TokensSaved = tokens
		}
	}

	costStr, err := redisClient.Get(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved").Result()
	if err == nil {
		if cost, parseErr := strconv.ParseFloat(costStr, 64); parseErr == nil {
			cacheAnalytics.CostSaved = cost
		}
	}

	log.Printf("Loaded cache analytics: %d hits, %d misses, %d semantic hits, %d tokens saved, $%.2f cost saved",
		cacheAnalytics.Hits, cacheAnalytics.Misses, cacheAnalytics.SemanticHits,
		cacheAnalytics.TokensSaved, cacheAnalytics.CostSaved)

	return nil
}

// pruneSemanticCache removes old entries from the semantic cache.
func pruneSemanticCache() {
	log.Println("Pruning semantic cache...")
	var keysToDelete []string
	semanticCache.Range(func(key, value interface{}) bool {
		entry, ok := value.(SemanticCacheEntry)
		if !ok {
			log.Printf("Unexpected type in semantic cache: %T", value)
			return true // Continue to next entry
		}
		if time.Since(entry.Timestamp) > SEMANTIC_CACHE_TTL {
			keysToDelete = append(keysToDelete, key.(string))
		}
		return true // Continue to next entry
	})

	for _, key := range keysToDelete {
		log.Printf("Deleting expired entry from semantic cache: %s", key)
		semanticCache.Delete(key)
	}
	log.Printf("Pruned %d expired entries from semantic cache.", len(keysToDelete))
}

// reportCacheAnalyticsRegularly periodically saves cache analytics to Redis
// and performs cache maintenance
func reportCacheAnalyticsRegularly(ctx context.Context) {
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			saveCacheAnalyticsToRedis(ctx)
			// Also prune the semantic cache to remove old entries
			pruneSemanticCache()
		case <-ctx.Done():
			return
		}
	}
}

// saveCacheAnalyticsToRedis saves the current cache analytics to Redis
func saveCacheAnalyticsToRedis(ctx context.Context) {
	analyticsLock.Lock()
	defer analyticsLock.Unlock()

	pipe := redisClient.Pipeline()

	// Save all analytics metrics
	pipe.Set(ctx, CACHE_HIT_COUNTER_KEY, cacheAnalytics.Hits, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_MISS_COUNTER_KEY, cacheAnalytics.Misses, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY, cacheAnalytics.SemanticHits, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved", cacheAnalytics.TokensSaved, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved", cacheAnalytics.CostSaved, CACHE_ANALYTICS_TTL)
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"average_latency", cacheAnalytics.AverageLatency, CACHE_ANALYTICS_TTL)

	// Calculate and save cache hit rate
	totalRequests := cacheAnalytics.Hits + cacheAnalytics.Misses + cacheAnalytics.SemanticHits
	var hitRate float64 = 0
	if totalRequests > 0 {
		hitRate = float64(cacheAnalytics.Hits+cacheAnalytics.SemanticHits) / float64(totalRequests)
	}
	pipe.Set(ctx, CACHE_ANALYTICS_PREFIX+"hit_rate", hitRate, CACHE_ANALYTICS_TTL)

	// Execute all commands
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Printf("Error saving cache analytics to Redis: %v", err)
	} else {
		log.Printf("Cache analytics saved to Redis: %d hits, %d misses, %.2f%% hit rate, $%.2f saved",
			cacheAnalytics.Hits, cacheAnalytics.Misses, hitRate*100, cacheAnalytics.CostSaved)
	}
}

func main() {
	defer kafkaWriter.Close()
	defer redisClient.Close()

	// Initialize the cache warming system
	ctx := context.Background()
	initCacheWarming(ctx)

	mux := http.NewServeMux()
	// Apply batch request middleware to the main LLM request handler
	mux.HandleFunc("/v1/chat/completions", handleLLMRequest)
	mux.HandleFunc("/v1/analytics/cache", handleCacheAnalyticsRequest)
	mux.HandleFunc("/v1/cache/manage", handleCacheManagementRequest)

	// Add batch statistics endpoint
	// mux.HandleFunc("/v1/analytics/batch", GetBatchStatisticsHandler)

	// Register cache warming handlers
	RegisterCacheWarmingHandlers(mux)

	// Add other endpoints as needed, e.g., for embeddings, image generation

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // Default port for proxy-gateway
	}
	log.Printf("Proxy Gateway starting on port %s...", port)
	log.Fatal(http.ListenAndServe(":"+port, mux))
}

// loadPoliciesFromRedis fetches all policies from Redis into the cache.
func loadPoliciesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newPolicies := make(map[string]Policy)
	keys, err := redisClient.Keys(ctx, REDIS_POLICIES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get policy keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting policy %s from Redis: %v", key, err)
			continue
		}
		var policy Policy
		if err := json.Unmarshal([]byte(val), &policy); err != nil {
			log.Printf("Error unmarshalling policy %s: %v", key, err)
			continue
		}
		newPolicies[policy.ID] = policy
	}
	policies = newPolicies
	log.Printf("Loaded %d policies from Redis.", len(policies))
	return nil
}

// loadModelProfilesFromRedis fetches all model profiles from Redis into the cache.
func loadModelProfilesFromRedis(ctx context.Context) error {
	mu.Lock()
	defer mu.Unlock()

	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}

	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, err)
			continue
		}
		var profile ModelProfile
		if err := json.Unmarshal([]byte(val), &profile); err != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, err)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))
	return nil
}

// listenForRedisPolicyUpdates listens for Redis Pub/Sub messages for policy updates.
func listenForRedisPolicyUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), POLICY_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis policy updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadPoliciesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading policies from Redis: %v", err)
		} else {
			log.Println("Policies cache refreshed due to pending updates...")
		}
	}
}

// listenForRedisModelProfileUpdates listens for Redis Pub/Sub messages for model profile updates.
func listenForRedisModelProfileUpdates() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL)
	defer pubsub.Close()
	log.Println("Listening for Redis model profile updates...")

	for msg := range pubsub.Channel() {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if err := loadModelProfilesFromRedis(context.Background()); err != nil {
			log.Printf("Error reloading model profiles from Redis: %v", err)
		} else {
			log.Println("Model profiles cache refreshed due to pending updates...")
		}
	}
}

// getModelProfileByID safely retrieves a model profile from the cache.
// This function IS used by parseTokensAndCost and also in the cache hit path directly.
func getModelProfileByID(id string) (ModelProfile, bool) {
	mu.RLock()
	defer mu.RUnlock()
	mp, ok := modelProfiles[id]
	return mp, ok
}

// getCacheKey generates a cache key based on the prompt and model.
func getCacheKey(prompt, model string) string {
	// Normalize prompt for consistent caching (e.g., lowercase, remove non-alphanumeric)
	normalizedPrompt := strings.ToLower(prompt)
	// Remove non-alphanumeric characters and collapse spaces
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	normalizedPrompt = reg.ReplaceAllString(normalizedPrompt, "")
	return REDIS_CACHE_KEY_PREFIX + normalizedPrompt + ":" + model
}

// getSemanticCacheKey generates a cache key for the semantic cache.
func getSemanticCacheKey(embedding []float64, model string) string {
	// Create a hash of the embedding
	hasher := sha256.New()
	for _, val := range embedding {
		hasher.Write([]byte(fmt.Sprintf("%f", val)))
	}
	embeddingHash := fmt.Sprintf("%x", hasher.Sum(nil))

	return SEMANTIC_CACHE_KEY_PREFIX + embeddingHash + ":" + model
}

// generateEmbedding creates an embedding vector for a
func generateEmbedding(ctx context.Context, text string) ([]float64, error) {
	// Lock to prevent parallel embedding requests
	embedderLock.Lock()
	defer embedderLock.Unlock()

	// Get the embedding model profile (using a fixed ID for the embedder)
	embeddingModelID := "text-embedding-3-small" // Default embedding model
	profile, ok := getModelProfileByID(embeddingModelID)
	if !ok {
		embeddingModelID = "text-embedding-ada-002" // Fallback embedding model
		profile, ok = getModelProfileByID(embeddingModelID)
		if !ok {
			return nil, fmt.Errorf("embedding model profile not found for %s or fallback", embeddingModelID)
		}
	}

	// Prepare the embedding request
	embeddingReq := EmbeddingRequest{
		Model: embeddingModelID,
		Input: []string{text},
	}

	reqBody, err := json.Marshal(embeddingReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal embedding request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", profile.BackendURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if profile.APIKey != "" {
		req.Header.Set("Authorization", "Bearer "+profile.APIKey)
	}

	// Call the embedding API
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("embedding API call failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("embedding API returned non-200 status: %d, body: %s", resp.StatusCode, string(body))
	}

	// Parse the response
	var embeddingResp EmbeddingResponse
	if err := json.NewDecoder(resp.Body).Decode(&embeddingResp); err != nil {
		return nil, fmt.Errorf("failed to decode embedding response: %w", err)
	}

	if len(embeddingResp.Data) == 0 || len(embeddingResp.Data[0].Embedding) == 0 {
		return nil, fmt.Errorf("embedding response contains no embeddings")
	}

	return embeddingResp.Data[0].Embedding, nil
}

// cosineSimilarity calculates the cosine similarity between two embedding vectors.
func cosineSimilarity(a, b []float64) (float64, error) {
	if len(a) != len(b) {
		return 0, fmt.Errorf("embedding vectors have different dimensions: %d vs %d", len(a), len(b))
	}

	var dotProduct, normA, normB float64
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	normA = math.Sqrt(normA)
	normB = math.Sqrt(normB)

	if normA == 0 || normB == 0 {
		return 0, fmt.Errorf("one of the embedding vectors has zero norm")
	}

	return dotProduct / (normA * normB), nil
}

// getLLMResponseFromCache attempts to retrieve an LLM response from Redis cache.
func getLLMResponseFromCache(ctx context.Context, cacheKey string) ([]byte, error) {
	val, err := redisClient.Get(ctx, cacheKey).Result()
	if err == redis.Nil {
		return nil, nil // Not found in cache
	}
	if err != nil {
		return nil, fmt.Errorf("error retrieving from cache: %w", err)
	}
	return []byte(val), nil
}

// findSimilarPromptInCache attempts to find a semantically similar prompt in the cache.
func findSimilarPromptInCache(ctx context.Context, prompt, model string) ([]byte, error) {
	// First, generate embedding for the current prompt
	promptEmbedding, err := generateEmbedding(ctx, prompt)
	if err != nil {
		log.Printf("Error generating embedding for semantic cache lookup: %v", err)
		return nil, err
	}

	// Generate cache key for the current embedding
	semanticKey := getSemanticCacheKey(promptEmbedding, model)

	// Try in-memory cache first (faster)
	if cachedEntry, found := semanticCache.Load(semanticKey); found {
		entry := cachedEntry.(SemanticCacheEntry)
		log.Printf("Semantic cache HIT (in-memory) for key %s", semanticKey)
		return entry.Response, nil
	}

	// Check Redis for exact match on embedding hash
	redisEntry, err := redisClient.Get(ctx, semanticKey).Result()
	if err == nil {
		// Found an exact match on embedding hash
		var entry SemanticCacheEntry
		if err := json.Unmarshal([]byte(redisEntry), &entry); err == nil {
			// Store in memory cache for faster future access
			semanticCache.Store(semanticKey, entry)
			log.Printf("Semantic cache HIT (Redis exact) for key %s", semanticKey)
			return entry.Response, nil
		}
	}

	// Scan for similar embeddings (slower operation)
	keys, err := redisClient.Keys(ctx, SEMANTIC_CACHE_KEY_PREFIX+"*:"+model).Result()
	if err != nil || len(keys) == 0 {
		return nil, nil // No semantic cache entries or error
	}

	// Fetch all semantic cache entries and check similarity
	for _, key := range keys {
		redisEntry, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			continue
		}

		var entry SemanticCacheEntry
		if err := json.Unmarshal([]byte(redisEntry), &entry); err != nil {
			continue
		}

		// Calculate similarity
		similarity, err := cosineSimilarity(promptEmbedding, entry.Embedding)
		if err != nil {
			continue
		}

		if similarity >= SEMANTIC_SIMILARITY_THRESHOLD {
			log.Printf("Semantic cache HIT (similarity=%.4f) for prompt '%s'",
				similarity, truncateString(prompt, 50))

			// Store in memory cache for faster future access
			semanticCache.Store(semanticKey, entry)

			return entry.Response, nil
		}
	}

	return nil, nil // No similar entries found
}

// truncateString limits a string to a maximum length for logging.
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// setLLMResponseInCache stores an LLM response in Redis cache with a TTL.
func setLLMResponseInCache(ctx context.Context, cacheKey string, responseBody []byte) error {
	err := redisClient.Set(ctx, cacheKey, responseBody, CACHE_TTL).Err()
	if err != nil {
		return fmt.Errorf("error setting cache: %w", err)
	}
	return nil
}

// storeInSemanticCache stores a response in the semantic cache.
func storeInSemanticCache(ctx context.Context, prompt, model string, responseBody []byte,
	inputTokens, outputTokens int64, totalCost float64) error {

	// Generate embedding for the prompt
	embedding, err := generateEmbedding(ctx, prompt)
	if err != nil {
		return fmt.Errorf("failed to generate embedding for semantic cache: %w", err)
	}

	// Create cache entry
	entry := SemanticCacheEntry{
		Prompt:       prompt,
		Response:     responseBody,
		Embedding:    embedding,
		Model:        model,
		Timestamp:    time.Now(),
		InputTokens:  inputTokens,
		OutputTokens: outputTokens,
		TotalCost:    totalCost,
	}

	// Serialize the entry
	entryBytes, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal semantic cache entry: %w", err)
	}

	// Generate cache key based on embedding
	semanticKey := getSemanticCacheKey(embedding, model)

	// Store in Redis with longer TTL
	if err := redisClient.Set(ctx, semanticKey, entryBytes, SEMANTIC_CACHE_TTL).Err(); err != nil {
		return fmt.Errorf("failed to store semantic cache entry in Redis: %w", err)
	}

	// Also store in memory for faster lookup
	entry.EmbeddingHash = semanticKey // Store key for reference
	semanticCache.Store(semanticKey, entry)

	log.Printf("Stored response in semantic cache with key: %s", semanticKey)
	return nil
}

// processPromptTemplate applies variable substitution to a prompt template
// It looks up the template by ID in the promptTemplates map and replaces
// placeholders like {{variable}} with values from the provided variables map.
// Falls back to default values defined in the template if a variable is not provided.
func processPromptTemplate(templateID string, variables map[string]string) (string, error) {
	mu.RLock()
	template, exists := promptTemplates[templateID]
	mu.RUnlock()

	if !exists {
		return "", fmt.Errorf("prompt template with ID %s not found", templateID)
	}

	// Start with the template text
	result := template.Template

	// Apply variable substitution
	for key, defaultValue := range template.Variables {
		// Check if the variable is provided in the input variables
		value, exists := variables[key]
		if !exists {
			// Use default value if no custom value provided
			value = defaultValue
		}

		// Replace the variable placeholder with the value
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}

	return result, nil
}

// estimateTokenCount provides a rough estimate of token count based on whitespace
// This is a simple heuristic and not a precise count
func estimateTokenCount(text string) int {
	// Simple approximation: count words and add 20%
	words := strings.Fields(text)
	return int(float64(len(words)) * 1.2)
}

// detectAndReduceRepetition identifies and reduces repetitive patterns in text
func detectAndReduceRepetition(text string) string {
	// Simple repetition detection for phrases
	// Look for repeated phrases of 5+ words that appear multiple times
	words := strings.Fields(text)
	if len(words) < 10 {
		return text // Too short to have meaningful repetition
	}

	// Construct sliding windows of phrases and count them
	phrases := make(map[string]int)

	// Check different phrase lengths
	for phraseLen := 5; phraseLen < 15 && phraseLen < len(words)/2; phraseLen++ {
		for i := 0; i <= len(words)-phraseLen; i++ {
			phrase := strings.Join(words[i:i+phraseLen], " ")
			phrases[phrase]++
		}
	}

	// Replace repeated phrases with a single instance
	result := text
	for phrase, count := range phrases {
		if count > 1 && len(phrase) > 20 { // Only meaningful phrases
			// Replace all but the first occurrence
			parts := strings.SplitN(result, phrase, 2)
			if len(parts) == 2 {
				// Keep the first occurrence and eliminate others
				result = parts[0] + phrase + strings.ReplaceAll(parts[1], phrase, "")
			}
		}
	}

	return result
}

// Constants for prompt compression are defined in prompt_optimization.go

// transformRequestPayload transforms the incoming OpenAI-like request body
// into the format expected by the target LLM backend.
func transformRequestPayload(originalBody []byte, backendType string) ([]byte, error) {
	if backendType == "google-external" || backendType == "google" {
		// Incoming request is OpenAI-like
		var openaiReq OpenAICompletionRequest
		if err := json.Unmarshal(originalBody, &openaiReq); err != nil {
			return nil, fmt.Errorf("failed to unmarshal OpenAI-like request: %w", err)
		}

		// Convert to Gemini format
		var geminiContents []GeminiContent
		for _, msg := range openaiReq.Messages {
			geminiContents = append(geminiContents, GeminiContent{
				Parts: []GeminiPart{{Text: msg["content"]}},
				Role:  msg["role"],
			})
		}

		geminiReq := GeminiGenerateContentRequest{
			Contents: geminiContents,
		}

		if openaiReq.MaxTokens > 0 {
			geminiReq.GenerationConfig.MaxOutputTokens = openaiReq.MaxTokens
		}

		translatedBody, err := json.Marshal(geminiReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal Gemini-format request: %w", err)
		}
		log.Printf("Successfully translated OpenAI-like payload to Gemini format for backend type: %s", backendType)
		return translatedBody, nil
	}
	// For other backend types (OpenAI, Anthropic, VLLM), assume the incoming format is compatible.
	// In a real robust system, you'd have similar translation logic for all non-standard backends.
	return originalBody, nil
}

// handleLLMRequest handles incoming LLM requests, routes them, and logs inference.
func handleLLMRequest(w http.ResponseWriter, r *http.Request) {
	requestID := uuid.New().String()
	logEntry := InferenceLog{
		RequestID: requestID,
		Timestamp: time.Now(),
		Method:    r.Method,
		Path:      r.URL.Path,
		ClientIP:  r.RemoteAddr,
		UserAgent: r.UserAgent(),
	}

	// Extract conversation_id from request headers
	conversationID := r.Header.Get("X-Conversation-Id")
	if conversationID == "" {
		conversationID = uuid.New().String() // Generate a new one if not provided
		log.Printf("Request %s: No conversation_id provided. Generated new ID: %s", requestID, conversationID)
	}
	logEntry.ConversationID = conversationID
	log.Printf("Handling request %s: %s %s", requestID, r.Method, r.URL.Path)

	// NEW: Extract UserID and UserRoles from headers
	logEntry.UserID = r.Header.Get("X-User-Id")
	userRolesHeader := r.Header.Get("X-User-Roles")
	if userRolesHeader != "" {
		// Split by comma and trim spaces for each role
		roles := strings.Split(userRolesHeader, ",")
		cleanedRoles := make([]string, 0, len(roles))
		for _, role := range roles {
			trimmedRole := strings.TrimSpace(role)
			if trimmedRole != "" {
				cleanedRoles = append(cleanedRoles, trimmedRole)
			}
		}
		logEntry.UserRoles = cleanedRoles
	} else {
		logEntry.UserRoles = []string{} // Ensure it's an empty slice if header is not present
	}

	// Read the request body once and store it locally
	requestBodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Request %s: Error reading request body: %v", requestID, err)
		http.Error(w, "Error reading request body", http.StatusInternalServerError)
		return
	}
	r.Body = io.NopCloser(bytes.NewBuffer(requestBodyBytes)) // Restore body for later reads

	// Set the request body snippet immediately after reading
	logEntry.RequestBodySnippet = string(requestBodyBytes)

	// Parse the model requested by the client (if available in the request body)
	var reqBodyMap map[string]interface{}
	modelRequested := "unknown"
	prompt := "unknown"
	streamRequested := false // Flag for streaming responses

	if err := json.Unmarshal(requestBodyBytes, &reqBodyMap); err == nil {
		if model, ok := reqBodyMap["model"].(string); ok {
			modelRequested = model
		}

		// Check for template ID in the request
		templateID, hasTemplate := reqBodyMap["template_id"].(string)
		templateVars, hasVars := reqBodyMap["template_variables"].(map[string]interface{})

		// Process via template if template_id is provided
		if hasTemplate && templateID != "" {
			// Convert variables to proper format
			vars := make(map[string]string)
			if hasVars {
				for k, v := range templateVars {
					if strVal, ok := v.(string); ok {
						vars[k] = strVal
					} else if val, err := json.Marshal(v); err == nil {
						vars[k] = string(val)
					}
				}
			}

			// Process the template
			processedPrompt, err := processPromptTemplate(templateID, vars)
			if err != nil {
				log.Printf("Request %s: Error processing template %s: %v", requestID, templateID, err)
				// Fall back to regular prompt processing if template fails
			} else {
				// If template processing succeeded, use the processed prompt
				prompt = processedPrompt
				log.Printf("Request %s: Successfully processed template %s", requestID, templateID)

				// Update request body with processed prompt if it contains messages
				if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
					if firstMessage, ok := messages[0].(map[string]interface{}); ok {
						firstMessage["content"] = processedPrompt
						if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
							requestBodyBytes = modifiedBody
							r.Body = io.NopCloser(bytes.NewBuffer(requestBodyBytes))
						}
					}
				}
			}
		} else {
			// Standard prompt extraction
			if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
				if firstMessage, ok := messages[0].(map[string]interface{}); ok {
					if content, ok := firstMessage["content"].(string); ok {
						prompt = content
					}
				}
			}
		}

		if stream, ok := reqBodyMap["stream"].(bool); ok {
			streamRequested = stream
		}
	}
	logEntry.ModelRequested = modelRequested
	logEntry.Stream = streamRequested // Ensure stream status is logged

	// --- Step 0: Check Cache for Response ---
	// First check the traditional exact-match cache
	cacheKey := getCacheKey(prompt, modelRequested)

	// First check in-memory cache for hot items
	inMemoryCacheItem, inMemoryHit := inMemoryCache.Get(cacheKey)
	if inMemoryHit {
		inMemoryValue := inMemoryCacheItem.(string)
		log.Printf("Request %s: In-memory Cache HIT for prompt '%s'. Serving from memory.", requestID, truncateString(prompt, 50))

		// Update cache analytics
		analyticsLock.Lock()
		cacheAnalytics.Hits++
		analyticsLock.Unlock()

		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Hit", "true")
		w.Header().Set("X-Cache-Level", "memory")
		w.Write([]byte(inMemoryValue))

		logEntry.StatusCode = int32(http.StatusOK)
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logEntry.ResponseBodySnippet = inMemoryValue

		// For cache hits, the policy applied is "cache-hit" and model used is the model requested
		logEntry.PolicyIDApplied = "memory-cache-hit"
		logEntry.SelectedBackendID = "memory-cache"
		logEntry.ModelUsed = modelRequested

		profile, ok := getModelProfileByID(modelRequested)
		if ok {
			inputTokens, outputTokens, cost := parseTokensAndCost([]byte(inMemoryValue), modelRequested, profile)
			logEntry.InputTokens = inputTokens
			logEntry.OutputTokens = outputTokens
			logEntry.TotalCost = cost

			// Update tokens and cost saved in analytics
			analyticsLock.Lock()
			cacheAnalytics.TokensSaved += inputTokens + outputTokens
			cacheAnalytics.CostSaved += cost
			analyticsLock.Unlock()

			log.Printf("Request %s: Memory cache hit saved %d tokens and $%.6f",
				requestID, inputTokens+outputTokens, cost)
		}

		logEntry.TaskType = "memory_cached_response"
		logInferenceLog(logEntry)
		return
	}

	// Then check Redis cache
	cachedResponse, cacheErr := getLLMResponseFromCache(r.Context(), cacheKey)
	if cacheErr != nil {
		log.Printf("Request %s: Error checking cache for key '%s': %v", requestID, cacheKey, cacheErr)
	} else if cachedResponse != nil {
		log.Printf("Request %s: Redis Cache HIT for prompt '%s'. Serving from Redis cache.", requestID, truncateString(prompt, 50))

		// Update cache analytics
		analyticsLock.Lock()
		cacheAnalytics.Hits++
		analyticsLock.Unlock()

		// Store in memory cache for faster future access
		inMemoryCache.Set(cacheKey, string(cachedResponse), CACHE_TTL)

		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Hit", "true")
		w.Header().Set("X-Cache-Level", "redis")
		w.Write(cachedResponse)

		logEntry.StatusCode = int32(http.StatusOK)
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logEntry.ResponseBodySnippet = string(cachedResponse) // Store full cached response for now

		// For cache hits, the policy applied is "cache-hit" and model used is the model requested
		logEntry.PolicyIDApplied = "redis-cache-hit"
		logEntry.SelectedBackendID = "redis-cache" // Designate a special "cache" backend ID
		logEntry.ModelUsed = modelRequested        // Use the model that was requested for cache hit logging

		// Attempt to parse tokens from cached response for logging
		profile, ok := getModelProfileByID(modelRequested) // Use modelRequested here
		if ok {
			inputTokens, outputTokens, cost := parseTokensAndCost(cachedResponse, modelRequested, profile)
			logEntry.InputTokens = inputTokens
			logEntry.OutputTokens = outputTokens
			logEntry.TotalCost = cost

			// Update tokens and cost saved in analytics
			analyticsLock.Lock()
			cacheAnalytics.TokensSaved += inputTokens + outputTokens
			cacheAnalytics.CostSaved += cost
			analyticsLock.Unlock()

			log.Printf("Request %s: Redis cache hit saved %d tokens and $%.6f",
				requestID, inputTokens+outputTokens, cost)
		} else {
			log.Printf("Request %s: Model profile for cached response model '%s' not found. Cannot calculate tokens/cost for cache hit.", requestID, modelRequested)
			// Ensure these are zeroed out if profile not found, they default to 0 in struct but good to be explicit
			logEntry.InputTokens = 0
			logEntry.OutputTokens = 0
			logEntry.TotalCost = 0.0
		}
		logEntry.TaskType = "redis_cached_response" // Set task type for cached response
		logInferenceLog(logEntry)                   // Log cache hit
		return                                      // Request handled
	}

	// If exact match cache miss, try semantic cache for similar queries
	// Only for non-streaming factual queries and code generation for better accuracy
	if !streamRequested && (reqBodyMap["stream"] == nil || reqBodyMap["stream"] == false) &&
		(logEntry.TaskType == "factual_query" || logEntry.TaskType == "code_generation" ||
			logEntry.TaskType == "unknown" || logEntry.TaskType == "other") {

		semanticCachedResponse, semanticErr := findSimilarPromptInCache(r.Context(), prompt, modelRequested)
		if semanticErr != nil {
			log.Printf("Request %s: Error checking semantic cache: %v", requestID, semanticErr)
		} else if semanticCachedResponse != nil {
			log.Printf("Request %s: Semantic Cache HIT for prompt '%s'. Serving from semantic cache.",
				requestID, truncateString(prompt, 50))

			// Update cache analytics
			analyticsLock.Lock()
			cacheAnalytics.SemanticHits++
			analyticsLock.Unlock()

			// Also add to in-memory cache for faster future access if exact match requested
			exactCacheKey := getCacheKey(prompt, modelRequested)
			inMemoryCache.Set(exactCacheKey, string(semanticCachedResponse), CACHE_TTL)

			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("X-Cache-Hit", "true")
			w.Header().Set("X-Cache-Level", "semantic")
			w.Write(semanticCachedResponse)

			logEntry.StatusCode = int32(http.StatusOK)
			logEntry.ResponseTimestamp = time.Now()
			logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
			logEntry.ResponseBodySnippet = string(semanticCachedResponse)

			// For semantic cache hits, the policy applied is "semantic-cache-hit"
			logEntry.PolicyIDApplied = "semantic-cache-hit"
			logEntry.SelectedBackendID = "semantic-cache"
			logEntry.ModelUsed = modelRequested

			// Attempt to parse tokens from cached response for logging
			profile, ok := getModelProfileByID(modelRequested)
			if ok {
				inputTokens, outputTokens, cost := parseTokensAndCost(semanticCachedResponse, modelRequested, profile)
				logEntry.InputTokens = inputTokens
				logEntry.OutputTokens = outputTokens
				logEntry.TotalCost = cost

				// Update tokens and cost saved in analytics
				analyticsLock.Lock()
				cacheAnalytics.TokensSaved += inputTokens + outputTokens
				cacheAnalytics.CostSaved += cost
				analyticsLock.Unlock()

				log.Printf("Request %s: Semantic cache hit saved %d tokens and $%.6f",
					requestID, inputTokens+outputTokens, cost)
			}

			logEntry.TaskType = "semantic_cached_response"
			logInferenceLog(logEntry)
			return
		}
	}
	// Optimize the prompt if enabled
	optimizedPrompt := enhancedOptimizePrompt(prompt, true)
	originalPromptLength := len(prompt)
	optimizedPromptLength := len(optimizedPrompt)

	if optimizedPrompt != prompt {
		log.Printf("Request %s: Prompt optimized from %d to %d chars",
			requestID, originalPromptLength, optimizedPromptLength)

		// Update the request body with optimized prompt
		if reqBodyMap != nil {
			if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
				if firstMessage, ok := messages[0].(map[string]interface{}); ok {
					firstMessage["content"] = optimizedPrompt
					if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
						requestBodyBytes = modifiedBody
					}
				}
			}
		}
	}

	// Log token budget if provided

	// --- Step 1: Call AI Optimizer for Routing Decision ---
	// Prepare the request to AI Optimizer's /route endpoint
	// This is where the intelligent routing decision happens
	optReq := OptimizationRequest{
		Prompt:          prompt,
		Model:           modelRequested,
		UserID:          logEntry.UserID,
		UserRoles:       logEntry.UserRoles,
		DataSensitivity: r.Header.Get("X-Data-Sensitivity"),
		PreferredLLMID:  r.Header.Get("X-Preferred-LLM-ID"), // Pass through preferred LLM if specified
		TokenBudget:     getTokenBudget(r),                  // Extract token budget from headers
	}

	// Log token budget if provided
	if optReq.TokenBudget != nil {
		log.Printf("Request %s: Token budget - MaxInputTokens: %d, MaxOutputTokens: %d",
			requestID, optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens)
	} else {
		log.Printf("Request %s: No token budget provided", requestID)
	}

	// Log token budget if provided
	if optReq.TokenBudget != nil {
		log.Printf("Request %s: Token budget - MaxInputTokens: %d, MaxOutputTokens: %d",
			requestID, optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens)
	} else {
		log.Printf("Request %s: No token budget provided", requestID)
	}

	// Log token budget if provided
	if optReq.TokenBudget != nil {
		log.Printf("Request %s: Token budget - MaxInputTokens: %d, MaxOutputTokens: %d",
			requestID, optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens)
	} else {
		log.Printf("Request %s: No token budget provided", requestID)
	}

	// Log token budget if provided
	if optReq.TokenBudget != nil {
		log.Printf("Request %s: Token budget - MaxInputTokens: %d, MaxOutputTokens: %d",
			requestID, optReq.TokenBudget.MaxInputTokens, optReq.TokenBudget.MaxOutputTokens)
	} else {
		log.Printf("Request %s: No token budget provided", requestID)
	}

	// Update cache analytics for cache miss
	analyticsLock.Lock()
	cacheAnalytics.Misses++
	analyticsLock.Unlock()

	optReqJSON, err := json.Marshal(optReq)
	if err != nil {
		log.Printf("Request %s: Error marshaling optimizer request: %v", requestID, err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Optimize the prompt if enabled
	optimizedPrompt = enhancedOptimizePrompt(prompt, true)
	if optimizedPrompt != prompt {
		log.Printf("Request %s: Prompt optimized from %d to %d chars",
			requestID, len(prompt), len(optimizedPrompt))

		// Update the request body with optimized prompt
		if reqBodyMap != nil {
			if messages, ok := reqBodyMap["messages"].([]interface{}); ok && len(messages) > 0 {
				if firstMessage, ok := messages[0].(map[string]interface{}); ok {
					firstMessage["content"] = optimizedPrompt
					if modifiedBody, err := json.Marshal(reqBodyMap); err == nil {
						requestBodyBytes = modifiedBody
					}
				}
			}
		}
	}

	// Enforce token budget if provided

	// Call AI Optimizer's /route endpoint
	log.Printf("Request %s: Calling AI Optimizer for routing decision", requestID)
	aiOptResp, err := http.Post(aiOptimizerURL+"/route", "application/json", bytes.NewBuffer(optReqJSON))
	if err != nil {
		log.Printf("Request %s: Error calling AI Optimizer: %v", requestID, err)
		http.Error(w, "Error determining optimal backend", http.StatusInternalServerError)
		return
	}
	defer aiOptResp.Body.Close()

	// Parse AI Optimizer response
	var routeResp AIOptimizerRouteResponse
	if err := json.NewDecoder(aiOptResp.Body).Decode(&routeResp); err != nil {
		log.Printf("Request %s: Error parsing AI Optimizer response: %v", requestID, err)
		http.Error(w, "Error processing routing decision", http.StatusInternalServerError)
		return
	}

	// Check if the AI Optimizer returned an error
	if routeResp.Error != "" {
		log.Printf("Request %s: AI Optimizer returned error: %s", requestID, routeResp.Error)
		http.Error(w, "Error from routing service: "+routeResp.Error, http.StatusInternalServerError)
		return
	}

	// Check if AI Optimizer has a cached response
	if routeResp.FromCache && routeResp.CachedResponseBody != "" {
		log.Printf("Request %s: AI Optimizer returned cached response for prompt '%s'",
			requestID, truncateString(prompt, 50))

		// Write cached response from AI Optimizer
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Cache-Hit", "true")
		w.Header().Set("X-Cache-Level", "ai-optimizer")
		w.Write([]byte(routeResp.CachedResponseBody))

		// Complete logging and return
		logEntry.StatusCode = int32(http.StatusOK)
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(time.Since(logEntry.Timestamp).Milliseconds())
		logEntry.ResponseBodySnippet = routeResp.CachedResponseBody
		logEntry.SelectedBackendID = "ai-optimizer-cache"
		logEntry.PolicyIDApplied = routeResp.PolicyIDApplied
		logEntry.ModelUsed = routeResp.ModelUsed
		logEntry.TaskType = "ai_optimizer_cached_response"

		// Attempt to parse tokens from cached response for logging
		profile, ok := getModelProfileByID(routeResp.ModelUsed)
		if ok {
			inputTokens, outputTokens, cost := parseTokensAndCost(
				[]byte(routeResp.CachedResponseBody), routeResp.ModelUsed, profile)
			logEntry.InputTokens = inputTokens
			logEntry.OutputTokens = outputTokens
			logEntry.TotalCost = cost

			// Update tokens and cost saved in analytics
			analyticsLock.Lock()
			cacheAnalytics.TokensSaved += inputTokens + outputTokens
			cacheAnalytics.CostSaved += cost
			analyticsLock.Unlock()
		}

		logInferenceLog(logEntry)
		return
	}

	// --- Step 2: Route Request to Selected Backend ---
	log.Printf("Request %s: Routing to %s (%s) via policy %s",
		requestID, routeResp.SelectedBackendID, routeResp.BackendURL, routeResp.PolicyIDApplied)

	// Save decision from AI Optimizer in our log entry
	logEntry.SelectedBackendID = routeResp.SelectedBackendID
	logEntry.BackendURL = routeResp.BackendURL
	logEntry.BackendType = routeResp.BackendType
	logEntry.PolicyIDApplied = routeResp.PolicyIDApplied
	logEntry.ModelUsed = routeResp.ModelUsed
	logEntry.TaskType = routeResp.TaskType

	// Transform request payload for the target backend if needed
	transformedBody, err := transformRequestPayload(requestBodyBytes, routeResp.BackendType)
	if err != nil {
		log.Printf("Request %s: Error transforming request payload: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Create request to backend
	backendReq, err := http.NewRequestWithContext(r.Context(), "POST", routeResp.BackendURL, bytes.NewBuffer(transformedBody))
	if err != nil {
		log.Printf("Request %s: Error creating backend request: %v", requestID, err)
		http.Error(w, "Error preparing request for backend", http.StatusInternalServerError)
		return
	}

	// Copy original headers to backend request
	for key, values := range r.Header {
		for _, value := range values {
			backendReq.Header.Add(key, value)
		}
	}

	// Get API key for the selected backend from model profile
	profile, profileExists := getModelProfileByID(routeResp.SelectedBackendID)
	if profileExists && profile.APIKey != "" {
		backendReq.Header.Set("Authorization", "Bearer "+profile.APIKey)
	}

	// Ensure content type is set
	backendReq.Header.Set("Content-Type", "application/json")

	// Call the backend LLM API
	client := &http.Client{Timeout: 60 * time.Second} // Longer timeout for LLM requests
	startTime := time.Now()
	backendResp, err := client.Do(backendReq)
	requestDuration := time.Since(startTime)

	if err != nil {
		log.Printf("Request %s: Error calling backend: %v", requestID, err)
		http.Error(w, "Error from LLM backend: "+err.Error(), http.StatusInternalServerError)
		logEntry.Error = err.Error()
		logEntry.StatusCode = http.StatusInternalServerError
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(requestDuration.Milliseconds())
		logInferenceLog(logEntry)
		return
	}
	defer backendResp.Body.Close()

	// Read the backend response
	backendResponseBody, err := io.ReadAll(backendResp.Body)
	if err != nil {
		log.Printf("Request %s: Error reading backend response: %v", requestID, err)
		http.Error(w, "Error reading LLM response", http.StatusInternalServerError)
		logEntry.Error = err.Error()
		logEntry.StatusCode = http.StatusInternalServerError
		logEntry.ResponseTimestamp = time.Now()
		logEntry.LatencyMs = float64(requestDuration.Milliseconds())
		logInferenceLog(logEntry)
		return
	}

	// Parse token usage and cost from the response
	inputTokens, outputTokens, totalCost := parseTokensAndCost(backendResponseBody, routeResp.ModelUsed, profile)
	logEntry.InputTokens = inputTokens
	logEntry.OutputTokens = outputTokens
	logEntry.TotalCost = totalCost

	// Log token usage
	log.Printf("Request %s: Used %d input tokens, %d output tokens, $%.6f cost",
		requestID, inputTokens, outputTokens, totalCost)

	// Store response in cache for future requests if not a streaming response
	if !streamRequested {
		if err := setLLMResponseInCache(r.Context(), cacheKey, backendResponseBody); err != nil {
			log.Printf("Request %s: Warning - Failed to store response in cache: %v", requestID, err)
		} else {
			// Also store in memory cache for faster access
			inMemoryCache.Set(cacheKey, string(backendResponseBody), CACHE_TTL)
			log.Printf("Request %s: Response stored in cache with key: %s", requestID, cacheKey)
		}

		// Also store in semantic cache for similar future queries
		// Only do this for non-streaming responses and factual queries
		if logEntry.TaskType == "factual_query" || logEntry.TaskType == "code_generation" ||
			logEntry.TaskType == "unknown" || logEntry.TaskType == "other" {
			if err := storeInSemanticCache(r.Context(), prompt, routeResp.ModelUsed, backendResponseBody,
				inputTokens, outputTokens, totalCost); err != nil {
				log.Printf("Request %s: Warning - Failed to store in semantic cache: %v", requestID, err)
			}
		}
	}

	// Copy all headers from backend response
	for key, values := range backendResp.Header {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// Complete logging
	logEntry.StatusCode = int32(backendResp.StatusCode)
	logEntry.ResponseTimestamp = time.Now()
	logEntry.LatencyMs = float64(requestDuration.Milliseconds())
	logEntry.ResponseBodySnippet = string(backendResponseBody)
	logInferenceLog(logEntry)

	// Return the backend response
	w.WriteHeader(backendResp.StatusCode)
	w.Write(backendResponseBody)
}

// parseTokensAndCost extracts token usage and calculates cost from response.
func parseTokensAndCost(responseBody []byte, _ string, profile ModelProfile) (int64, int64, float64) {
	var inputTokens, outputTokens int64
	var totalCost float64

	// Try to parse the response based on the API format
	var openaiResp OpenAICompletionResponse
	var anthropicResp AnthropicMessagesResponse
	var geminiResp GeminiGenerateContentResponse

	// Try to parse as OpenAI format first (most common)
	if err := json.Unmarshal(responseBody, &openaiResp); err == nil {
		if openaiResp.Usage.PromptTokens > 0 || openaiResp.Usage.CompletionTokens > 0 {
			inputTokens = int64(openaiResp.Usage.PromptTokens)
			outputTokens = int64(openaiResp.Usage.CompletionTokens)
		}
	} else if err := json.Unmarshal(responseBody, &anthropicResp); err == nil {
		// Try Anthropic format
		if anthropicResp.Usage.InputTokens > 0 || anthropicResp.Usage.OutputTokens > 0 {
			inputTokens = int64(anthropicResp.Usage.InputTokens)
			outputTokens = int64(anthropicResp.Usage.OutputTokens)
		}
	} else if err := json.Unmarshal(responseBody, &geminiResp); err == nil {
		// Try Gemini format
		if geminiResp.UsageMetadata.PromptTokenCount > 0 || geminiResp.UsageMetadata.CandidatesTokenCount > 0 {
			inputTokens = int64(geminiResp.UsageMetadata.PromptTokenCount)
			outputTokens = int64(geminiResp.UsageMetadata.CandidatesTokenCount)
		}
	}

	// Calculate cost based on the model profile's pricing
	if inputTokens > 0 || outputTokens > 0 {
		// Use the provided profile directly
		totalCost = float64(inputTokens)*profile.CostPerInputToken +
			float64(outputTokens)*profile.CostPerOutputToken
	}

	return inputTokens, outputTokens, totalCost
}

// logInferenceLog sends the inference log to Kafka for analysis.
func logInferenceLog(log InferenceLog) {
	// Marshal the log entry to JSON
	logJSON, err := json.Marshal(log)
	if err != nil {
		fmt.Printf("Error marshaling inference log: %v\n", err)
		return
	}

	// Write to Kafka
	err = kafkaWriter.WriteMessages(context.Background(),
		kafka.Message{
			Key:   []byte(log.RequestID),
			Value: logJSON,
		},
	)
	if err != nil {
		fmt.Printf("Error writing inference log to Kafka: %v\n", err)
	}
}

// handleCacheAnalyticsRequest returns cache performance metrics.
func handleCacheAnalyticsRequest(w http.ResponseWriter, r *http.Request) {
	analyticsLock.RLock()
	defer analyticsLock.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(cacheAnalytics)
}

// handleCacheManagementRequest handles cache administrative operations.
func handleCacheManagementRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Action string `json:"action"`
		Key    string `json:"key,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	switch req.Action {
	case "clear_all":
		// Clear all cache entries
		keys, err := redisClient.Keys(ctx, REDIS_CACHE_KEY_PREFIX+"*").Result()
		if err != nil {
			http.Error(w, fmt.Sprintf("Error retrieving cache keys: %v", err), http.StatusInternalServerError)
			return
		}

		if len(keys) > 0 {
			if err := redisClient.Del(ctx, keys...).Err(); err != nil {
				http.Error(w, fmt.Sprintf("Error clearing cache: %v", err), http.StatusInternalServerError)
				return
			}
		}

		// Clear semantic cache
		semanticKeys, err := redisClient.Keys(ctx, SEMANTIC_CACHE_KEY_PREFIX+"*").Result()
		if err == nil && len(semanticKeys) > 0 {
			_ = redisClient.Del(ctx, semanticKeys...)
		}

		// Clear in-memory cache
		inMemoryCache.Flush()
		semanticCache = sync.Map{}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"success","message":"Cache cleared"}`))

	case "delete_key":
		if req.Key == "" {
			http.Error(w, "Key parameter required", http.StatusBadRequest)
			return
		}

		// Delete from Redis
		deleted, err := redisClient.Del(ctx, req.Key).Result()
		if err != nil {
			http.Error(w, fmt.Sprintf("Error deleting key: %v", err), http.StatusInternalServerError)
			return
		}

		// Delete from in-memory cache too
		inMemoryCache.Delete(req.Key)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf(`{"status":"success","deleted":%d}`, deleted)))

	case "reset_analytics":
		// Reset analytics counters
		analyticsLock.Lock()
		cacheAnalytics = CacheAnalytics{
			Hits:           0,
			Misses:         0,
			SemanticHits:   0,
			TokensSaved:    0,
			CostSaved:      0.0,
			AverageLatency: 0,
		}
		analyticsLock.Unlock()

		// Also reset Redis counters
		pipe := redisClient.Pipeline()
		pipe.Del(ctx, CACHE_HIT_COUNTER_KEY)
		pipe.Del(ctx, CACHE_MISS_COUNTER_KEY)
		pipe.Del(ctx, SEMANTIC_CACHE_HIT_COUNTER_KEY)
		pipe.Del(ctx, CACHE_ANALYTICS_PREFIX+"tokens_saved")
		pipe.Del(ctx, CACHE_ANALYTICS_PREFIX+"cost_saved")
		pipe.Exec(ctx)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"success","message":"Analytics reset"}`))

	default:
		http.Error(w, "Unknown action", http.StatusBadRequest)
	}
}

// semanticPromptCompression is implemented in prompt_optimization.go

// preserveStructureCompression is implemented in prompt_optimization.go

// essentialContentCompression is implemented in prompt_optimization.go

// getTokenBudget extracts token budget from request headers.
func getTokenBudget(r *http.Request) *TokenBudget {
	// Check for X-Max-Input-Tokens and X-Max-Output-Tokens headers
	maxInputTokensStr := r.Header.Get("X-Max-Input-Tokens")
	maxOutputTokensStr := r.Header.Get("X-Max-Output-Tokens")

	// If either header is present, attempt to parse them
	if maxInputTokensStr != "" || maxOutputTokensStr != "" {
		maxInputTokens, err1 := strconv.Atoi(maxInputTokensStr)
		maxOutputTokens, err2 := strconv.Atoi(maxOutputTokensStr)

		// If there's an error parsing either value, log it and return nil
		if err1 != nil || err2 != nil {
			log.Printf("Error parsing token budget headers: inputErr=%v, outputErr=%v", err1, err2)
			return nil
		}

		// If parsing was successful, return a TokenBudget struct
		return &TokenBudget{
			MaxInputTokens:  maxInputTokens,
			MaxOutputTokens: maxOutputTokens,
			Priority:        10, // Default priority
		}
	}

	// If neither header is present, return nil
	return nil
}
