# k8s/frontend/frontend.yaml
# Combined Kubernetes YAML definitions for the Frontend Dashboard
# Includes Service, ConfigMap, and Deployment

apiVersion: v1
kind: Service
metadata:
  name: frontend-dashboard # Service name for in-cluster communication
spec:
  selector:
    app: frontend-dashboard
  ports:
  - name: http
    protocol: TCP
    port: 80 # Frontend listens on 80
    targetPort: 80
  type: ClusterIP # Use LoadBalancer to expose the dashboard externally

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  labels:
    app: frontend-dashboard
data:
  # The key here will be the filename inside the pod
  default.conf: |
    # Main Nginx configuration file for frontend-dashboard with proxying to internal services.

    user nginx;
    worker_processes auto;

    error_log /var/log/nginx/error.log debug; # Increased log level for troubleshooting
    pid /run/nginx.pid;

    events {
        worker_connections 1024;
    }

    http {
        include       mime.types;
        default_type  application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                         '$status $body_bytes_sent "$http_referer" '
                         '"$http_user_agent" "$http_x_forwarded_for" '
                         'rt=$request_time uct=$upstream_connect_time uht=$upstream_header_time urt=$upstream_response_time';

        access_log /var/log/nginx/access.log main;

        sendfile         on;
        keepalive_timeout  120; # Increased from 65
        client_body_timeout 120s;
        client_header_timeout 120s;
        send_timeout 120s;

        # Resolver for dynamic DNS resolution within Kubernetes
        resolver kube-dns.kube-system.svc.cluster.local valid=5s ipv6=off;

        # Define upstream servers with improved connection handling
        upstream dashboard_api_upstream {
            server dashboard-api.default.svc.cluster.local:8081;
        }

        upstream proxy_gateway_upstream {
            server proxy-gateway.default.svc.cluster.local:8080;
        }

        upstream policy_manager_upstream {
            server policy-manager.default.svc.cluster.local:8083 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        server {
            listen 80;
            server_name localhost;

            # Increase timeouts for all locations
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
            
            # Enable keepalive connections to upstream
            proxy_http_version 1.1;
            proxy_set_header Connection "";

            # 1. LLM inference proxy
            location /v1/ {
                proxy_pass http://proxy_gateway_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_buffering off;
                proxy_cache off;
                chunked_transfer_encoding off;
            }

            # IMPORTANT: Order matters in nginx location blocks!
            # More specific patterns should come before more general ones

            # 2. Policies API - direct pass to policy-manager
            # Keep the existing /policy-api/ endpoint for backward compatibility
            location /policy-api/ {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";
                # Remove the /policy-api/ prefix when proxying to policy-manager
                rewrite ^/policy-api/(.*) /$1 break;
                proxy_pass http://$policy_manager_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/$1";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }
            
            # 2.1 New consistent endpoint for policies - direct pass to policy-manager
            location /api/policies {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";
                proxy_pass http://$policy_manager_backend/policies;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/policies";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }

            location /api/prompts {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";
                proxy_pass http://$policy_manager_backend/api/prompts;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/api/prompts";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }
            
            # 3. Model profiles API - direct pass to policy-manager
            # Support both underscore and hyphen versions for compatibility
            location ~ ^/api/model[-_]profiles {
                # Use variable to ensure DNS resolution happens for each request
                set $policy_manager_backend "policy-manager.default.svc.cluster.local:8083";

                # Rewrite to ensure consistent backend URL regardless of hyphen or underscore
                rewrite ^/api/model-profiles(.*) /model_profiles$1 break;
                rewrite ^/api/model_profiles(.*) /model_profiles$1 break;
                proxy_pass http://$policy_manager_backend/api/model-profiles;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable caching
                proxy_no_cache 1;
                proxy_cache_bypass 1;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "$policy_manager_backend/api/model-profiles";
                add_header X-Debug-Info "Direct policy-manager access";
                
                # Add error handling
                proxy_intercept_errors on;
                error_page 404 502 503 504 = @policy_manager_fallback;
            }

            # 4. Dashboard API endpoints - IMPORTANT: NO REWRITE for these specific paths, directly proxy
            # THIS IS THE UPDATED LINE TO INCLUDE 'inference-logs' and REMOVE 'recent-logs'
            location ~ ^/api/(summary|time-series|inference-logs|backend-latencies|optimal-backend|evaluation-results|curated-data|set-preference) {
                proxy_pass http://dashboard_api_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_intercept_errors on;
                error_log /var/log/nginx/dashboard_api_error.log debug;
                
                # Add debugging headers
                add_header X-Proxy-Response-Time $upstream_response_time;
                add_header X-Request-Time $request_time;
                add_header X-Proxy-Destination "dashboard_api_upstream$request_uri";
            }

            # 5. General /api/ proxy (catch-all for any other /api paths not specifically handled above)
            # This block is less specific than the regex above, so it will only catch paths
            # that don't match the regex. It seems like a general fallback.
            # If you intend for *all* /api/ calls to go to dashboard_api_upstream
            # unless specifically routed to policy-manager, then this is fine.
            # If this is ONLY for dashboard-api, consider making it more specific
            # or removing it if the regex above covers all dashboard-api paths.
            # For now, keeping it as is, assuming it catches other unlisted /api/ paths.
            # Note: Given the more specific regex above for common dashboard API paths,
            # this block might not be hit often for dashboard data.
            location /api/ {
                # For dashboard-api, we'll keep the rewrite if it's meant to strip /api/ for some internal backend endpoints
                # However, for direct proxying to dashboard_api_upstream, a rewrite might not be needed
                # if dashboard-api expects the full /api/ path.
                # Assuming dashboard-api expects full /api/ path given previous contexts for specific paths.
                # If this location block is intended as a general catch-all for /api/ and needs to strip the /api/ prefix
                # before forwarding, then the rewrite would be needed. Otherwise, direct proxying without rewrite is typical.
                # For consistency with the specific /api/ routes to dashboard_api_upstream, let's assume no rewrite here.
                # If this is meant to cover *all* dashboard_api_upstream endpoints that are *not* explicitly listed above,
                # and if dashboard_api_upstream expects paths without /api/, then uncommenting and adjusting the rewrite is needed.
                # For now, keeping it simple as a direct proxy for any /api/ calls not caught by the specific regex.
                # rewrite ^/api/(.*) /$1 break; # <--- Only uncomment if dashboard-api expects path without /api/
                proxy_pass http://dashboard_api_upstream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Add status endpoints to check if services are accessible
            location = /api/status/policy-manager {
                proxy_pass http://policy_manager_upstream/health;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                add_header Content-Type application/json;
                add_header X-Debug-Info "Checking policy-manager health";
            }
            
            location = /api/status/dashboard-api {
                proxy_pass http://dashboard_api_upstream/health;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                add_header Content-Type application/json;
                add_header X-Debug-Info "Checking dashboard-api health";
            }
            
            location = /api/status/proxy-gateway {
                proxy_pass http://proxy_gateway_upstream/health;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                add_header Content-Type application/json;
                add_header X-Debug-Info "Checking proxy-gateway health";
            }

            # Fallback for policy-manager endpoints
            location @policy_manager_fallback {
                add_header Content-Type application/json;
                add_header X-Debug-Info "Policy manager service unavailable";
                return 503 '{"error": "Policy manager service temporarily unavailable", "status": 503}';
            }

            # 6. Static files
            location / {
                root /usr/share/nginx/html;
                index index.html index.htm;
                try_files $uri $uri/ /index.html;
            }

            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-dashboard
  labels:
    app: frontend-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-dashboard
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: frontend-dashboard
    spec:
      containers:
      - name: frontend-dashboard
        image:  us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        command: ["nginx", "-g", "daemon off;"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        volumeMounts:
        - name: nginx-config-volume
          mountPath: /etc/nginx/nginx.conf
          subPath: default.conf
      volumes:
      - name: nginx-config-volume
        configMap:
          name: frontend-nginx-config
          items:
          - key: default.conf
            path: default.conf
