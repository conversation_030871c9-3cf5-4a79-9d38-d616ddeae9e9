# Use the official Go image to build the application
FROM golang:1.24-alpine AS builder

# Set the current working directory inside the container
WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod ./
COPY go.sum ./

# Download all dependencies
RUN go mod download

# Copy the rest of the application source code
COPY . .

# Build the Go application
RUN go build -o /ai-optimizer .

# Use a minimal Alpine image for the final stage
FROM alpine:latest

# Install ca-certificates for HTTPS connections (e.g., to Prometheus)
RUN apk add --no-cache ca-certificates

# Set the current working directory inside the container
WORKDIR /root/

# Copy the built binary from the builder stage
COPY --from=builder /ai-optimizer .

# Command to run the executable
CMD ["./ai-optimizer"]
