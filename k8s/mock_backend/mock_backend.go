package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// MockAIRequest represents the expected structure of the incoming request body.
// We expect an OpenAI-like request here.
type MockAIRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	// Add other potential request fields here if needed from actual client requests
}

// OpenAICompletionResponse (simplified for mock purposes, but structurally compatible)
type OpenAICompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int               `json:"index"`
		Message      map[string]string `json:"message"`
		LogProbs     interface{}       `json:"logprobs"`
		FinishReason string            `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// handleAIRequest is the handler for incoming requests to the mock backend.
func handleAIRequest(w http.ResponseWriter, r *http.Request) {
	// Extract Request ID propagated by the proxy
	requestID := r.Header.Get("X-Request-ID")
	if requestID == "" {
		requestID = "N/A (No X-Request-ID header)"
	}

	backendID := os.Getenv("BACKEND_ID") // Get backend ID from environment variable
	if backendID == "" {
		backendID = "mock-backend-unknown"
	}

	log.Printf("[%s] [%s] Received request: %s %s from %s", backendID, requestID, r.Method, r.URL.String(), r.RemoteAddr)

	// Read and parse request body
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("[%s] [%s] Error reading request body: %v", backendID, requestID, err)
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close() // Ensure the request body is closed

	var aiRequest MockAIRequest
	err = json.Unmarshal(bodyBytes, &aiRequest)
	if err != nil {
		log.Printf("[%s] [%s] Error unmarshalling request body: %v. Body: %s", backendID, requestID, err, string(bodyBytes))
		http.Error(w, "Invalid request body format", http.StatusBadRequest)
		return
	}

	prompt := ""
	if len(aiRequest.Messages) > 0 {
		prompt = aiRequest.Messages[len(aiRequest.Messages)-1].Content // Get the last user message as prompt
	}
	if prompt == "" {
		prompt = "No identifiable prompt."
	}

	// Simulate processing time (latency) - This is the initial delay before work starts
	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 5 // Default delay REDUCED from 100ms
	}
	time.Sleep(time.Duration(delayMs) * time.Millisecond)
	log.Printf("[%s] [%s] Finished simulated initial delay of %d ms", backendID, requestID, delayMs)

	// --- Simulate CPU Usage for a Minimum Duration ---
	log.Printf("[%s] [%s] Simulating CPU usage for a minimum duration...", backendID, requestID)
	startTimeCPU := time.Now()
	minimumCPUDuration := 2 * time.Second

	i := 0
	for {
		_ = i * i / (i + 1)
		i++
		if time.Since(startTimeCPU) >= minimumCPUDuration {
			break
		}
	}
	durationCPU := time.Since(startTimeCPU)
	log.Printf("[%s] [%s] Finished simulating CPU usage after %s (minimum duration was %s)", backendID, requestID, durationCPU, minimumCPUDuration)
	// --- End Simulate CPU Usage ---

	// --- Simulate Memory Usage ---
	log.Printf("[%s] [%s] Simulating Memory usage...", backendID, requestID)
	memorySizeMB := 100
	tempMemory := make([]byte, memorySizeMB*1024*1024)
	memoryHoldDuration := 2 * time.Second
	time.Sleep(memoryHoldDuration)
	_ = tempMemory
	log.Printf("[%s] [%s] Finished simulating Memory usage after holding %d MB for %s", backendID, requestID, memorySizeMB, memoryHoldDuration)
	// --- End Simulate Memory Usage ---

	// --- Simulate AI Response and Token Counting ---
	simulatedResponseContent := fmt.Sprintf("Acknowledged your prompt: '%s'. This is a simulated response from %s. (ID: %s)", prompt, backendID, requestID)

	// Simulate token counting (very basic: count words as tokens)
	inputTokens := countWordsForTokens(prompt)
	outputTokens := countWordsForTokens(simulatedResponseContent)
	if inputTokens == 0 && outputTokens == 0 {
		outputTokens = 1 // Ensure at least 1 output token if no words, to simulate minimal cost
	}
	// --- End Simulation ---

	// Prepare and send OpenAI-compatible response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	openAIResponse := OpenAICompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", strings.ReplaceAll(uuid.New().String(), "-", "")), // Generate a unique ID
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   aiRequest.Model, // Reflect the model that was requested
		Choices: []struct {
			Index        int               `json:"index"`
			Message      map[string]string `json:"message"`
			LogProbs     interface{}       `json:"logprobs"`
			FinishReason string            `json:"finish_reason"`
		}{
			{
				Index: 0,
				Message: map[string]string{
					"role":    "assistant",
					"content": simulatedResponseContent,
				},
				LogProbs:     nil,
				FinishReason: "stop",
			},
		},
		Usage: struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		}{
			PromptTokens:     inputTokens,
			CompletionTokens: outputTokens,
			TotalTokens:      inputTokens + outputTokens,
		},
	}

	json.NewEncoder(w).Encode(openAIResponse)

	log.Printf("[%s] [%s] Sent OpenAI-compatible response. Input Tokens: %d, Output Tokens: %d", backendID, requestID, inputTokens, outputTokens)
}

// countWordsForTokens is a simple helper to simulate token counting by counting words.
// Ensures at least 1 token if content is not empty.
func countWordsForTokens(s string) int {
	if s == "" {
		return 0
	}
	words := strings.Fields(s)
	if len(words) == 0 && len(s) > 0 { // For very short strings that have no spaces
		return 1
	}
	return len(words)
}

// main function serves as the entry point for the mock backend application.
func main() {
	listenAddr := os.Getenv("LISTEN_ADDR")
	if listenAddr == "" {
		listenAddr = ":5001"
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = "mock-backend-unknown"
	}

	delayMsStr := os.Getenv("DELAY_MS")
	delayMs, err := strconv.Atoi(delayMsStr)
	if err != nil || delayMs < 0 {
		delayMs = 5
	}

	log.Printf("Starting Mock AI Backend '%s' on %s with simulated initial delay of %d ms", backendID, listenAddr, delayMs)

	http.HandleFunc("/v1/chat/completions", handleAIRequest) // Explicitly handle this path

	err = http.ListenAndServe(listenAddr, nil)
	if err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
