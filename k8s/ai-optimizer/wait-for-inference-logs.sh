#!/bin/bash
echo "Starting wait-for-inference-logs-table init container..."
CLICKHOUSE_POD=""
MAX_POD_RETRIES=20
POD_RETRY_COUNT=0

# First, find the ClickHouse pod
# Use '|| true' to prevent script from exiting if kubectl get fails initially
while [ -z "$CLICKHOUSE_POD" ] && [ "$POD_RETRY_COUNT" -lt "$MAX_POD_RETRIES" ]; do
  echo "Attempting to find ClickHouse pod (attempt $((POD_RETRY_COUNT + 1))/${MAX_POD_RETRIES})..."
  CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)
  if [ -z "$CLICKHOUSE_POD" ]; then
    sleep 10 # Wait longer if pod isn't found
  fi
  POD_RETRY_COUNT=$((POD_RETRY_COUNT+1))
done

if [ -z "$CLICKHOUSE_POD" ]; then
  echo "Error: ClickHouse pod not found after multiple retries. Exiting init container."
  exit 1 # Fail init container if ClickHouse pod is not found
fi
echo "Found ClickHouse pod: $CLICKHOUSE_POD"

# Wait for the kafka-topic-creator job to complete
MAX_JOB_RETRIES=60
JOB_RETRY_COUNT=0
until kubectl get job kafka-topic-creator -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}' 2>/dev/null | grep -q "True"; do
  echo "Waiting for kafka-topic-creator job to complete (attempt $((JOB_RETRY_COUNT + 1))/${MAX_JOB_RETRIES})..."
  sleep 5
  JOB_RETRY_COUNT=$((JOB_RETRY_COUNT+1))
  if [ "$JOB_RETRY_COUNT" -ge "$MAX_JOB_RETRIES" ]; then
    echo "Timeout: kafka-topic-creator job did not complete within the expected time."
    exit 1
  fi
done
echo "kafka-topic-creator job completed."

# Now, wait for the 'inference_logs' table inside the ClickHouse pod
MAX_TABLE_RETRIES=60 # Total wait time: 60 * 5s = 5 minutes
TABLE_RETRY_COUNT=0
# Use 'EXISTS TABLE' and check for '1' in output to confirm table existence
until kubectl exec "$CLICKHOUSE_POD" -- clickhouse-client -q "EXISTS TABLE inference_logs;" 2>/dev/null | grep -q "1"; do
  echo "Table 'inference_logs' not found in ClickHouse pod $CLICKHOUSE_POD (attempt $((TABLE_RETRY_COUNT + 1))/${MAX_TABLE_RETRIES}). Retrying in 5 seconds..."
  sleep 5
  TABLE_RETRY_COUNT=$((TABLE_RETRY_COUNT+1))
  if [ "$TABLE_RETRY_COUNT" -ge "$MAX_TABLE_RETRIES" ]; then
    echo "Timeout: ClickHouse table 'inference_logs' did not appear within the expected time."
    exit 1 # Fail init container if table doesn't appear
  fi
done

echo "ClickHouse table 'inference_logs' is ready for ai-optimizer."