#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Arguments passed from Cloud Build:
GCP_PROJECT_ID="$1"
GCP_REGION="$2"
ARTIFACT_REGISTRY_REPO="$3"
BUILD_ID="$4"
GKE_CLUSTER_NAME="$5"

# --- Removed local export of API keys here to avoid shell variable issues ---
# Instead, they will be directly embedded as literals in the YAML below.

echo "Using direct API Key placeholders embedded as literals in Kubernetes Job YAML."
echo "NOTE: For actual LLM access, REPLACE these placeholders with your real API keys."

# Construct the full image name
# THIS NAME MUST MATCH THE ONE USED IN cloudbuild.yaml FOR TAGGING!
REDIS_POPULATOR_IMAGE="${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/redis-populator-job:latest" # Using :latest

# Define a unique job name to avoid conflicts if previous jobs are still around
JOB_NAME="redis-populator-job-${BUILD_ID}"

echo "Creating Kubernetes Job to populate Redis..."
echo "Job Name: ${JOB_NAME}"
echo "Redis Populator Image: ${REDIS_POPULATOR_IMAGE}"


# Kubernetes Job YAML
cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: ${JOB_NAME}
  namespace: default # Or your target namespace
spec:
  template:
    spec:
      containers:
      - name: redis-populator
        image: ${REDIS_POPULATOR_IMAGE}
        command: ["/bin/bash", "/app/populate_redis_with_debug.sh"] # Use the shell script to run Python
        env:
        - name: REDIS_URL
          value: "redis://redis:6379" # Ensure this matches your Redis service name
        # Pass API Keys as ENVIRONMENT VARIABLE LITERALS directly to the container.
        # This completely bypasses any shell variable issues.
        # IMPORTANT: REPLACE THESE WITH YOUR ACTUAL API KEYS IF YOU WANT TO USE REAL LLMs!
        - name: OPENAI_API_KEY
          value: "********************************************************************************************************************************************************************" # This is a placeholder, REPLACE IT with your real key!
        - name: GOOGLE_API_KEY
          value: "AIzaSyBzG0Wdhtm44BPP4Htrt739oZyNBXFZ46I" # This is a placeholder, REPLACE IT with your real key!
        - name: ANTHROPIC_API_KEY
          value: "************************************************************************************************************" # This is a placeholder, REPLACE IT with your real key!
      restartPolicy: OnFailure
  backoffLimit: 3 # Retry up to 3 times on failure
EOF


echo "Waiting for Redis Populator Job ${JOB_NAME} to complete..."
# Monitor the job's completion. This might take a few seconds.
kubectl wait --for=condition=complete job/${JOB_NAME} --timeout=300s || {
    echo "Redis Populator Job ${JOB_NAME} failed or timed out. Checking logs..."
    kubectl logs job/${JOB_NAME}
    exit 1
}
echo "Redis Populator Job ${JOB_NAME} completed successfully."

# Comment out the cleanup lines TEMPORARILY for debugging
# echo "Cleaning up Redis Populator Job ${JOB_NAME}..."
# kubectl delete job/${JOB_NAME} --ignore-not-found=true --grace-period=0 --force
# echo "Redis Populator Job ${JOB_NAME} cleaned up."

