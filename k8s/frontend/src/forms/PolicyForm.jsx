import React, { useState, useEffect } from 'react';
import { PlusCircle, Trash2, Info as InfoIcon } from 'lucide-react'; // Importing necessary icons
import { Modal } from '../components/Modal'; // Import Modal from the new location

/**
 * @typedef {Object} PolicyFormProps
 * @property {Policy} policy - The policy object to edit (or new policy stub).
 * @property {(policy: Policy) => void} onSave - Function to call when the form is saved.
 * @property {() => void} onCancel - Function to call when the form is cancelled.
 * @property {Object.<string, string>} errors - Validation errors.
 */

/**
 * Policy Form component for creating and editing policies.
 * @param {PolicyFormProps} props
 */
const PolicyForm = ({ policy, onSave, onCancel, errors }) => {
  const [formData, setFormData] = useState({ ...policy, criteria: policy.criteria || [] });

  useEffect(() => {
    setFormData({ ...policy, criteria: policy.criteria || [] });
  }, [policy]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleCriteriaChange = (index, e) => {
    const { name, value } = e.target;
    // Ensure criteria is an array, fallback to empty array if null/undefined
    const newCriteria = [...(formData.criteria || [])];
    newCriteria[index] = { ...newCriteria[index], [name]: value };
    setFormData(prev => ({
      ...prev,
      criteria: newCriteria
    }));
  };

  const addCriteria = () => {
    setFormData(prev => ({
      ...prev,
      criteria: [...(prev.criteria || []), { field: '', operator: '=', value: '' }]
    }));
  };

  const removeCriteria = (index) => {
    setFormData(prev => ({
      ...prev,
      criteria: (prev.criteria || []).filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    try {
      // Ensure criteria is a valid JSON array or null/empty array
      const parsedCriteria = JSON.parse(JSON.stringify(formData.criteria || []));
      onSave({
        ...formData,
        criteria: parsedCriteria,
        priority: parseInt(formData.priority, 10) || 0, // Ensure priority is a number
        rules: formData.rules || '{}', // Ensure rules is a JSON string
      });
    } catch (error) {
      console.error("Failed to parse policy data:", error);
      // The `onSave` function (from usePolicyManagement) will handle showing notification if needed
    }
  };

  return (
    <Modal isOpen={true} onClose={onCancel} title={policy.id ? "Edit Policy" : "Create Policy"}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
          <input type="text" id="name" name="name" value={formData.name || ''} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
          <textarea id="description" name="description" value={formData.description || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"></textarea>
          {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
        </div>

        <div className="space-y-3 p-4 border border-gray-200 rounded-md">
          <h3 className="text-lg font-semibold text-gray-800">Criteria</h3>
          {(Array.isArray(formData.criteria) ? formData.criteria : []).map((criterion, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input type="text" name="field" placeholder="Field" value={criterion.field || ''} onChange={(e) => handleCriteriaChange(index, e)} className="flex-1 border border-gray-300 rounded-md shadow-sm p-2" />
              <select name="operator" value={criterion.operator || '='} onChange={(e) => handleCriteriaChange(index, e)} className="border border-gray-300 rounded-md shadow-sm p-2">
                <option value="=">=</option>
                <option value="!=">!=</option>
                <option value="&gt;">&gt;</option>
                <option value="&lt;">&lt;</option>
                <option value="&gt;=">&gt;=</option>
                <option value="&lt;=">&lt;=</option>
                <option value="CONTAINS">CONTAINS</option>
                <option value="STARTS_WITH">STARTS_WITH</option>
                <option value="ENDS_WITH">ENDS_WITH</option>
                <option value="IN">IN</option>
              </select>
              <input type="text" name="value" placeholder="Value" value={criterion.value || ''} onChange={(e) => handleCriteriaChange(index, e)} className="flex-1 border border-gray-300 rounded-md shadow-sm p-2" />
              <button type="button" onClick={() => removeCriteria(index)} className="p-2 text-red-500 hover:text-red-700">
                <Trash2 size={20} />
              </button>
            </div>
          ))}
          <button type="button" onClick={addCriteria} className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-all duration-200 flex items-center space-x-2">
            <PlusCircle size={18} /> <span>Add Criteria</span>
          </button>
        </div>

        <div>
          <label htmlFor="action" className="block text-sm font-medium text-gray-700">Action</label>
          <select id="action" name="action" value={formData.action || 'ROUTE'} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2">
            <option value="ROUTE">ROUTE</option>
            <option value="OPTIMIZE">OPTIMIZE</option>
            <option value="BLOCK">BLOCK</option>
            <option value="REWRITE">REWRITE</option>
          </select>
          {errors.action && <p className="text-red-500 text-xs mt-1">{errors.action}</p>}
        </div>
        {formData.action === 'ROUTE' || formData.action === 'OPTIMIZE' ? (
          <div>
            <label htmlFor="backend_id" className="block text-sm font-medium text-gray-700">LLM ID (for ROUTE/OPTIMIZE)</label>
            <input type="text" id="backend_id" name="backend_id" value={formData.backend_id || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
            {errors.backend_id && <p className="text-red-500 text-xs mt-1">{errors.backend_id}</p>}
          </div>
        ) : null}
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700">Priority (Higher is more important)</label>
          <input type="number" id="priority" name="priority" value={formData.priority || 0} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
          {errors.priority && <p className="text-red-500 text-xs mt-1">{errors.priority}</p>}
        </div>
        <div>
          <label htmlFor="rules" className="block text-sm font-medium text-gray-700">Rules (JSON String)</label>
          <textarea id="rules" name="rules" value={formData.rules || '{}'} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 font-mono text-xs"></textarea>
          {errors.rules && <p className="text-red-500 text-xs mt-1">{errors.rules}</p>}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button type="button" onClick={onCancel} className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-all duration-200">
            Cancel
          </button>
          <button type="submit" className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-all duration-200">
            {policy.id ? "Update Policy" : "Create Policy"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default PolicyForm;
