# Autonomous Planning Engine - Example Use Cases

This document provides practical examples of how to use the Autonomous Task Decomposition & Planning Engine for common agentic workflows.

## Example 1: Automated Customer Service Workflow

### Goal
"Analyze customer support tickets from the last week, identify common issues, and generate a comprehensive report with recommendations for improving customer satisfaction."

### API Request
```bash
curl -X POST http://planning-service:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: customer-service-team" \
  -d '{
    "description": "Analyze customer support tickets from the last week, identify common issues, and generate a comprehensive report with recommendations for improving customer satisfaction.",
    "success_criteria": [
      {
        "description": "Report includes analysis of at least 100 tickets",
        "metric": "ticket_count",
        "target": 100,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      },
      {
        "description": "Identify top 5 most common issues",
        "metric": "issue_categories",
        "target": 5,
        "operator": ">=",
        "weight": 0.9,
        "required": true
      },
      {
        "description": "Provide actionable recommendations",
        "metric": "recommendations_count",
        "target": 3,
        "operator": ">=",
        "weight": 0.7,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete analysis within 2 hours",
        "limit": "2h",
        "operator": "<=",
        "severity": "hard"
      },
      {
        "type": "cost",
        "description": "Keep analysis cost under $10",
        "limit": 10.0,
        "operator": "<=",
        "severity": "soft"
      }
    ],
    "context": {
      "data_source": "support_ticket_database",
      "time_range": "last_7_days",
      "department": "customer_service"
    },
    "priority": 7
  }'
```

### Expected Task Decomposition
The planning engine would break this down into tasks like:
1. **Data Query**: Retrieve support tickets from the last 7 days
2. **Data Analysis**: Categorize tickets by issue type
3. **Statistical Analysis**: Calculate frequency and trends
4. **LLM Analysis**: Generate insights and identify patterns
5. **Report Generation**: Create comprehensive report with recommendations
6. **Validation**: Verify report meets success criteria

## Example 2: Data Analysis Pipeline

### Goal
"Perform a comprehensive analysis of our e-commerce sales data to identify trends, predict next quarter's performance, and recommend optimization strategies."

### API Request
```bash
curl -X POST http://planning-service:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: analytics-team" \
  -d '{
    "description": "Perform a comprehensive analysis of our e-commerce sales data to identify trends, predict next quarter performance, and recommend optimization strategies.",
    "success_criteria": [
      {
        "description": "Analysis covers at least 6 months of historical data",
        "metric": "data_coverage_months",
        "target": 6,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      },
      {
        "description": "Prediction accuracy above 80%",
        "metric": "prediction_accuracy",
        "target": 0.8,
        "operator": ">=",
        "weight": 0.9,
        "required": true
      },
      {
        "description": "Provide at least 5 optimization recommendations",
        "metric": "optimization_recommendations",
        "target": 5,
        "operator": ">=",
        "weight": 0.7,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete analysis within 4 hours",
        "limit": "4h",
        "operator": "<=",
        "severity": "hard"
      },
      {
        "type": "quality",
        "description": "Use statistical significance testing",
        "limit": "p_value < 0.05",
        "operator": "meets",
        "severity": "hard"
      }
    ],
    "context": {
      "data_sources": ["sales_db", "customer_db", "product_db"],
      "analysis_period": "last_6_months",
      "prediction_horizon": "next_quarter"
    },
    "priority": 8
  }'
```

### Expected Task Decomposition
1. **Data Extraction**: Pull sales, customer, and product data
2. **Data Cleaning**: Handle missing values and outliers
3. **Exploratory Analysis**: Generate descriptive statistics
4. **Trend Analysis**: Identify seasonal patterns and trends
5. **Predictive Modeling**: Build forecasting models
6. **Model Validation**: Test prediction accuracy
7. **Insight Generation**: Use LLM to interpret results
8. **Recommendation Engine**: Generate optimization strategies
9. **Report Compilation**: Create executive summary

## Example 3: Content Creation Workflow

### Goal
"Create a comprehensive marketing campaign for our new product launch, including blog posts, social media content, email templates, and press releases."

### API Request
```bash
curl -X POST http://planning-service:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: marketing-team" \
  -d '{
    "description": "Create a comprehensive marketing campaign for our new product launch, including blog posts, social media content, email templates, and press releases.",
    "success_criteria": [
      {
        "description": "Generate 5 blog posts of 1000+ words each",
        "metric": "blog_posts_count",
        "target": 5,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      },
      {
        "description": "Create 20 social media posts",
        "metric": "social_posts_count",
        "target": 20,
        "operator": ">=",
        "weight": 0.7,
        "required": true
      },
      {
        "description": "Develop 3 email templates",
        "metric": "email_templates_count",
        "target": 3,
        "operator": ">=",
        "weight": 0.6,
        "required": true
      },
      {
        "description": "Write 2 press releases",
        "metric": "press_releases_count",
        "target": 2,
        "operator": ">=",
        "weight": 0.9,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete campaign within 3 days",
        "limit": "72h",
        "operator": "<=",
        "severity": "hard"
      },
      {
        "type": "quality",
        "description": "All content must be brand-compliant",
        "limit": "brand_compliance_score >= 0.9",
        "operator": "meets",
        "severity": "hard"
      }
    ],
    "context": {
      "product_name": "AI Assistant Pro",
      "target_audience": "business professionals",
      "brand_guidelines": "professional, innovative, trustworthy",
      "launch_date": "2024-02-15"
    },
    "priority": 9
  }'
```

### Expected Task Decomposition
1. **Research Phase**: Analyze target audience and competitors
2. **Content Strategy**: Define messaging and content pillars
3. **Blog Content Creation**: Generate 5 detailed blog posts
4. **Social Media Content**: Create platform-specific posts
5. **Email Campaign**: Develop email templates and sequences
6. **Press Release Writing**: Create media-ready announcements
7. **Brand Compliance Check**: Validate all content against guidelines
8. **Content Optimization**: Refine based on SEO and engagement metrics
9. **Campaign Package**: Compile all materials with usage guidelines

## Example 4: Automated Research and Analysis

### Goal
"Research the competitive landscape for AI-powered customer service tools, analyze their features and pricing, and provide strategic recommendations for our product positioning."

### API Request
```bash
curl -X POST http://planning-service:8080/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: product-strategy-team" \
  -d '{
    "description": "Research the competitive landscape for AI-powered customer service tools, analyze their features and pricing, and provide strategic recommendations for our product positioning.",
    "success_criteria": [
      {
        "description": "Analyze at least 10 competitors",
        "metric": "competitors_analyzed",
        "target": 10,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      },
      {
        "description": "Create feature comparison matrix",
        "metric": "feature_matrix_completeness",
        "target": 1.0,
        "operator": "==",
        "weight": 0.9,
        "required": true
      },
      {
        "description": "Provide 5 strategic recommendations",
        "metric": "strategic_recommendations",
        "target": 5,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete research within 6 hours",
        "limit": "6h",
        "operator": "<=",
        "severity": "hard"
      },
      {
        "type": "quality",
        "description": "Use only verified and recent data",
        "limit": "data_freshness <= 30_days",
        "operator": "meets",
        "severity": "hard"
      }
    ],
    "context": {
      "industry": "AI customer service",
      "focus_areas": ["features", "pricing", "market_position"],
      "our_product": "AI Cost-Performance Optimizer"
    },
    "priority": 6
  }'
```

## Monitoring and Progress Tracking

### Check Goal Status
```bash
curl -X GET http://planning-service:8080/v1/goals/{goal_id}/status \
  -H "X-User-ID: user-id"
```

### Get Execution Plan
```bash
curl -X GET http://planning-service:8080/v1/goals/{goal_id}/plan \
  -H "X-User-ID: user-id"
```

### View Task Progress
```bash
curl -X GET http://planning-service:8080/v1/goals/{goal_id}/tasks \
  -H "X-User-ID: user-id"
```

### Get Final Results
```bash
curl -X GET http://planning-service:8080/v1/goals/{goal_id}/results \
  -H "X-User-ID: user-id"
```

## Integration with Existing AI Optimizer

The planning engine seamlessly integrates with the existing AI Cost-Performance Optimizer:

1. **LLM Routing**: All LLM calls are routed through the AI Optimizer for cost optimization
2. **Performance Monitoring**: Task execution metrics are logged to the existing analytics pipeline
3. **Policy Compliance**: All operations respect existing governance policies
4. **Resource Management**: The optimizer ensures efficient resource utilization across the entire workflow

## Advanced Features

### Adaptive Re-planning
If a task fails or conditions change, the system can automatically re-plan:
```bash
curl -X POST http://planning-service:8080/v1/goals/{goal_id}/replan \
  -H "X-User-ID: user-id"
```

### Custom Task Templates
Organizations can define custom task templates for their specific workflows, enabling faster and more consistent planning for recurring goal types.

### Multi-Goal Orchestration
The system can handle multiple concurrent goals, automatically managing resource allocation and dependencies between different workflows.
