/* src/App.css */
/* Basic styling for the dashboard */

body {
  font-family: 'Inter', sans-serif; /* Use Inter font */
  background-color: #f0f2f5; /* Light gray background */
  margin: 0; /* Remove default body margin */
  padding: 0; /* Remove default body padding */
  color: #333; /* Set a default dark text color for the body */
}

.container {
  max-width: 1200px;
  /* Added background color and padding to the main content container */
  background-color: #ffffff; /* Explicitly set background to white */
  padding: 24px; /* Increased padding */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Add a more prominent shadow */
  border-radius: 8px; /* Keep rounded corners */
  margin-top: 24px; /* Add some space above the container */
  margin-bottom: 24px; /* Add some space below the container */
  color: #333; /* Ensure container text is also dark */
}

h1, h2, h3 {
    color: #1a202c; /* Darker heading color */
}

table {
  border-collapse: collapse;
  width: 100%; /* Ensure table takes full width of its container */
  color: #333; /* Ensure table text is dark */
}

th, td {
  padding: 12px 15px;
  text-align: left;
  color: #333; /* Ensure table cell text is dark */
}

thead tr {
    background-color: #e2e8f0; /* Use a light blue-gray for header background */
    color: #4a5568; /* Darker text for header */
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
}


tbody tr:nth-child(even) {
  background-color: #f7fafc; /* Very light gray for even rows */
}

tbody tr:hover {
  background-color: #edf2f7; /* Slightly darker gray on hover */
}

/* Ensure rounded corners on table */
/* Apply rounded corners to the container that wraps the table */
.rounded-lg {
  border-radius: 0.5rem;
}

/* Add some basic responsive adjustments if needed */
@media (max-width: 768px) {
  .overflow-x-auto {
    overflow-x: auto;
  }
  table {
    min-width: 600px; /* Ensure table doesn't shrink too much on small screens */
  }
  .container {
      padding: 16px; /* Adjust padding on smaller screens */
  }
}

/* Styling for filter controls */
select {
    /* Ensure select has a background color and dark text */
    background-color: #ffffff;
    border: 1px solid #cbd5e0; /* Add a light border */
    color: #333; /* Ensure select text is dark */
}

select option {
     color: #333; /* Ensure option text is dark */
}


select:focus {
    border-color: #4c51bf; /* Highlight color on focus */
    box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.2); /* Add focus ring */
}

/* Styling for labels */
label {
    color: #333; /* Ensure label text is dark */
}

/* Additional styling for charts section */
.grid.gap-6 > div {
    background-color: #ffffff; /* White background for chart cards */
    padding: 16px; /* Padding inside chart cards */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); /* Subtle shadow for chart cards */
    border-radius: 8px; /* Rounded corners for chart cards */
}


