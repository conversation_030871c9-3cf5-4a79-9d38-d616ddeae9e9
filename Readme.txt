
#Project Architecture Overview: AI Cost-Performance Optimizer
This system is designed to intelligently route AI inference requests to the most optimal backend (e.g., GPU models) based on dynamic policies, real-time performance metrics, and cost considerations. It also provides comprehensive logging and analytics for monitoring and optimization.

1. System Architecture Diagram
+-----------------------+
|                       |
|        User/Client    |
|                       |
+-----------+-----------+
            |
            | 1. HTTP(S) Inference Request (e.g., /predict)
            |    (Includes X-Data-Sensitivity header)
            V
+-----------+-----------+
|                       |
|     Proxy Gateway     |
|   (Go Lang Service)   |
+-----------+-----------+
    |       |       |
    |       |       | 2. HTTP(S) Proxy Request (to Backend)
    |       |       +-------------------------------------------------+
    |       |                                                         |
    |       | 3. Redis GET/SET (Model Profiles, Policies, Live Latency)
    |       | 4. Redis Pub/Sub (Policy/Profile Updates)               |
    |       |                                                         |
    |       | 5. <PERSON><PERSON><PERSON> (Inference Logs)                      |
    |       |                                                         |
    |       | 6. HTTP(S) Proxy Request (to Dashboard API)             |
    |       |                                                         |
    V       V                                                         V
+---+-------+---+       +-----------------------+       +-----------------------+
|               |       |                       |       |                       |
|    Redis      |<----->|    Policy Manager     |       |   Mock Backend GPU 1  |
| (Cache, Metrics,      | (Go Lang Service)     |       | (Simulated AI Service)|
|   Pub/Sub)    |       +-----------+-----------+       +-----------+-----------+
+---------------+                   |                               ^
                                    | 7. Firestore API (Policies, Model Profiles)
                                    V                               |
                            +-----------------+                     |
                            |                 |                     |
                            |    Firestore    |                     |
                            | (Source of Truth)|                     |
                            +-----------------+                     |
                                                                    | 8. HTTP(S) Inference Response
+-----------------------+                                           |
|                       |                                           |
|     Kafka             |                                           |
| (Message Broker)      |                                           |
+-----------+-----------+                                           |
            ^                                                       |
            | 9. Kafka Consumer (Inference Logs)                    |
            |                                                       |
+-----------+-----------+                                           |
|                       |                                           |
|    Data Processor     |                                           |
| (Go Lang Service)     |                                           |
+-----------+-----------+                                           |
    |       |       |                                               |
    |       |       | 10. Prometheus HTTP API (CPU/Memory Metrics)  |
    |       |       +-------------------------------------------------+
    |       |                                                         |
    |       | 11. ClickHouse TCP/HTTP (Processed Logs)                |
    |       |                                                         |
    V       V                                                         V
+-----------+-----------+       +-----------------------+       +-----------------------+
|                       |       |                       |       |                       |
|    ClickHouse         |       |      Prometheus       |       |   Mock Backend GPU 2  |
| (Analytics Database)  |       | (Metrics Scraper)     |       | (Simulated AI Service)|
+-----------------------+       +-----------+-----------+       +-----------+-----------+
                                            ^
                                            | 12. Scrapes Metrics (e.g., cAdvisor)
                                            |
                                    +-----------------------+
                                    |                       |
                                    |     AI Optimizer      |
                                    |   (Go Lang Service)   |
                                    | (Internal Analytics)  |
                                    +-----------+-----------+
                                        ^   ^
                                        |   | (Reads metrics for internal optimization)
                                        |   |
                                +-------+---+-------+
                                |                       |
                                |    Dashboard API      |
                                |  (Go Lang Service)    |
                                +-----------+-----------+
                                        ^   ^
                                        |   | (Queries data for dashboard)
                                        |   |
                                +-------+---+-------+
                                |                       |
                                |       React App       |
                                |     (Frontend UI)     |
                                | (via API Gateway/Nginx)|
                                +-----------------------+

2. Component Descriptions and Functionality
a. User/Client
Functionality: Initiates AI inference requests (e.g., text generation, image analysis).

Communication: Sends HTTP(S) POST requests to the Proxy Gateway's /predict endpoint. May include headers like X-Data-Sensitivity.

b. Proxy Gateway (Go Lang Service)
Functionality: The central routing component.

Receives all incoming inference requests.

Backend Selection: Uses an intelligent algorithm to select the most suitable Mock Backend GPU based on:

Request attributes (e.g., X-Data-Sensitivity header).

Dynamically loaded Policies (e.g., "confidential data must go to confidential backends").

Model Profiles (expected cost, latency, capabilities of each backend's model).

Real-time Live Latency of backends from Redis.

Proxies the request to the selected backend.

Captures detailed inference logs (request/response, latency, selected backend, cost).

Proxies requests for the Dashboard API. This is a key clarification based on your original description.

Communication:

Receives: HTTP(S) requests from User/Client.

Sends: HTTP(S) requests to Mock Backend GPU 1/2.

Sends: HTTP(S) requests to the Dashboard API (for dashboard data requests originating from the React App, proxied through Nginx).

Communicates with Redis:

GET operations to fetch cached Model Profiles and Policies on startup.

GET operations to fetch Live Latency metrics.

SUBSCRIBE to Redis Pub/Sub channels (policy_updates, model_profile_updates) for real-time cache invalidation/updates from Policy Manager.

Communicates with Kafka: Acts as a Kafka Producer, sending InferenceLog messages to the inference-logs topic.

c. Mock Backend GPU 1 & 2 (Simulated AI Services)
Functionality: Simulate actual AI model inference endpoints. They receive requests and return a simulated response with a controlled latency.

Communication:

Receives: HTTP(S) requests from Proxy Gateway.

Sends: HTTP(S) responses back to Proxy Gateway.

Monitored by: Prometheus (via cAdvisor metrics).

d. Policy Manager (Go Lang Service)
Functionality: Manages the "source of truth" for dynamic routing policies and model profiles.

Watches Firestore for any changes (additions, modifications, deletions) to policies and model profiles.

Propagates these changes to Redis by setting/deleting individual keys.

Publishes these changes to Redis Pub/Sub channels for real-time updates.

Exposes a basic HTTP API for manual CRUD operations (though typically automated via Firestore).

Communication:

Communicates with Firestore: Uses the Firestore API to set up snapshot listeners for policies and model_profiles collections.

Communicates with Redis:

SET operations to store Policy and ModelProfile objects as individual keys (e.g., policy:<ID>, model_profile:<ID>).

DEL operations to remove keys when documents are deleted in Firestore.

PUBLISH operations to policy_updates and model_profile_updates Pub/Sub channels.

e. Firestore (Google Cloud Service)
Functionality: A NoSQL cloud database that serves as the authoritative source of truth for Policies and Model Profiles.

Communication:

Receives: API calls from Policy Manager (for snapshot listeners, and potentially CRUD operations).

f. Redis (In-Memory Data Store)
Functionality: Serves multiple critical roles due to its speed:

Cache: Stores Policies and Model Profiles (populated by Policy Manager) to provide low-latency access for the Proxy Gateway's routing decisions.

Real-time Metrics Store: Stores Live Latency data for each backend (updated by Data Processor).

Pub/Sub Broker: Facilitates real-time communication between Policy Manager (publisher) and Proxy Gateway (subscriber) for cache synchronization.

Communication:

Receives: SET/DEL/PUBLISH commands from Policy Manager and Data Processor.

Sends: GET responses and Pub/Sub messages to Proxy Gateway, Data Processor, AI Optimizer, and Dashboard API.

g. Kafka (Message Broker)
Functionality: Provides a highly scalable and fault-tolerant message queue for buffering Inference Logs. This decouples the Proxy Gateway from the Data Processor, ensuring logs are not lost even if the Data Processor is temporarily unavailable.

Communication:

Receives: Messages from Proxy Gateway (Producer).

Sends: Messages to Data Processor (Consumer).

h. Data Processor (Go Lang Service)
Functionality: Processes raw inference logs from Kafka, enriches them with performance metrics, and stores them for analysis.

Consumes Inference Logs from Kafka.

Queries Prometheus to fetch real-time CPU and Memory usage for the Selected Backend ID during the inference duration.

Calculates the total cost of the inference (base cost + resource cost).

Updates the Live Latency for the selected backend in Redis.

Inserts the enriched, processed log data into ClickHouse.

Manages its Kafka consumer group offsets to ensure correct message processing on restarts.

Communication:

Receives: Messages from Kafka (Consumer).

Sends: HTTP GET requests to Prometheus API.

Communicates with Redis: SET operations to update Live Latency keys.

Communicates with ClickHouse: TCP/HTTP connections to insert processed logs.

i. ClickHouse (Analytics Database)
Functionality: A high-performance, columnar analytical database optimized for fast queries on large datasets. Stores all processed inference logs for historical analysis, reporting, and dashboarding.

Communication:

Receives: TCP/HTTP connections for data inserts from Data Processor.

Sends: Query results to AI Optimizer and Dashboard API.

j. Prometheus (Monitoring System)
Functionality: A time-series database and monitoring system. It continuously scrapes metrics from configured targets (e.g., Kubernetes pods running Mock Backend GPU services via cAdvisor metrics).

Communication:

Scrapes: HTTP endpoints of Mock Backend GPU 1/2 (and other Kubernetes components) to collect metrics.

Serves: HTTP API queries from Data Processor.

k. AI Optimizer (Go Lang Service) - Dedicated Internal Component
Functionality: This component is an internal, asynchronous analytics and recommendation engine. Its primary purpose is to:

Periodically run an optimization cycle (as demonstrated in your main.go).

Fetch historical inference logs from ClickHouse (fetchHistoricalInferenceLogs).

Analyze backend performance metrics (analyzeBackendPerformance).

Fetch real-time latency data from Redis (fetchLiveLatencies).

Make optimization decisions and generate recommendations (e.g., for scaling, traffic shifting, or alerting) based on these metrics.

Crucially, it does NOT expose any HTTP API endpoints for the frontend. Its output is primarily internal (e.g., logs, alerts, or triggers to other services for scaling/routing changes). It operates independently of direct UI requests.

Communication:

Queries ClickHouse: Fetches historical data for its internal analysis.

Queries Redis: Fetches live data for its internal analysis.

l. Dashboard API (Go Lang Service) - Dedicated Frontend-Facing Component
Functionality: This dedicated component serves as the backend for the React App's dashboard.

It exposes HTTP API endpoints (e.g., /api/inference_summary, /api/time_series_summary) that the React App consumes.

It queries ClickHouse to retrieve historical inference data and aggregates it for the dashboard's summary and time-series views.

It may also query Redis for any real-time metrics needed by the dashboard that aren't primarily historical.

Communication:

Receives: HTTP requests from the Proxy Gateway (which are ultimately from the React App via Nginx) for dashboard data.

Queries ClickHouse: To get historical data for summaries and time-series.

Queries Redis: To get relevant live metrics for the dashboard.

m. React App (Frontend UI)
Functionality: The user interface for the entire platform. It allows users to:

View aggregated inference summaries.

Visualize inference trends over time using various metrics (requests, latency, tokens, cost).

Apply filters (date range, backend ID, time interval) to the dashboard data.

Manage routing policies (create, edit, delete) via the Policy Manager's API (accessed through the API Gateway).

Manage model profiles (create, edit, delete) via the Policy Manager's API (accessed through the API Gateway).

Communication: Makes API calls to the API Gateway, which then routes them to the Proxy Gateway (which then routes to the Dashboard API for dashboard data) and the Policy Manager (for admin panel data).

n. Kafka Topic Creator (Utility)
Functionality: A one-off utility to ensure the necessary Kafka topics (e.g., inference-logs) are created before the main services start consuming/producing. It might also send initial test messages.

Communication:

Communicates with Kafka: Sends commands to create topics and produce test messages.




How the model routing works:

The proxy-gateway determines whether to route requests to your mock backends or real external models (like OpenAI, Google, Anthropic) primarily based on the BackendURL defined within the ModelProfile that gets selected for a given request.

Here's a breakdown of the routing logic in proxy-gateway/main.go and how this distinction is made:

The selectBackend Function: The Brain of Routing
The selectBackend function is where the core routing decision is made. It follows a specific hierarchy:

Policy Evaluation (Highest Priority):

The proxy-gateway first iterates through all configured policies, sorted by their Priority (higher priority evaluated first).
It checks if any ROUTE policy's Criteria match the incoming request (e.g., model_requested, client_ip, data_sensitivity, prompt_length).
If a ROUTE policy matches, the policy.BackendID is used to look up a corresponding ModelProfile from its in-memory cache.
Fallback 1: Requested Model Name or Alias:

If no explicit ROUTE policy matches, the proxy then attempts to find a ModelProfile whose Name or Aliases directly match the model specified in the incoming LLM request's body (e.g., "gpt-4o-mini", "gemini-flash").
Fallback 2: AI Optimizer's Optimal Backend:

If neither policies nor a direct model name/alias match, the proxy-gateway checks Redis for the REDIS_OPTIMAL_BACKEND_KEY ("routing:optimal_backend_id"). The ai-optimizer service is responsible for publishing the ID of the currently "optimal" backend to this key.
If an optimal backend ID is found, the proxy retrieves its ModelProfile.
Fallback 3: First Available Model Profile:

As a last resort, if none of the above criteria yield a backend, the proxy-gateway will simply pick the first ModelProfile it finds in its cache. This serves as a safety net to ensure requests are always routed if possible.
How BackendURL Dictates the Target
Once a ModelProfile is selected by any of the above mechanisms, the SendRequestToBackend function is called. This function uses the BackendURL field from the chosen ModelProfile to construct the actual HTTP request that is sent to the LLM.

Routing to Mock Backends:
Your mock backends (e.g., mock-backend-gpu1, mock-google, mock-anthropic, mock-openai) are typically deployed within your Kubernetes cluster. Their ModelProfiles will have BackendURLs pointing to their internal Kubernetes service names and ports.

Example: http://mock-backend-gpu1:5001/predict or http://mock-openai:5003/v1/chat/completions
Routing to Real External Models:
The ModelProfiles for real OpenAI, Google, or Anthropic models will have BackendURLs pointing to their public internet endpoints.

Example: https://api.openai.com/v1/chat/completions
Example: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent
Example: https://api.anthropic.com/v1/messages
In Summary:
The routing decision is a cascade:

The proxy-gateway figures out which ModelProfile to use based on policies, requested model name, or the AI optimizer's recommendation.
Once a ModelProfile is chosen, the proxy-gateway simply uses the BackendURL specified within that ModelProfile to send the request.
So, if a ModelProfile's BackendURL is set to an internal Kubernetes service address, the request goes to your mock. If it's set to an external API endpoint, it goes to the real provider. The choice between mock and real is entirely driven by how you define and manage the BackendURLs in your ModelProfiles (which are stored in Redis).






Policy Form Fields Explained
This section details the fields used when defining or editing a routing policy.

Name

Purpose: A human-readable identifier for the policy. This should be concise and descriptive.

Example: "High-Priority GPT-4 Requests," "Cost-Optimized Llama-2 Routing," "Block Malicious IPs."

Description

Purpose: Provides more detailed information about the policy's intent, the specific scenarios it covers, or any caveats. This helps other team members understand its function without diving into the code.

Example: "Routes all production GPT-4 large model requests to dedicated GPU backends to minimize latency," or "Denies requests from known problematic IP ranges identified by security."

Action

Purpose: Defines what the router should do when an incoming inference request matches the policy's Criteria. This is the core outcome of the policy.

Possible Values:

ROUTE_TO_BACKEND: Send the request to the Backend ID specified in this policy.

REJECT: Block the request entirely (e.g., return a 403 Forbidden or 429 Too Many Requests).

OPTIMIZE_COST: Dynamically select the cheapest available backend from a pool of Model Profiles that meet the request's needs.

OPTIMIZE_LATENCY: Dynamically select the fastest available backend.

LOG_ONLY: Process the request as usual but specifically log its details for analysis without altering routing.

Example: "ROUTE_TO_BACKEND," "REJECT," "OPTIMIZE_COST."

Backend ID

Purpose: If the Action is set to ROUTE_TO_BACKEND, this field specifies the exact ID of the target model backend where the inference request should be sent. This ID would typically correspond to the id of one of your Model Profiles.

Example: "mock-backend-gpu1," "azure-openai-us-east," "on-prem-cpu-cluster."

Priority

Purpose: Determines the order in which policies are evaluated. When multiple policies could potentially match an incoming request, the policy with the higher priority (typically a lower number, like 1 being highest priority) is applied first.

Example: 1 (highest), 10, 100 (lowest). You'd want a "block malicious IPs" policy to have a higher priority than a general routing policy.

Rules (JSON String)

Purpose: This field is for more complex or custom routing logic that might not be easily expressed with simple key-value Criteria. By storing it as a JSON string, your backend can parse and interpret it for advanced scenarios.

Example: You might store a complex conditional expression, a weighted routing configuration, or parameters for A/B testing different backends.

Example Value: {"min_batch_size": 2, "max_concurrency": 5} or {"custom_function": "evaluate_user_tier"}.

Criteria (Repeating Group: Field, Operator, Value)

Purpose: These define the conditions that an incoming inference request must meet for the policy to be applied. A policy can have multiple criteria, which are usually evaluated with an "AND" logic (all criteria must be true).

Field: The name of the attribute or property of the incoming request you want to evaluate.

Example: "user_id," "model_name," "input_token_count," "origin_ip."

Operator: How you want to compare the Field to the Value.

Example: "=", "!=", ">", "<", "CONTAINS", "STARTS_WITH."

Value: The specific value you are comparing the Field against.

Example: "premium-user," "gpt-4-turbo," "1000," "***********."

Remove (Button for Criteria)

Purpose: Allows the user to remove a specific criterion from the policy.

This architecture provides a robust, scalable, and observable system for dynamic AI inference routing and performance analytics.
