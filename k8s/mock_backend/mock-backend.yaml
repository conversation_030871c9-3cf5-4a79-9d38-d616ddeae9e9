# mock-backend.yaml
# Kubernetes YAML definitions for Mock AI Backend services

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-backend-gpu1
  labels:
    app: mock-backend
    backend-id: mock-backend-gpu1 # Specific label for this backend instance
spec:
  replicas: 1 # Running a single replica for this backend
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-backend-gpu1
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-backend-gpu1
        com.docker.compose.service: mock-backend-gpu1 # ADDED: Crucial label for data-processor
    spec:
      containers:
      - name: mock-backend-gpu1 # CHANGED: Container name now matches backend-id
        image: ai-cost-performance-optimizer-mock-backend:latest # Use the actual image name built by your script
        imagePullPolicy: Always # Use IfNotPresent for local development
        ports:
        - containerPort: 5001 # The port the mock backend listens on
        env:
        - name: LISTEN_ADDR
          value: ":5001" # Configure the backend to listen on its container port
        - name: BACKEND_ID
          value: "mock-backend-gpu1" # Pass the backend ID
        - name: DELAY_MS
          value: "100" # Configure the delay
        command: ["/app/mock_backend"] # Explicitly set the command to the binary path
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
---
apiVersion: v1
kind: Service
metadata:
  name: mock-backend-gpu1 # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-backend-gpu1 # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5001 # Service port
    targetPort: 5001 # Container port
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-backend-gpu2
  labels:
    app: mock-backend
    backend-id: mock-backend-gpu2 # Specific label for this backend instance
spec:
  replicas: 1 # Running a single replica for this backend
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-backend-gpu2
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-backend-gpu2
        com.docker.compose.service: mock-backend-gpu2 # ADDED: Crucial label for data-processor
    spec:
      containers:
      - name: mock-backend-gpu2 # CHANGED: Container name now matches backend-id
        image: ai-cost-performance-optimizer-mock-backend:latest # Use the actual image name built by your script
        imagePullPolicy: IfNotPresent # Use IfNotPresent for local development
        ports:
        - containerPort: 5002 # The port this instance listens on
        env:
        - name: LISTEN_ADDR
          value: ":5002" # Configure the backend to listen on its container port
        - name: BACKEND_ID
          value: "mock-backend-gpu2" # Pass the backend ID
        - name: DELAY_MS
          value: "150" # Configure the delay
        command: ["/app/mock_backend"] # Explicitly set the command to the binary path
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
---
apiVersion: v1
kind: Service
metadata:
  name: mock-backend-gpu2 # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-backend-gpu2 # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5002 # Service port
    targetPort: 5002 # Container port

