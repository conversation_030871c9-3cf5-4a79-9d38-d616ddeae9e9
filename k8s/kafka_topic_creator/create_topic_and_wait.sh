#!/bin/sh

set -e # Exit immediately if a command exits with a non-zero status
set -x # Enable shell debugging: print commands and their arguments as they are executed

# Add a small initial sleep to allow <PERSON><PERSON><PERSON> to fully settle before the script attempts client operations.
# This can help mitigate subtle timing issues where <PERSON><PERSON><PERSON> might be "reachable" but not fully ready for all commands.
echo "Initial sleep for 10 seconds to allow <PERSON><PERSON><PERSON> to fully stabilize..."
sleep 10
echo "Initial sleep finished."

# Define Kafka broker and topic details from environment variables
# These should be passed by the Kubernetes Job definition
KAFKA_BROKER="${KAFKA_BROKERS}" # KAFKA_BROKERS is set in the K8s Job
TOPIC_NAME="${TOPIC_NAME}"       # TOPIC_NAME is set in the K8s Job
PARTITIONS=1
REPLICATION_FACTOR=1
TIMEOUT_MS=60000 # 60 seconds timeout for topic creation

echo "Starting Kafka topic creation script."
echo "Kafka Broker: ${KAFKA_BROKER}"
echo "Topic Name: ${TOPIC_NAME}"
echo "Partitions: ${PARTITIONS}"
echo "Replication Factor: ${REPLICATION_FACTOR}"

# Check if topic already exists
echo "Checking if topic '${TOPIC_NAME}' already exists..."
echo "Attempting to list Kafka topics..."
RAW_TOPICS_LIST=$(/usr/bin/kafka-topics --bootstrap-server "${KAFKA_BROKER}" --list 2>&1)
echo "Raw Kafka topics list output:"
echo "${RAW_TOPICS_LIST}"
echo "--- End Raw Kafka topics list output ---"

# Fix: Add '|| true' to grep to prevent set -e from exiting if topic is not found
TOPIC_EXISTS=$(echo "${RAW_TOPICS_LIST}" | grep -w "${TOPIC_NAME}" || true)

if [ -n "${TOPIC_EXISTS}" ]; then
  echo "Topic '${TOPIC_NAME}' already exists. Skipping creation."
else
  echo "Topic '${TOPIC_NAME}' does not exist. Attempting to create it..."
  echo "DEBUG: About to attempt topic creation and capture output (set +e follows)." # ADDED THIS EXPLICIT DEBUG LINE
  # Temporarily disable 'exit on error' to capture the output and status of the create command
  set +e 
  # Create the Kafka topic
  # Capture stdout and stderr, and the exit code
  CREATE_OUTPUT=$(/usr/bin/kafka-topics --bootstrap-server "${KAFKA_BROKER}" \
                                --create \
                                --topic "${TOPIC_NAME}" \
                                --partitions "${PARTITIONS}" \
                                --replication-factor "${REPLICATION_FACTOR}" \
                                --if-not-exists \
                                --command-config /dev/null 2>&1) # Capture all output
  CREATE_STATUS=$? # Get the exit status of the previous command
  # Re-enable 'exit on error'
  set -e

  echo "Kafka topic creation command output:"
  echo "${CREATE_OUTPUT}"
  echo "Kafka topic creation command exit status: ${CREATE_STATUS}"

  if [ "${CREATE_STATUS}" -eq 0 ]; then
    echo "Successfully initiated creation of topic '${TOPIC_NAME}'."
  else
    echo "ERROR: Failed to initiate creation of topic '${TOPIC_NAME}'. See output above for details."
    exit 1 # Exit the script if topic creation command failed
  fi

  # Wait for the topic to be ready (metadata propagation)
  echo "Waiting for topic '${TOPIC_NAME}' to be fully ready and available..."
  start_time=$(date +%s)
  while true; do
    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))

    if [ "${elapsed_time}" -ge "${TIMEOUT_MS}" ]; then
      echo "ERROR: Timeout waiting for topic '${TOPIC_NAME}' to become ready."
      exit 1
    fi

    # Describe the topic and check for partition/leader information
    TOPIC_METADATA=$(/usr/bin/kafka-topics --bootstrap-server "${KAFKA_BROKER}" --describe --topic "${TOPIC_NAME}" 2>&1)
    if echo "${TOPIC_METADATA}" | grep -q "Leader:"; then
      echo "Topic '${TOPIC_NAME}' is ready."
      break
    else
      echo "Topic '${TOPIC_NAME}' not yet ready. Retrying in 2 seconds. Metadata: ${TOPIC_METADATA}"
      sleep 2
    fi
  done
fi

# Produce a test message to the topic
echo "Producing a test message to topic '${TOPIC_NAME}' to confirm readiness..."
TEST_MESSAGE='{"request_id": "TEST_MESSAGE", "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'", "message": "This is a test message from kafka-topic-creator."}'

# Use kafka-console-producer to send a single message
PRODUCER_OUTPUT=$(echo "${TEST_MESSAGE}" | /usr/bin/kafka-console-producer --bootstrap-server "${KAFKA_BROKER}" --topic "${TOPIC_NAME}" 2>&1)
PRODUCER_STATUS=$?

echo "Kafka console producer command output:"
echo "${PRODUCER_OUTPUT}"
echo "Kafka console producer command exit status: ${PRODUCER_STATUS}"

if [ "${PRODUCER_STATUS}" -eq 0 ]; then
  echo "Successfully produced test message to topic '${TOPIC_NAME}'."
else
  echo "WARNING: Failed to produce test message to topic '${TOPIC_NAME}'. This might indicate a lingering issue with topic readiness or producer connectivity."
  # Do not exit here, as topic creation might still have succeeded even if producer failed
fi

echo "Kafka topic creation script finished."

