# mock-backend-gpu1.yaml
# Kubernetes YAML definitions for Mock AI Backend GPU1

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-backend-gpu1
  labels:
    app: mock-backend
    backend-id: mock-backend-gpu1 # Specific label for this backend instance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-backend-gpu1
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-backend-gpu1
        # IMPORTANT: Add the com.docker.compose.service label here for Prometheus discovery
        com.docker.compose.service: mock-backend-gpu1 # This label is crucial for data-processor
    spec:
      containers:
      - name: mock-backend-gpu1 # CORRECTED: Container name now matches backend-id
        image: ai-cost-performance-optimizer-mock-backend-gpu1:latest # <--- Placeholder for substitution
        imagePullPolicy: Always
        ports:
        - containerPort: 5001
          name: http-gpu1
        env:
        - name: LISTEN_ADDR
          value: ":5001"
        - name: BACKEND_ID
          value: "mock-backend-gpu1"
        - name: DELAY_MS
          value: "100"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        args: ["mock_backend_gpu1"]
---
apiVersion: v1
kind: Service
metadata:
  name: mock-backend-gpu1 # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-backend-gpu1 # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5001
    targetPort: 5001
  type: ClusterIP # Use ClusterIP for in-cluster access
