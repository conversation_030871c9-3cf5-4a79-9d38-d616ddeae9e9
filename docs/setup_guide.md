# AI Cost-Performance Optimizer: Setup Guide

This guide walks through the process of setting up and configuring the AI Cost-Performance Optimizer in a Kubernetes environment.

## Prerequisites

- Kubernetes cluster (v1.22+)
- Helm (v3.8+)
- kubectl CLI configured to access your cluster
- Access to LLM provider APIs (OpenAI, Google, Anthropic)
- Redis instance (v6.2+)
- Docker registry access

## Quick Start

```bash
# Clone the repository
git clone https://github.com/your-org/ai-cost-performance-optimizer.git
cd ai-cost-performance-optimizer

# Configure your environment variables
cp .env.example .env
# Edit .env with your API keys and configuration

# Deploy with <PERSON><PERSON>
helm install ai-optimizer ./helm/ai-optimizer
```

## Component Setup

### 1. Proxy Gateway

The Proxy Gateway is the entry point for all LLM requests.

```yaml
# k8s/proxy-gateway/values.yaml
image:
  repository: your-registry/proxy-gateway
  tag: latest
  pullPolicy: Always

config:
  redisUrl: "redis://redis-master:6379"
  apiKeys:
    openai: "${OPENAI_API_KEY}"
    google: "${GOOGLE_API_KEY}"
    anthropic: "${ANTHROPIC_API_KEY}"
  
  caching:
    enabled: true
    ttlSeconds: 3600
    maxEntriesPerNamespace: 1000
    
  rateLimit:
    requestsPerMinute: 300
    burstSize: 50
```

### 2. AI Optimizer Service

The intelligence hub for routing decisions:

```yaml
# k8s/ai-optimizer/values.yaml
image:
  repository: your-registry/ai-optimizer
  tag: latest
  pullPolicy: Always

config:
  classifierModel: "gemini-1.0-pro-classifier"
  optimizationStrategy: "cost-performance-balanced"
  defaultLLM: "gpt-3.5-turbo-mock"
  fallbackChain:
    - "claude-3-opus-********-mock"
    - "gemini-2.5-flash-preview-05-20"
    - "gpt-3.5-turbo-mock"
  redis:
    url: "redis://redis-master:6379"
```

### 3. Dashboard API

For monitoring and administration:

```yaml
# k8s/dashboard-api/values.yaml
image:
  repository: your-registry/dashboard-api
  tag: latest
  pullPolicy: Always

config:
  dbConnectionString: "********************************************/dashboard"
  metricsEnabled: true
  adminAuth:
    jwtSecret: "${JWT_SECRET}"
    sessionTimeoutMinutes: 60
```

## Provider Configuration

### OpenAI Setup

1. Obtain API key from [OpenAI Platform](https://platform.openai.com)
2. Configure in `.env` or as Kubernetes secret:

```bash
kubectl create secret generic llm-api-keys \
  --from-literal=openai-api-key=sk-... \
  --namespace ai-optimizer
```

### Google Gemini Setup

1. Create API key in [Google AI Studio](https://makersuite.google.com/)
2. Configure in environment:

```bash
kubectl create secret generic llm-api-keys \
  --from-literal=google-api-key=AIza... \
  --namespace ai-optimizer
```

### Anthropic Setup

1. Get API key from [Anthropic Console](https://console.anthropic.com/)
2. Add to configuration:

```bash
kubectl create secret generic llm-api-keys \
  --from-literal=anthropic-api-key=sk-ant-... \
  --namespace ai-optimizer
```

## Policy Configuration

Policies define routing rules. Create a policy file:

```json
// policies.json
[
  {
    "id": "code-to-codellama",
    "name": "Route Code Tasks to CodeLlama",
    "description": "Send all code generation tasks to CodeLlama for optimal performance",
    "criteria": {
      "task_type": "code_generation"
    },
    "action": "ROUTE",
    "backend_id": "codellama-mock",
    "priority": 10,
    "status": "active"
  },
  {
    "id": "optimize-for-factual-queries",
    "name": "Optimize Factual Queries",
    "description": "Use cost/performance optimization for factual queries",
    "criteria": {
      "task_type": "factual_query"
    },
    "action": "OPTIMIZE",
    "priority": 20,
    "status": "active"
  }
]
```

Load policies through the Dashboard UI or API:

```bash
curl -X POST http://dashboard-api:8080/api/v1/policies/batch \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d @policies.json
```

## Redis Configuration

Redis is used for caching and token accounting:

```bash
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install redis bitnami/redis \
  --set auth.enabled=false \
  --namespace ai-optimizer
```

## Backend Configuration

Define available LLM backends:

```json
// backends.json
[
  {
    "id": "gpt-3.5-turbo-mock",
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "endpoint": "http://mock-openai:8091/v1/chat/completions",
    "status": "active",
    "capabilities": ["simple_chat", "factual_query", "creative_writing"],
    "cost_per_1k_tokens": {
      "input": 0.0015,
      "output": 0.002
    },
    "avg_latency_ms": 750
  },
  {
    "id": "claude-3-opus-********-mock",
    "provider": "anthropic",
    "model": "claude-3-opus-********",
    "endpoint": "http://mock-anthropic:8093/v1/messages",
    "status": "active",
    "capabilities": ["complex_reasoning", "long_context", "code_generation"],
    "cost_per_1k_tokens": {
      "input": 0.015,
      "output": 0.075
    },
    "avg_latency_ms": 4500
  }
]
```

## Token Accounting Setup

Configure usage tracking for billing and quotas:

```yaml
# token-accounting-config.yaml
accounting:
  enabled: true
  aggregation:
    intervalMinutes: 60
  quotas:
    enabled: true
    defaultDailyTokens: 100000
    defaultMonthlyTokens: 3000000
  alerts:
    quotaThresholds: [50, 80, 95]
    recipients: ["<EMAIL>"]
```

## Security Configuration

```yaml
# security-config.yaml
security:
  authentication:
    type: "jwt"
    jwtSecret: "${JWT_SECRET}"
  authorization:
    enabled: true
    defaultPolicy: "deny"
  encryption:
    dataAtRest: true
    dataInTransit: true
    keyRotationDays: 90
  networkPolicies:
    enabled: true
    allowedNamespaces: ["default", "ai-optimizer"]
```

## Metrics and Monitoring

Enable Prometheus metrics:

```yaml
# monitoring-config.yaml
metrics:
  prometheus:
    enabled: true
    endpoint: "/metrics"
  dashboards:
    grafana:
      enabled: true
  alerts:
    enabled: true
    rules:
      - name: "HighLatency"
        condition: "avg_latency > 5000ms"
        for: "5m"
        severity: "warning"
```

## Troubleshooting

Common issues and solutions:

### Connection Issues

```bash
# Check service status
kubectl get pods -n ai-optimizer

# View logs
kubectl logs -f deployment/proxy-gateway -n ai-optimizer
kubectl logs -f deployment/ai-optimizer -n ai-optimizer

# Check Redis connectivity
kubectl exec -it deployment/proxy-gateway -n ai-optimizer -- redis-cli -h redis-master ping
```

### Authentication Problems

```bash
# Check API key secrets
kubectl describe secret llm-api-keys -n ai-optimizer

# Test API connections
kubectl exec -it deployment/proxy-gateway -n ai-optimizer -- curl -v https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"
```

### Performance Issues

```bash
# Check resource utilization
kubectl top pods -n ai-optimizer

# Analyze service response times
kubectl exec -it deployment/proxy-gateway -n ai-optimizer -- curl http://localhost:8080/metrics | grep latency
```

## Upgrade Procedure

```bash
# Update configuration
kubectl apply -f updated-config.yaml

# Update Helm release
helm upgrade ai-optimizer ./helm/ai-optimizer --values ./helm/ai-optimizer/values.yaml

# Verify deployment
kubectl rollout status deployment/proxy-gateway -n ai-optimizer
kubectl rollout status deployment/ai-optimizer -n ai-optimizer
```

## Next Steps

After setup, proceed to:

1. [Routing Examples](routing_examples.md) for configuring intelligent routing
2. [Cost Model](cost_model.md) for understanding the optimization algorithms
3. [Architecture](architecture.md) for a deeper dive into system design
