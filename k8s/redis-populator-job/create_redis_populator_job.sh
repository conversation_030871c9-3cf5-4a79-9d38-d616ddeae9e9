#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration from Environment Variables (passed from Cloud Build) ---
export GCP_PROJECT_ID="${GCP_PROJECT_ID}"
export GCP_REGION="${GCP_REGION}"
export ARTIFACT_REGISTRY_REPO="${ARTIFACT_REGISTRY_REPO}"
export BUILD_ID="${BUILD_ID}"
export GKE_CLUSTER_NAME="${GKE_CLUSTER_NAME}"

echo "Injecting API Keys via Kubernetes Secrets. Ensure secrets are created in the cluster."
echo "Correcting Redis connection environment variables for populate_redis.py."

# Construct the full image name
REDIS_POPULATOR_IMAGE="${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/ai-cost-performance-optimizer-redis-populator-job:latest"

# Define a unique job name to avoid conflicts if previous jobs are still around
JOB_NAME="redis-populator-job-${BUILD_ID}"

echo "Creating Kubernetes Job to populate Redis..."
echo "Job Name: ${JOB_NAME}"
echo "Redis Populator Image: ${REDIS_POPULATOR_IMAGE}"


# Kubernetes Job YAML
cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: ${JOB_NAME}
  namespace: default # Or your target namespace
spec:
  template:
    spec:
      containers:
      - name: redis-populator
        image: ${REDIS_POPULATOR_IMAGE}
        # Explicitly run the Python script directly
        command: ["python3", "/app/populate_redis.py"] # Corrected: Direct Python script execution
        env:
        # Corrected: Pass Redis connection details as separate environment variables
        - name: REDIS_HOST
          value: "redis" # Kubernetes service name for Redis
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_DB
          value: "0" # Default Redis database
        - name: REDIS_PASSWORD # If your Redis requires a password, configure this
          value: "" # Assuming no password for now. Use valueFrom.secretKeyRef for secure passwords.
          # Example for secret:
          # valueFrom:
          #   secretKeyRef:
          #     name: redis-password-secret
          #     key: password
          #     optional: true

        # Pass API Keys from Kubernetes Secrets (assuming you've created these secrets with New_Autostart.sh)
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-api-key
              key: OPENAI_API_KEY
              optional: true
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: google-api-key
              key: GOOGLE_API_KEY
              optional: true
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: anthropic-api-key
              key: ANTHROPIC_API_KEY
              optional: true
        - name: LLAMA3_API_KEY
          valueFrom:
            secretKeyRef:
              name: llama3-api-key
              key: LLAMA3_API_KEY
              optional: true
      restartPolicy: OnFailure
  backoffLimit: 3 # Retry up to 3 times on failure
EOF


echo "Waiting for Redis Populator Job ${JOB_NAME} to complete..."
kubectl wait --for=condition=complete job/${JOB_NAME} --timeout=300s || {
    echo "Redis Populator Job ${JOB_NAME} failed or timed out. Checking logs..."
    kubectl logs job/${JOB_NAME}
    exit 1
}
echo "Redis Populator Job ${JOB_NAME} completed successfully."

# Comment out the cleanup lines TEMPORARILY for debugging
# echo "Cleaning up Redis Populator Job ${JOB_NAME}..."
# kubectl delete job/${JOB_NAME} --ignore-not-found=true --grace-period=0 --force
# echo "Redis Populator Job ${JOB_NAME} cleaned up."

