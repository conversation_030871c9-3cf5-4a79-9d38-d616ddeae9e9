{"version": 4, "terraform_version": "1.11.4", "serial": 8, "lineage": "f568e341-48cc-edec-f2c4-d018f767480a", "outputs": {}, "resources": [{"mode": "data", "type": "google_client_config", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"access_token": "ya29.c.c0ASRK0GZhZHkU_XbP0t_zh31LlG8_RA6kYI8w4glCUjFQ7JFRISOB7L_Ums9nV-DeYz5FS0xjjvmaoLF5tAWi6KxpQ9Vx_WvGQX_EHINyoTS9uc8zjAW5ZJAkV_4hGWfFVSPuY1lnMUMEZqn1qKMbh4dBkIIgmcWnBfjT7VnD7K3N7N8LpYgeN4Nm8WpRuSz5BPW7MGw1Q8SBZWboPKqcBeVu47R1W33jRrJfM6E3F-WU3j7PeOox3M4tI0_INivqJazT3bho3KyEID7Gl-Z7uso9YfpLwrDPd2yASjjDXvret_zo4Q8kR1MXlcYRlVxTrPMFCDDydcBJVCi_DGeyZkYDDWReVJy8-F4ew3S-g6aevHGc6K0TrRonQAN387DBhXRYq2kxSW3doW6hdco_gQVMaJ6Onz8VUw1oFVFsRO4Jx92zUi97hywgpR1t3j9jblygpwxw1bW6Fb_l5OffpXcgQuWms0JBcYfVa1mewgXut_ng9WwpOOBz8d1ovX4-Fk04baOnrykUZi42O4gldYciF7mvSQvQVbss3qUilSfFMeBgjdxs6S8vMeSFgelSmO0Yt5Sz8j8bkxuwta3jF2ictlV0_fj37J4FzJZM_Qv4QIds1feMWz_ttlspzXR93J8iXokfu9BlQ6ntpdSdu3iBo23RW61e3ueZezFxFUmFFvxYrJbdgsUqSd4zb7p-Safx3QF1xoXydXiWyhseIBhk1ifWtj8v_xl0RkgXpd0bFil7R_F8fz56d2yVquI-o0YyRZkS2ueXupWodgpYb9-9ngnx4r2er6BXy7kafitXzldItqivZWciwWc984IdMexY0Xw2jeScicXWo0i9OyJXB-mV5198hbpx4qqbl-pjwomrbeVo5ot07_IO0v4aFZsRmnaqm1qVsSSa5YqYmY6zukXopgw0w6xko0_Zbtsc79sedpgvwOvqzmMRu2fBJj_-fuwgW0vQ-2Vlpkem2VkhiSn2r89cX8bwU08ruhbUgg36zIdnzyr", "id": "projects/\"silken-zenith-460615-s7\"/regions/<null>/zones/<null>", "project": "silken-zenith-460615-s7", "region": null, "zone": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "access_token"}]]}]}, {"mode": "data", "type": "google_container_cluster", "name": "primary", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": true}]}], "allow_net_admin": null, "authenticator_groups_config": [], "binary_authorization": [], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "*********/14", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "", "default_max_pods_per_node": 110, "default_snat_status": [], "description": "", "dns_config": [], "enable_autopilot": false, "enable_binary_authorization": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "*************", "gateway_api_config": [], "id": "projects/silken-zenith-460615-s7/locations/us-central1/clusters/ai-optimizer-cluster", "initial_node_count": 0, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "*********/14", "cluster_secondary_range_name": "gke-ai-optimizer-cluster-pods-8ea9e383", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "************/20", "services_secondary_range_name": "", "stack_type": "IPV4"}], "label_fingerprint": "cfb7e07e", "location": "us-central1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.32.4-gke.1415000", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "relay_mode": ""}], "enable_components": ["SYSTEM_COMPONENTS", "DEPLOYMENT", "STATEFULSET", "JOBSET", "STORAGE", "HPA", "POD", "DAEMONSET", "CADVISOR", "KUBELET", "DCGM"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "ai-optimizer-cluster", "network": "projects/silken-zenith-460615-s7/global/networks/default", "network_policy": [{"enabled": false, "provider": "PROVIDER_UNSPECIFIED"}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [{"threads_per_core": 0}], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 20, "disk_type": "pd-standard", "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "pod_pids_limit": 0}], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-4", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"], "preemptible": false, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "on-demand"}, "service_account": "default", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "node_pool": [{"autoscaling": [{"location_policy": "ANY", "max_node_count": 0, "min_node_count": 0, "total_max_node_count": 3, "total_min_node_count": 1}], "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-c/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-76d5b428-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-a/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-5ea61ba4-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-b/instanceGroupManagers/gke-ai-optimizer-cluster-pool-1-cd25aa8f-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-c/instanceGroups/gke-ai-optimizer-cluster-pool-1-76d5b428-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-a/instanceGroups/gke-ai-optimizer-cluster-pool-1-5ea61ba4-grp", "https://www.googleapis.com/compute/v1/projects/silken-zenith-460615-s7/zones/us-central1-b/instanceGroups/gke-ai-optimizer-cluster-pool-1-cd25aa8f-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "pool-1", "name_prefix": "", "network_config": [{"create_pod_range": false, "enable_private_nodes": false, "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "*********/14", "pod_range": "gke-ai-optimizer-cluster-pods-8ea9e383"}], "node_config": [{"advanced_machine_features": [{"threads_per_core": 0}], "boot_disk_kms_key": "", "confidential_nodes": [], "disk_size_gb": 20, "disk_type": "pd-standard", "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "pod_pids_limit": 0}], "labels": {}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-4", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"], "preemptible": false, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "on-demand"}, "service_account": "default", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": [], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_count": 0, "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "placement_policy": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.32.4-gke.1415000"}], "node_pool_auto_config": [{"network_tags": null}], "node_pool_defaults": [{"node_config_defaults": [{"logging_variant": "DEFAULT"}]}], "node_version": "1.32.4-gke.1415000", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": false, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "", "peering_name": "", "private_endpoint": "***********", "private_endpoint_subnetwork": "", "public_endpoint": "*************"}], "private_ipv6_google_access": "", "project": "silken-zenith-460615-s7", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": null, "resource_labels": {"app": "ai-optimizer-platform", "env": "dev"}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/silken-zenith-460615-s7/locations/us-central1/clusters/ai-optimizer-cluster", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "************/20", "subnetwork": "projects/silken-zenith-460615-s7/regions/us-central1/subnetworks/default", "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": [{"workload_pool": "silken-zenith-460615-s7.svc.id.goog"}]}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "kubernetes_config_map", "name": "kafka-log4j-config", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"binary_data": {}, "data": {"log4j.properties": "# Log4j 1.x properties format for backward compatibility\nlog4j.rootLogger=INFO, stdout, kafkaAppender\n\n# Stdout appender\nlog4j.appender.stdout=org.apache.log4j.ConsoleAppender\nlog4j.appender.stdout.layout=org.apache.log4j.PatternLayout\nlog4j.appender.stdout.layout.ConversionPattern=[%d] %p %m (%c)%n\n\n# Kafka appender\nlog4j.appender.kafkaAppender=org.apache.log4j.RollingFileAppender\nlog4j.appender.kafkaAppender.File=${kafka.logs.dir}/server.log\nlog4j.appender.kafkaAppender.layout=org.apache.log4j.PatternLayout\nlog4j.appender.kafkaAppender.layout.ConversionPattern=[%d] %p %m (%c)%n\nlog4j.appender.kafkaAppender.MaxFileSize=50MB\nlog4j.appender.kafkaAppender.MaxBackupIndex=10\n\n# State change logger\nlog4j.logger.kafka.controller=INFO, controllerAppender\nlog4j.additivity.kafka.controller=false\nlog4j.appender.controllerAppender=org.apache.log4j.RollingFileAppender\nlog4j.appender.controllerAppender.File=${kafka.logs.dir}/controller.log\nlog4j.appender.controllerAppender.layout=org.apache.log4j.PatternLayout\nlog4j.appender.controllerAppender.layout.ConversionPattern=[%d] %p %m (%c)%n\nlog4j.appender.controllerAppender.MaxFileSize=50MB\nlog4j.appender.controllerAppender.MaxBackupIndex=10\n\n# Request logger\nlog4j.logger.kafka.request.logger=WARN, requestAppender\nlog4j.additivity.kafka.request.logger=false\nlog4j.appender.requestAppender=org.apache.log4j.RollingFileAppender\nlog4j.appender.requestAppender.File=${kafka.logs.dir}/kafka-request.log\nlog4j.appender.requestAppender.layout=org.apache.log4j.PatternLayout\nlog4j.appender.requestAppender.layout.ConversionPattern=[%d] %p %m (%c)%n\nlog4j.appender.requestAppender.MaxFileSize=50MB\nlog4j.appender.requestAppender.MaxBackupIndex=10\n\n# Kafka logs\nlog4j.logger.kafka=INFO\nlog4j.logger.org.apache.kafka=INFO\n"}, "id": "default/kafka-log4j-config", "immutable": false, "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "kafka-log4j-config", "namespace": "default", "resource_version": "1750931503945407018", "uid": "0e6bf50f-8541-4bd9-a5d7-cd90820c5d02"}]}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "ai-optimizer", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/ai-optimizer", "metadata": [{"annotations": {}, "generate_name": "", "generation": 1, "labels": {"app": "ai-optimizer"}, "name": "ai-optimizer", "namespace": "default", "resource_version": "1750975806238287017", "uid": "98d45811-39f6-438b-8326-a2f079f7e547"}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "1", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "ai-optimizer"}}], "strategy": [{"rolling_update": [{"max_surge": "25%", "max_unavailable": "25%"}], "type": "RollingUpdate"}], "template": [{"metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {"app": "ai-optimizer"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": false, "container": [{"args": [], "command": [], "env": [{"name": "KAFKA_BROKERS", "value": "kafka:9092", "value_from": []}, {"name": "KAFKA_TOPIC", "value": "inference-logs", "value_from": []}, {"name": "CLICKHOUSE_HOST", "value": "clickhouse", "value_from": []}, {"name": "CLICKHOUSE_PORT", "value": "9000", "value_from": []}, {"name": "CLICKHOUSE_DB", "value": "default", "value_from": []}, {"name": "CLICKHOUSE_USER", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "user", "name": "clickhouse-credentials", "optional": false}]}]}, {"name": "CLICKHOUSE_PASSWORD", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "password", "name": "clickhouse-credentials", "optional": false}]}]}, {"name": "REDIS_ADDR", "value": "redis:6379", "value_from": []}, {"name": "REDIS_PASSWORD", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "password", "name": "redis-credentials", "optional": true}]}]}, {"name": "REDIS_DB", "value": "0", "value_from": []}, {"name": "PROMETHEUS_HOST", "value": "prometheus", "value_from": []}, {"name": "PROMETHEUS_PORT", "value": "9090", "value_from": []}, {"name": "GKE_PROJECT_ID", "value": "${GCP_PROJECT_ID}", "value_from": []}, {"name": "GOOGLE_API_KEY", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "GOOGLE_API_KEY", "name": "google-api-key", "optional": false}]}]}], "env_from": [], "image": "ai-cost-performance-optimizer-ai-optimizer:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "ai-optimizer", "port": [{"container_port": 8085, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}], "readiness_probe": [], "resources": [{"limits": {"cpu": "200m", "memory": "256Mi"}, "requests": {"cpu": "100m", "memory": "128Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": false, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [{"args": [], "command": ["sh", "-c", "echo \"Waiting for <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>...\"; until nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> are ready.\""], "env": [], "env_from": [], "image": "busybox:1.36", "image_pull_policy": "IfNotPresent", "lifecycle": [], "liveness_probe": [], "name": "wait-for-clickhouse-and-redis", "port": [], "readiness_probe": [], "resources": [{"limits": {"cpu": "20m", "memory": "20Mi"}, "requests": {"cpu": "10m", "memory": "10Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}, {"args": ["echo \"Starting wait-for-inference-logs-table init container...\"\nCLICKHOUSE_POD=\"\"\nMAX_POD_RETRIES=20\nPOD_RETRY_COUNT=0\n\n# First, find the ClickHouse pod\n# Use '|| true' to prevent script from exiting if kubectl get fails initially\nwhile [ -z \"$CLICKHOUSE_POD\" ] && [ \"$POD_RETRY_COUNT\" -lt \"$MAX_POD_RETRIES\" ]; do\n  echo \"Attempting to find ClickHouse pod (attempt $((POD_RETRY_COUNT + 1))/${MAX_POD_RETRIES})...\"\n  CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)\n  if [ -z \"$CLICKHOUSE_POD\" ]; then\n    sleep 10 # Wait longer if pod isn't found\n  fi\n  POD_RETRY_COUNT=$((POD_RETRY_COUNT+1))\ndone\n\nif [ -z \"$CLICKHOUSE_POD\" ]; then\n  echo \"Error: ClickHouse pod not found after multiple retries. Exiting init container.\"\n  exit 1 # Fail init container if ClickHouse pod is not found\nfi\necho \"Found ClickHouse pod: $CLICKHOUSE_POD\"\n\n# Now, wait for the 'inference_logs' table inside the ClickHouse pod\nMAX_TABLE_RETRIES=60 # Total wait time: 60 * 5s = 5 minutes\nTABLE_RETRY_COUNT=0\n# Use 'EXISTS TABLE' and check for '1' in output to confirm table existence\nuntil kubectl exec \"$CLICKHOUSE_POD\" -- clickhouse-client -q \"EXISTS TABLE inference_logs;\" 2>/dev/null | grep -q \"1\"; do\n  echo \"Table 'inference_logs' not found in ClickHouse pod $CLICKHOUSE_POD (attempt $((TABLE_RETRY_COUNT + 1))/${MAX_TABLE_RETRIES}). Retrying in 5 seconds...\"\n  sleep 5\n  TABLE_RETRY_COUNT=$((TABLE_RETRY_COUNT+1))\n  if [ \"$TABLE_RETRY_COUNT\" -ge \"$MAX_TABLE_RETRIES\" ]; then\n    echo \"Timeout: ClickHouse table 'inference_logs' did not appear within the expected time.\"\n    exit 1 # Fail init container if table doesn't appear\n  fi\ndone\n\necho \"ClickHouse table 'inference_logs' is ready for ai-optimizer.\"\n"], "command": ["/bin/bash", "-c"], "env": [], "env_from": [], "image": "gcr.io/cloud-builders/kubectl:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "wait-for-inference-logs-table", "port": [], "readiness_probe": [], "resources": [{"limits": {"cpu": "200m", "memory": "200Mi"}, "requests": {"cpu": "100m", "memory": "100Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "node_name": "", "node_selector": {}, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "ai-optimizer-sa", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "ai_optimizer", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/ai-optimizer", "metadata": [{"annotations": {}, "generate_name": "", "generation": 1, "labels": {"app": "ai-optimizer"}, "name": "ai-optimizer", "namespace": "default", "resource_version": "1750975806238287017", "uid": "98d45811-39f6-438b-8326-a2f079f7e547"}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "1", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "ai-optimizer"}}], "strategy": [{"rolling_update": [{"max_surge": "25%", "max_unavailable": "25%"}], "type": "RollingUpdate"}], "template": [{"metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {"app": "ai-optimizer"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": false, "container": [{"args": [], "command": [], "env": [{"name": "KAFKA_BROKERS", "value": "kafka:9092", "value_from": []}, {"name": "KAFKA_TOPIC", "value": "inference-logs", "value_from": []}, {"name": "CLICKHOUSE_HOST", "value": "clickhouse", "value_from": []}, {"name": "CLICKHOUSE_PORT", "value": "9000", "value_from": []}, {"name": "CLICKHOUSE_DB", "value": "default", "value_from": []}, {"name": "CLICKHOUSE_USER", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "user", "name": "clickhouse-credentials", "optional": false}]}]}, {"name": "CLICKHOUSE_PASSWORD", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "password", "name": "clickhouse-credentials", "optional": false}]}]}, {"name": "REDIS_ADDR", "value": "redis:6379", "value_from": []}, {"name": "REDIS_PASSWORD", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "password", "name": "redis-credentials", "optional": true}]}]}, {"name": "REDIS_DB", "value": "0", "value_from": []}, {"name": "PROMETHEUS_HOST", "value": "prometheus", "value_from": []}, {"name": "PROMETHEUS_PORT", "value": "9090", "value_from": []}, {"name": "GKE_PROJECT_ID", "value": "${GCP_PROJECT_ID}", "value_from": []}, {"name": "GOOGLE_API_KEY", "value": "", "value_from": [{"config_map_key_ref": [], "field_ref": [], "resource_field_ref": [], "secret_key_ref": [{"key": "GOOGLE_API_KEY", "name": "google-api-key", "optional": false}]}]}], "env_from": [], "image": "ai-cost-performance-optimizer-ai-optimizer:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "ai-optimizer", "port": [{"container_port": 8085, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}], "readiness_probe": [], "resources": [{"limits": {"cpu": "200m", "memory": "256Mi"}, "requests": {"cpu": "100m", "memory": "128Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": false, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [{"args": [], "command": ["sh", "-c", "echo \"Waiting for <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>...\"; until nc -z clickhouse 9000 && nc -z redis 6379; do echo \"ClickHouse or Redis not ready, waiting...\"; sleep 2; done; echo \"<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> are ready.\""], "env": [], "env_from": [], "image": "busybox:1.36", "image_pull_policy": "IfNotPresent", "lifecycle": [], "liveness_probe": [], "name": "wait-for-clickhouse-and-redis", "port": [], "readiness_probe": [], "resources": [{"limits": {"cpu": "20m", "memory": "20Mi"}, "requests": {"cpu": "10m", "memory": "10Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}, {"args": ["echo \"Starting wait-for-inference-logs-table init container...\"\nCLICKHOUSE_POD=\"\"\nMAX_POD_RETRIES=20\nPOD_RETRY_COUNT=0\n\n# First, find the ClickHouse pod\n# Use '|| true' to prevent script from exiting if kubectl get fails initially\nwhile [ -z \"$CLICKHOUSE_POD\" ] && [ \"$POD_RETRY_COUNT\" -lt \"$MAX_POD_RETRIES\" ]; do\n  echo \"Attempting to find ClickHouse pod (attempt $((POD_RETRY_COUNT + 1))/${MAX_POD_RETRIES})...\"\n  CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)\n  if [ -z \"$CLICKHOUSE_POD\" ]; then\n    sleep 10 # Wait longer if pod isn't found\n  fi\n  POD_RETRY_COUNT=$((POD_RETRY_COUNT+1))\ndone\n\nif [ -z \"$CLICKHOUSE_POD\" ]; then\n  echo \"Error: ClickHouse pod not found after multiple retries. Exiting init container.\"\n  exit 1 # Fail init container if ClickHouse pod is not found\nfi\necho \"Found ClickHouse pod: $CLICKHOUSE_POD\"\n\n# Now, wait for the 'inference_logs' table inside the ClickHouse pod\nMAX_TABLE_RETRIES=60 # Total wait time: 60 * 5s = 5 minutes\nTABLE_RETRY_COUNT=0\n# Use 'EXISTS TABLE' and check for '1' in output to confirm table existence\nuntil kubectl exec \"$CLICKHOUSE_POD\" -- clickhouse-client -q \"EXISTS TABLE inference_logs;\" 2>/dev/null | grep -q \"1\"; do\n  echo \"Table 'inference_logs' not found in ClickHouse pod $CLICKHOUSE_POD (attempt $((TABLE_RETRY_COUNT + 1))/${MAX_TABLE_RETRIES}). Retrying in 5 seconds...\"\n  sleep 5\n  TABLE_RETRY_COUNT=$((TABLE_RETRY_COUNT+1))\n  if [ \"$TABLE_RETRY_COUNT\" -ge \"$MAX_TABLE_RETRIES\" ]; then\n    echo \"Timeout: ClickHouse table 'inference_logs' did not appear within the expected time.\"\n    exit 1 # Fail init container if table doesn't appear\n  fi\ndone\n\necho \"ClickHouse table 'inference_logs' is ready for ai-optimizer.\"\n"], "command": ["/bin/bash", "-c"], "env": [], "env_from": [], "image": "gcr.io/cloud-builders/kubectl:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "wait-for-inference-logs-table", "port": [], "readiness_probe": [], "resources": [{"limits": {"cpu": "200m", "memory": "200Mi"}, "requests": {"cpu": "100m", "memory": "100Mi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "node_name": "", "node_selector": {}, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "ai-optimizer-sa", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_service", "name": "ai-optimizer", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/ai-optimizer", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": {}, "name": "ai-optimizer", "namespace": "default", "resource_version": "1750975806091663004", "uid": "3361eeef-17fb-4067-9dff-49c46d933c94"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": [], "external_name": "", "external_traffic_policy": "", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "classifier-http", "node_port": 0, "port": 8085, "protocol": "TCP", "target_port": "8085"}], "publish_not_ready_addresses": false, "selector": {"app": "ai-optimizer"}, "session_affinity": "None", "session_affinity_config": [], "type": "ClusterIP"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_service", "name": "clickhouse", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/clickhouse", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": {}, "name": "clickhouse", "namespace": "default", "resource_version": "1750931502523871008", "uid": "7e795a32-4bba-4ac0-baea-abfecb466dc8"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "**************", "cluster_ips": ["**************"], "external_ips": [], "external_name": "", "external_traffic_policy": "", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "client", "node_port": 0, "port": 9000, "protocol": "TCP", "target_port": "9000"}, {"app_protocol": "", "name": "http", "node_port": 0, "port": 8123, "protocol": "TCP", "target_port": "8123"}], "publish_not_ready_addresses": false, "selector": {"app": "clickhouse"}, "session_affinity": "None", "session_affinity_config": [], "type": "ClusterIP"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_service", "name": "kafka", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/kafka", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": {}, "name": "kafka", "namespace": "default", "resource_version": "1750931504947631018", "uid": "8dd36d24-77f9-4fef-86df-ee9d674f896b"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "*************", "cluster_ips": ["*************"], "external_ips": [], "external_name": "", "external_traffic_policy": "", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "plaintext", "node_port": 0, "port": 9092, "protocol": "TCP", "target_port": "9092"}], "publish_not_ready_addresses": false, "selector": {"app": "kafka"}, "session_affinity": "None", "session_affinity_config": [], "type": "ClusterIP"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_service", "name": "prometheus", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/prometheus", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": {}, "name": "prometheus", "namespace": "default", "resource_version": "1750931545499935011", "uid": "af3dbbb4-d6cb-4b0f-bf09-c0efd7fcc135"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "*************", "cluster_ips": ["*************"], "external_ips": [], "external_name": "", "external_traffic_policy": "Cluster", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "", "node_port": 30957, "port": 9090, "protocol": "TCP", "target_port": "9090"}], "publish_not_ready_addresses": false, "selector": {"app": "prometheus"}, "session_affinity": "None", "session_affinity_config": [], "type": "LoadBalancer"}], "status": [{"load_balancer": [{"ingress": [{"hostname": "", "ip": "************"}]}]}], "timeouts": null, "wait_for_load_balancer": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************"}]}, {"mode": "managed", "type": "kubernetes_service", "name": "redis", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/redis", "metadata": [{"annotations": {"cloud.google.com/neg": "{\"ingress\":true}"}, "generate_name": "", "generation": 0, "labels": {}, "name": "redis", "namespace": "default", "resource_version": "1750931505668383023", "uid": "d8193e06-aeab-4ab0-97f2-5c29ba7138cb"}], "spec": [{"allocate_load_balancer_node_ports": true, "cluster_ip": "************", "cluster_ips": ["************"], "external_ips": [], "external_name": "", "external_traffic_policy": "", "health_check_node_port": 0, "internal_traffic_policy": "Cluster", "ip_families": ["IPv4"], "ip_family_policy": "SingleStack", "load_balancer_class": "", "load_balancer_ip": "", "load_balancer_source_ranges": [], "port": [{"app_protocol": "", "name": "tcp-port", "node_port": 0, "port": 6379, "protocol": "TCP", "target_port": "6379"}], "publish_not_ready_addresses": false, "selector": {"app": "redis"}, "session_affinity": "None", "session_affinity_config": [], "type": "ClusterIP"}], "status": [{"load_balancer": [{"ingress": []}]}], "timeouts": null, "wait_for_load_balancer": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************"}]}], "check_results": null}