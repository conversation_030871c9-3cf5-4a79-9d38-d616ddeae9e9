apiVersion: apps/v1
kind: Deployment
metadata:
  name: governance-service
spec:
  selector:
    matchLabels:
      app: governance-service
  replicas: 1
  template:
    metadata:
      labels:
        app: governance-service
    spec:
      containers:
        - name: governance-service
          image: us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-governance-service:latest
          ports:
            - containerPort: 8080
