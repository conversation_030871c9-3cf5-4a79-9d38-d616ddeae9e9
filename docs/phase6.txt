Integrating Your AI Application with the LLM Cost-Performance Optimizer
The LLM Cost-Performance Optimizer is designed to simplify and enhance how developers use Large Language Models (LLMs) in their applications. Instead of directly calling various LLM providers (like OpenAI, Anthropic, Google, etc.), developers send all their LLM requests through your intelligent Proxy Gateway. This gateway then handles routing, optimization, and policy enforcement transparently.

1. Core Integration: Sending Requests to the Proxy
The fundamental change for a developer is that their application will no longer make API calls to api.openai.com, api.anthropic.com, or similar endpoints. Instead, all LLM requests (specifically chat completions) will be directed to your Proxy Gateway.

Main API Endpoint for Chat Completions:

All chat completion requests should be sent as POST requests to:

http://<YOUR_FRONTEND_DASHBOARD_IP>:8080/v1/chat/completions

Replace <YOUR_FRONTEND_DASHBOARD_IP> with the actual external IP address or hostname where your frontend-dashboard service is accessible.

The request body (payload) should conform to the standard LLM Chat Completion API format (e.g., the widely adopted OpenAI API specification for messages, model, temperature, etc.).

2. Key Features for Developers
Your product offers several advantages to developers:

Intelligent Routing: Based on the policies you, as an administrator, define, the proxy will automatically select the most appropriate backend LLM for each request. This could be based on criteria like prompt content, user roles, token length, or desired performance/cost.

Cost & Performance Optimization: Developers don't need to manually switch between LLMs to find the cheapest or fastest one. The optimizer automatically routes requests to meet a predefined preference (e.g., LATENCY, COST, BALANCED).

Conversational Context (Automatic conversation_id Management):

For multi-turn conversations, the proxy can maintain context. Developers simply include the conversation_id (obtained from the first response) in subsequent requests. The proxy will automatically retrieve and inject prior messages into the current prompt before sending it to the chosen LLM.

If no conversation_id is provided for the first turn, the proxy will generate one and include it in the response.

Policy Application: Developers benefit from centrally defined policies without needing to implement complex routing logic in their own applications.

Manual Overrides (Advanced): For specific use cases, developers can explicitly request a particular backend LLM by setting a custom HTTP header.

3. API Usage Examples for Developers (Curl)
Here are common curl examples developers would use to interact with your proxy:

3.1. Basic Chat Completion Request (Intelligent Routing Applied)
The proxy will automatically select the best LLM based on configured policies and optimization preferences.

curl -X POST \
  http://<YOUR_FRONTEND_DASHBOARD_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'

3.2. Conversational Turn 1 (New Conversation)
For the first turn of a conversation, omit conversation_id. The proxy will generate one.

curl -X POST \
  http://<YOUR_FRONTEND_DASHBOARD_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
# The response will contain a 'conversation_id' in the payload or headers.

3.3. Conversational Turn 2 (Continuing the Conversation)
Use the conversation_id obtained from the first turn's response. The proxy will retrieve previous messages from the conversation history.

curl -X POST \
  http://<YOUR_FRONTEND_DASHBOARD_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID_FROM_PREVIOUS_RESPONSE>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'

3.4. Request with User Override (X-Preferred-LLM-ID Header)
Developers can override the automatic routing by specifying a preferred LLM ID (as defined in your LLM Profiles) using the X-Preferred-LLM-ID header.

curl -X POST \
  http://<YOUR_FRONTEND_DASHBOARD_IP>:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gpt-4-turbo-us-east" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'

Note: The model field in the request body (e.g., "model": "gpt-3.5-turbo") still acts as a hint or fallback if no policy matches or the X-Preferred-LLM-ID is invalid/unavailable. It's generally best practice to send the model you intended to use, even if the proxy might route to a different one.

4. Importable Client Libraries for Developers
Creating client libraries is an excellent idea for improving the developer experience. A well-designed client library would:

Abstract HTTP Calls: Developers wouldn't need to manually construct curl commands or fetch requests.

Handle JSON Serialization/Deserialization: Automatically convert Python/JavaScript objects to JSON payloads and parse JSON responses.

Manage Headers: Provide easy ways to include conversation_id or X-Preferred-LLM-ID without boilerplate.

Provide Type Safety (for typed languages): Offer clear data structures for requests and responses, reducing errors.

Include Error Handling: Encapsulate common API error responses.

4.1. What a Client Library Would Encapsulate:
Base URL Configuration: Allow setting YOUR_FRONTEND_DASHBOARD_IP easily.

Chat Completion Method: A single method (e.g., client.chat.completions.create(...)) that takes the standard LLM payload.

Conversation ID Handling: Methods or parameters for passing conversation_id.

Override Header Inclusion: Parameters to easily add X-Preferred-LLM-ID.

Standard LLM Response Parsing: Provide a structured object for the response.

4.2. Example: Python Client Library (Pseudo-code)
import requests
import json
from typing import List, Dict, Any, Optional

class LLMOptimizerClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.chat_completions_endpoint = f"{self.base_url}/v1/chat/completions"

    def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        conversation_id: Optional[str] = None,
        preferred_llm_id: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Sends a chat completion request to the LLM Optimizer Proxy.

        Args:
            messages (List[Dict[str, str]]): List of message objects.
            model (str): The model name (used as a hint/fallback by the proxy).
            temperature (float): Sampling temperature.
            max_tokens (Optional[int]): Maximum number of tokens to generate.
            conversation_id (Optional[str]): ID to continue a conversation.
            preferred_llm_id (Optional[str]): Forces routing to a specific LLM.
            **kwargs: Additional parameters to pass to the LLM API.

        Returns:
            Dict[str, Any]: The response payload from the LLM.
        """
        payload = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            **kwargs
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if conversation_id:
            payload["conversation_id"] = conversation_id

        headers = {"Content-Type": "application/json"}
        if preferred_llm_id:
            headers["X-Preferred-LLM-ID"] = preferred_llm_id

        try:
            response = requests.post(
                self.chat_completions_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            raise
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            raise
        except Exception as err:
            print(f"An unexpected error occurred: {err}")
            raise

# --- How a Developer Would Use It ---
if __name__ == "__main__":
    # Replace with your actual deployed frontend IP
    OPTIMIZER_BASE_URL = "http://localhost:8080" # Or your actual IP/hostname

    client = LLMOptimizerClient(OPTIMIZER_BASE_URL)

    # Example 1: Basic Request (Proxy chooses LLM)
    try:
        response_basic = client.create_chat_completion(
            messages=[{"role": "user", "content": "What is the capital of France?"}],
            model="gpt-3.5-turbo" # Hint model
        )
        print("Basic Request Response:", json.dumps(response_basic, indent=2))
        first_conversation_id = response_basic.get("conversation_id") or response_basic.get("id") # Assuming ID is conversation_id
        if first_conversation_id:
            print(f"Initial conversation ID: {first_conversation_id}")
    except Exception as e:
        print(f"Error during basic request: {e}")

    # Example 2: Conversational Request (Turn 2)
    if first_conversation_id:
        try:
            response_conv = client.create_chat_completion(
                messages=[{"role": "user", "content": "And what is its population?"}],
                model="gpt-3.5-turbo",
                conversation_id=first_conversation_id
            )
            print("\nConversational Request Response:", json.dumps(response_conv, indent=2))
        except Exception as e:
            print(f"Error during conversational request: {e}")

    # Example 3: Request with LLM Override
    try:
        response_override = client.create_chat_completion(
            messages=[{"role": "user", "content": "Write a haiku about a coding bug."}],
            model="claude-3-opus-********", # Hint model
            preferred_llm_id="google-gemini-pro" # Forces routing to a specific LLM profile ID
        )
        print("\nOverride Request Response:", json.dumps(response_override, indent=2))
    except Exception as e:
        print(f"Error during override request: {e}")

4.3. Other Client Libraries to Consider
JavaScript/TypeScript: For Node.js backends or browser-based applications using fetch or axios. This would be very similar in structure to the Python example.

Go: Given your backend is in Go, a Go client library would be natural for other Go services integrating with the proxy.

Ruby, Java, C#, etc.: Depending on your target developer audience.

5. Benefits of Using Your Product (Value Proposition for Developers)
When explaining this to developers, emphasize:

"Set it and Forget it" Optimization: Developers write code once to hit your proxy, and the backend infrastructure automatically handles cost and performance decisions.

Future-Proofing: As new, better, or cheaper LLMs emerge, you can update your backend policies and LLM profiles without requiring any code changes in the developers' applications.

Simplified LLM Management: No need for developers to manage API keys for multiple LLMs in their own app, or to implement complex failover logic.

Centralized Observability: They can use your dashboard to see how their requests are performing and costing across all LLMs.

Reduced Development Time: Less boilerplate code for LLM integration.

By providing clear documentation and potentially client libraries, you significantly lower the barrier to entry and highlight the robust capabilities of your LLM Cost-Performance Optimizer.
