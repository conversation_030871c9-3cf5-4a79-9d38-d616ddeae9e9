# LLM Cost-Performance Optimizer Architecture

## System Overview

The LLM Cost-Performance Optimizer is an intelligent proxy system that sits in front of various Large Language Model (LLM) providers, optimizing for cost and performance. It makes real-time decisions about which LLM backend to route requests to based on dynamic data, user preferences, and configurable policies.

## Core Components

The system uses a microservices architecture deployed on Kubernetes with these key components:

### 1. Proxy Gateway (Entry Point)

**Purpose**: Acts as the primary entry point for all LLM requests.

**Key Functions**:
- Intercepts incoming LLM requests (e.g., chat completions)
- Communicates with the AI Optimizer for intelligent routing decisions
- Maintains conversation context across multiple turns in Redis
- Applies user-controlled routing overrides via headers (X-Preferred-Backend-ID)
- Handles request/response transformation between different LLM APIs
- Proxies requests to the chosen LLM backend with appropriate authentication
- Implements response caching for frequently asked queries
- Calculates tokens and costs for each request
- Publishes comprehensive inference logs to Kafka for analysis

**Technical Details**:
- Exposes a unified API endpoint (/v1/chat/completions)
- Compatible with OpenAI, Google, and Anthropic request formats
- Handles both streaming and non-streaming responses
- Processes conversation_id for maintaining multi-turn context

### 2. AI Optimizer (Intelligence Hub)

**Purpose**: Makes intelligent routing decisions based on request characteristics and system performance.

**Key Functions**:
- Classifies incoming prompts into task types (e.g., code_generation, factual_query)
- Uses a tiered classification system with primary, secondary, and fallback classifiers
- Evaluates routing policies against requests with priority-based processing
- Applies user preferences via X-Preferred-LLM-ID header
- Performs dynamic routing optimization based on latency or cost metrics
- Maintains an optimal backend selection in Redis
- Implements a sophisticated fallback mechanism when preferred backends are unavailable
- Caches routing decisions to improve response time

**Technical Details**:
- Exposes a /route endpoint for the proxy gateway
- Supports different optimization preferences: cost_priority, latency_priority, balanced
- Monitors real-time backend performance metrics
- Health-checks classifier availability on startup

### 3. Policy Manager (Configuration Service)

**Purpose**: Manages routing policies and model profiles.

**Key Functions**:
- Stores and retrieves routing policies
- Maintains detailed model profiles with performance characteristics
- Notifies other services about configuration updates via Redis Pub/Sub

**Technical Details**:
- Manages model API keys securely
- Supports multiple policy types (ROUTE, OPTIMIZE, BLOCK, ALLOW_ACCESS, DENY_ACCESS)
- Handles policy criteria based on user roles, task type, capabilities, etc.

### 4. Data Processor (Metrics Collection & Enrichment)

**Purpose**: Processes inference logs and maintains performance metrics.

**Key Functions**:
- Consumes raw inference logs from Kafka
- Enriches logs with system-level metrics
- Calculates cost information based on token usage
- Persists enriched data to ClickHouse for historical analysis

**Technical Details**:
- Processes metrics like latency, token counts, and costs
- Maintains historical performance data for optimization

### 5. Dashboard API and Frontend

**Purpose**: Provides administrative interface and visualization.

**Key Functions**:
- Displays real-time performance metrics
- Allows policy and model profile management
- Shows usage patterns and cost information

**Technical Details**:
- React-based frontend with interactive components
- Visualizes data from ClickHouse via the dashboard API

## Supporting Infrastructure

- **Redis**: Used for:
  - Caching policy and model profile information
  - Storing conversation history for multi-turn interactions
  - Caching LLM responses for frequently asked queries
  - Publishing optimal backend IDs
  - Redis Pub/Sub for configuration updates

- **ClickHouse**: Columnar database for:
  - Storing enriched inference logs
  - Supporting analytical queries for dashboard
  - Maintaining historical performance data

- **Kafka**: Message broker for:
  - Reliable communication of inference logs
  - Asynchronous data processing

- **Prometheus**: Monitoring system for:
  - Collecting system-level metrics
  - Monitoring service health

## Data Flow

1. Client sends an LLM request to the proxy-gateway
2. Proxy checks response cache (if applicable)
3. If cache miss, proxy requests routing decision from ai-optimizer
4. AI Optimizer classifies the prompt and evaluates policies
5. Proxy forwards request to selected backend with appropriate auth
6. Response is returned to client and cached if appropriate
7. Inference logs are published to Kafka
8. Data processor enriches logs and stores in ClickHouse
9. Metrics inform future optimization decisions

## Advanced Features

- **Prompt Classification**: LLM-based classification of requests into categories for specialized handling
- **Semantic Routing**: Understanding the meaning of prompts for context-aware routing
- **Conversational Context Management**: Maintaining history across multiple turns
- **Dynamic Routing Preferences**: Adjustable optimization objectives (cost vs. latency)
- **Response Caching**: Storing responses for frequent queries to reduce costs and latency
- **Multi-Provider Support**: Compatible with OpenAI, Google, Anthropic, and custom backends
- **User-Controlled Routing**: Application developers can explicitly override routing decisions
- **Model Status Awareness**: System understands active vs. inactive models for reliable routing
- **Multi-Tier Fallback Mechanisms**: Graceful degradation when preferred models are unavailable

## Routing Strategies

The AI Optimizer supports different routing strategies to determine the optimal backend for a request.

### 1. Default

Routes to a specific model based on the `ModelPriorities` list.

### 2. Cascade

Tries models in the `ModelPriorities` list in order, until a suitable model is found.

### 3. Parallel

Sends the request to multiple models in parallel and compares the results using the `ComparisonMethod`.

### 4. Hybrid

Combines different routing strategies based on the request characteristics.

The routing strategy is defined in the `RoutingStrategy` struct in `k8s/ai-optimizer/main.go`:

```go
type RoutingStrategy struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Strategy          string                     `json:"strategy"` // ROUTING_STRATEGY_* constants
	ModelRequirements ModelCapabilityRequirement `json:"model_requirements,omitempty"`
	ModelPriorities   []string                   `json:"model_priorities,omitempty"`  // For cascade routing
	ParallelModels    []string                   `json:"parallel_models,omitempty"`   // For parallel routing
	ComparisonMethod  string                     `json:"comparison_method,omitempty"` // For parallel routing
	EnableFallback    bool                       `json:"enable_fallback"`             // Whether to use fallback routing
	FallbackModelID   string                     `json:"fallback_model_id,omitempty"` // Specific fallback model ID
	PolicyID          string                     `json:"policy_id,omitempty"`
	Priority          int                        `json:"priority"`
	TaskType          string                     `json:"task_type,omitempty"`
}
```

## Policy Structure

The Policy struct defines the routing policies and includes fields for Role-Based Access Control (RBAC):

```go
type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`   // JSON string of conditions
	Action       string          `json:"action"`     // e.g., "ROUTE", "OPTIMIZE", "BLOCK"
	BackendID    string          `json:"backend_id"` // Specific backend ID if action is ROUTE
	Priority     int             `json:"priority"`   // Order of evaluation
	Rules        json.RawMessage `json:"rules"`      // JSON string of task-specific rules or parameters for OPTIMIZE
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata"`      // Additional arbitrary metadata
	RateLimit    int             `json:"rate_limit"`    // For rate limiting policies (e.g., requests per minute)
	Budget       float64         `json:"budget"`        // For cost budgeting policies (e.g., max USD per hour)
	Effect       string          `json:"effect"`        // "ALLOW" or "DENY" for RBAC
	Subjects     []string        `json:"subjects"`      // Users or roles
	ResourceType string          `json:"resource_type"` // e.g., "model", "endpoint"
	ResourceIDs  []string        `json:"resource_ids"`  // Specific resource IDs or "*"
	Permissions  []string        `json:"permissions"`   // Actions allowed/denied
	Status       string          `json:"status"`        // "active", "inactive", etc.
}
```

*   **Effect**: "ALLOW" or "DENY" for RBAC.
*   **Subjects**: List of user IDs, role IDs, or group IDs.
*   **ResourceType**: Type of resource being controlled (e.g., "model", "endpoint").
*   **ResourceIDs**: List of specific resource IDs (e.g., model IDs) or "\*" for all.
*   **Permissions**: List of actions allowed or denied (e.g., "read", "write", "execute").

## Semantic Cache

The proxy gateway implements a semantic cache to store LLM responses for similar queries.

1.  **Embedding Generation**: The proxy generates an embedding vector for each incoming prompt using an embedding model.
2.  **Similarity Search**: The proxy searches the cache for prompts with similar embeddings.
3.  **Response Retrieval**: If a similar prompt is found in the cache, the proxy returns the cached response.

The semantic cache uses cosine similarity to measure the similarity between embeddings.
