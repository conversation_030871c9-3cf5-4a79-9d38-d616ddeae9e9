# Use a specific, stable Go builder image that meets the go.mod requirement
# Corrected 'as' to 'AS' for casing consistency
FROM golang:1.24 AS builder

# Set the working directory inside the container
WORKDIR /app

# --- DEBUGGING START (Builder Stage) ---
RUN echo "--- DEBUG (Builder Stage): Contents of /app before copying go.mod/go.sum ---" && ls -la /app
# --- DEBUGGING END (Builder Stage) ---

# Copy the Go module files first (for caching)
# This layer is cached as long as go.mod/go.sum don't change
COPY go.mod go.sum ./

# --- DEBUGGING START (Builder Stage) ---
RUN echo "--- DEBUG (Builder Stage): Contents of /app after copying go.mod/go.sum ---" && ls -la /app
# --- DEBUGGING END (Builder Stage) ---

# Clean the Go module cache to ensure a fresh download of dependencies
RUN go clean --modcache

# Download the Go module dependencies specified in go.mod
# This step resolves and caches dependencies
RUN go mod download

# --- DEBUGGING START (Builder Stage) ---
RUN echo "--- DEBUG (Builder Stage): Contents of /app after go mod download ---" && ls -la /app
# --- DEBUGGING END (Builder Stage) ---

# Copy the rest of your application source code
# This invalidates the cache from this point forward if source code changes
COPY . .

# --- DEBUGGING START (Builder Stage) ---
RUN echo "--- DEBUG (Builder Stage): Contents of /app after copying source code ---" && ls -la /app
# --- DEBUGGING END (Builder Stage) ---

# Ensure go.mod/go.sum are up-to-date with the source code imports
# This adds any missing dependencies based on imports and removes unused ones
RUN go mod tidy

# --- DEBUGGING START (Builder Stage) ---
RUN echo "--- DEBUG (Builder Stage): Contents of /app after go mod tidy ---" && ls -la /app
# --- DEBUGGING END (Builder Stage) ---

# Build the Go application binary
# CGO_ENABLED=0 is important for building statically linked binaries
# for minimal base images like Alpine
# -o data_processor specifies the output file name
# -ldflags="-extldflags=-static" ensures static linking for musl libc
RUN CGO_ENABLED=0 go build -o data_processor -ldflags='-extldflags="-static"' .

# Use a minimal Alpine Linux image for the final stage
FROM alpine:latest

# Set the working directory in the final image
WORKDIR /app

# Install curl (and netcat, which is already there, but good to ensure)
RUN apk update && apk add --no-cache curl netcat-openbsd

# Copy the built binary from the builder stage to the final image
COPY --from=builder /app/data_processor .

# Ensure the binary has execute permissions
RUN chmod +x /app/data_processor

# --- DEBUGGING START (Final Stage) ---
RUN echo "--- DEBUG (Final Stage): Contents of /app ---" && ls -la /app
# --- DEBUGGING END (Final Stage) ---

# Define the default command to run when the container starts
# This executes your compiled data processor application
CMD ["/app/data_processor"]

