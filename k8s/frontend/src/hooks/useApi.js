import { useCallback, useState } from 'react';

// API prefixes
// These prefixes are relative to window.location.origin, relying on proxying in dev/ingress in prod.
export const DASHBOARD_API_PREFIX = '/api';
export const POLICY_MANAGER_PREFIX = '/api';

// Create the hook
const useApi = () => {
  const [apiLoading, setApiLoading] = useState(false);
  const [apiError, setApiError] = useState(null);

  // Default options for fetch
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    credentials: 'same-origin'
  };

  const callApi = useCallback(async (endpoint, options = {}) => {
    console.log(`[API] Calling: ${endpoint}`);
    setApiLoading(true);
    setApiError(null); // Clear previous errors
    
    try {
      // Log the full URL being called
      const fullUrl = endpoint.startsWith('http') 
        ? endpoint 
        : new URL(endpoint, window.location.origin).toString();
      console.log(`[API] Full URL: ${fullUrl}`);
      
      // Make the API call
      const response = await fetch(fullUrl, {
        ...defaultOptions,
        ...options,
        headers: {
          ...defaultOptions.headers,
          ...options.headers,
          'Cache-Control': 'no-cache', // Ensure fresh data
          'X-Debug-Timestamp': Date.now().toString() // Useful for debugging proxies
        }
      });
      
      console.log(`[API] Response status: ${response.status} for ${endpoint}`);
      
      // Process response
      if (response.ok) {
        const contentType = response.headers.get('content-type');
        
        // Attempt to parse as JSON if content-type suggests JSON, or if it's text/plain but valid JSON
        if (contentType && contentType.includes('application/json') || 
            (contentType && contentType.includes('text/plain') && response.headers.get('x-is-json') === 'true')) { // Added a custom header check as a fallback
            try {
                const data = await response.json();
                console.log(`[API] Response data for ${endpoint}:`, data);
                return data;
            } catch (parseError) {
                // If parsing fails, log and return raw text as a fallback
                const text = await response.text();
                console.warn(`[API] Failed to parse JSON for ${endpoint} despite Content-Type. Returning raw text. Error:`, parseError);
                return text;
            }
        } else {
            // For other content types or if no content type, try to parse as JSON first
            // This is the key change for handling backend responses that are JSON but mis-headed
            try {
                const data = await response.json();
                console.log(`[API] Parsed potential JSON from non-application/json response for ${endpoint}:`, data);
                return data;
            } catch (e) {
                // If it's not JSON, return the raw text
                const text = await response.text();
                console.log(`[API] Non-JSON or unparsable JSON response from ${endpoint}: ${text}`);
                return text;
            }
        }
      } else {
        console.error(`[API] Error response ${response.status} for ${endpoint}`);
        let errorText = 'Unknown error';
        try {
          errorText = await response.text() || `HTTP Error ${response.status}`;
        } catch (e) {
          errorText = `HTTP Error ${response.status}`;
        }
        console.error(`[API] Error details: ${errorText}`);
        const errorMessage = `API error: ${response.status} - ${errorText}`;
        setApiError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`[API] Call failed for ${endpoint}:`, error);
      const errorMessage = error?.message || 'Unknown API error';
      setApiError(errorMessage);
      throw error;
    } finally {
      setApiLoading(false);
    }
  }, []);

  return {
    callApi,
    apiLoading,
    apiError,
    setApiLoading,
    setApiError
  };
};

// Export both as default and named export for backward compatibility
export default useApi;
export { useApi };
