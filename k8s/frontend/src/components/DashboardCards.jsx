import React from 'react';

/**
 * @typedef {Object} TabButtonProps
 * @property {string} name - The name of the tab.
 * @property {React.ReactNode} icon - Icon component to display.
 * @property {string} activeTab - The currently active tab.
 * @property {(tabName: string) => void} setActiveTab - Function to set the active tab.
 * @property {string} label - Text label for the button.
 */

/**
 * Tab button component for main navigation.
 * @param {TabButtonProps} props
 */
export const TabButton = ({ name, icon, activeTab, setActiveTab, label }) => (
  <button
    onClick={() => setActiveTab(name)}
    className={`flex items-center space-x-2 px-5 py-2 rounded-lg text-lg font-medium transition-all duration-300 ease-in-out
      ${activeTab === name
        ? 'bg-white text-indigo-700 shadow-md transform scale-105'
        : 'text-gray-300 hover:text-white hover:bg-indigo-600'
      }`}
  >
    {icon}
    <span>{label}</span>
  </button>
);

/**
 * @typedef {Object} MetricCardProps
 * @property {React.ReactNode} icon - Icon component for the card.
 * @property {string} title - Title of the metric.
 * @property {string} value - The value of the metric.
 * @property {string} description - A brief description of the metric.
 */

/**
 * Metric card component for displaying key statistics.
 * @param {MetricCardProps} props
 */
export const MetricCard = ({ icon, title, value, description }) => (
  <div className="bg-white p-6 rounded-lg shadow-md flex-none w-[220px] min-w-[220px]">
    <div className="flex flex-col items-center justify-center h-full text-center">
      <div className="flex-shrink-0 p-3 rounded-full bg-gray-100 mb-2">
        {icon}
      </div>
      <div className="flex flex-col items-center">
        <h3 className="text-lg font-semibold text-gray-600 mb-1">{title}</h3>
        <p className="text-3xl font-bold text-gray-900 mt-1 mb-1">{value}</p>
        <p className="text-sm text-gray-500 max-w-full overflow-hidden text-ellipsis px-2">{description}</p>
      </div>
    </div>
  </div>
);
