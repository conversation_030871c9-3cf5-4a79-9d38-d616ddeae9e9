<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2195.984375 1138.4315185546875" style="max-width: 2195.984375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758"><style>#mermaid-27db37ce-e353-406c-be17-72b3d6721758{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .error-icon{fill:#a44141;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-thickness-normal{stroke-width:1px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .marker.cross{stroke:lightgrey;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 p{margin:0;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster-label text{fill:#F9FFFE;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster-label span{color:#F9FFFE;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster-label span p{background-color:transparent;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .label text,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 span{fill:#ccc;color:#ccc;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node rect,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node circle,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node ellipse,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node polygon,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .rough-node .label text,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node .label text,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .image-shape .label,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .icon-shape .label{text-anchor:middle;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .rough-node .label,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node .label,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .image-shape .label,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .icon-shape .label{text-align:center;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .node.clickable{cursor:pointer;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .arrowheadPath{fill:lightgrey;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster text{fill:#F9FFFE;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .cluster span{color:#F9FFFE;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 rect.text{fill:none;stroke-width:0;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .icon-shape,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .icon-shape p,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .icon-shape rect,#mermaid-27db37ce-e353-406c-be17-72b3d6721758 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-27db37ce-e353-406c-be17-72b3d6721758 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph6" class="cluster"><rect height="122.43148040771484" width="595.9453125" y="854" x="8" style=""></rect><g transform="translate(256.00390625, 854)" class="cluster-label"><foreignObject height="24" width="99.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Tools</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="122.43148040771484" width="418.3828125" y="854" x="623.9453125" style=""></rect><g transform="translate(794.5703125, 854)" class="cluster-label"><foreignObject height="24" width="77.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="104" width="491.5234375" y="1026.4314804077148" x="1696.4609375" style=""></rect><g transform="translate(1892.87890625, 1026.4314804077148)" class="cluster-label"><foreignObject height="24" width="98.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM Backends</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="104" width="458.150390625" y="676" x="577.296875" style=""></rect><g transform="translate(728.2666015625, 676)" class="cluster-label"><foreignObject height="24" width="156.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Existing Core Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="557.4314804077148" width="523.44921875" y="419" x="1089.869140625" style=""></rect><g transform="translate(1292.90625, 419)" class="cluster-label"><foreignObject height="24" width="117.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Planning Service</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="814.4314804077148" width="507.751953125" y="162" x="1633.318359375" style=""></rect><g transform="translate(1820.0458984375, 162)" class="cluster-label"><foreignObject height="24" width="134.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Gateway Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="104" width="283.1640625" y="8" x="1737.7265625" style=""></rect><g transform="translate(1828.2734375, 8)" class="cluster-label"><foreignObject height="24" width="102.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Interface</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PG_PS_0" d="M1846.216,241L1838.658,247.167C1831.1,253.333,1815.984,265.667,1808.425,277.333C1800.867,289,1800.867,300,1800.867,305.5L1800.867,311"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PG_AO_1" d="M1950.542,241L1966.811,247.167C1983.081,253.333,2015.619,265.667,2031.889,282.5C2048.158,299.333,2048.158,320.667,2048.158,340C2048.158,359.333,2048.158,376.667,2048.158,389.5C2048.158,402.333,2048.158,410.667,2048.158,423.5C2048.158,436.333,2048.158,453.667,2048.158,473C2048.158,492.333,2048.158,513.667,2048.158,535C2048.158,556.333,2048.158,577.667,2048.158,597C2048.158,616.333,2048.158,633.667,2048.158,646.5C2048.158,659.333,2048.158,667.667,2048.158,680.5C2048.158,693.333,2048.158,710.667,2048.158,728C2048.158,745.333,2048.158,762.667,2048.158,777.5C2048.158,792.333,2048.158,804.667,2048.158,817C2048.158,829.333,2048.158,841.667,2038.456,853.213C2028.754,864.759,2009.35,875.517,1999.647,880.897L1989.945,886.276"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_GP_2" d="M1740.79,369L1731.518,373.167C1722.247,377.333,1703.705,385.667,1694.433,394C1685.162,402.333,1685.162,410.667,1638.866,421.662C1592.569,432.658,1499.976,446.317,1453.679,453.146L1407.383,459.975"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GP_TDE_3" d="M1332.641,498L1332.641,504.167C1332.641,510.333,1332.641,522.667,1332.641,534.333C1332.641,546,1332.641,557,1332.641,562.5L1332.641,568"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TDE_EO_4" d="M1286.857,626L1279.792,630.167C1272.726,634.333,1258.596,642.667,1251.53,651C1244.465,659.333,1244.465,667.667,1244.465,675.333C1244.465,683,1244.465,690,1244.465,693.5L1244.465,697"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_SM_5" d="M1301.409,755L1310.196,759.167C1318.984,763.333,1336.559,771.667,1345.347,782C1354.135,792.333,1354.135,804.667,1354.135,817C1354.135,829.333,1354.135,841.667,1354.135,852.869C1354.135,864.072,1354.135,874.144,1354.135,879.18L1354.135,884.216"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_PG_6" d="M1879.309,87L1879.309,91.167C1879.309,95.333,1879.309,103.667,1879.309,112C1879.309,120.333,1879.309,128.667,1879.309,137C1879.309,145.333,1879.309,153.667,1879.309,161.333C1879.309,169,1879.309,176,1879.309,179.5L1879.309,183"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_AO_7" d="M1868.038,369L1878.403,373.167C1888.769,377.333,1909.501,385.667,1919.867,394C1930.232,402.333,1930.232,410.667,1930.232,423.5C1930.232,436.333,1930.232,453.667,1930.232,473C1930.232,492.333,1930.232,513.667,1930.232,535C1930.232,556.333,1930.232,577.667,1930.232,597C1930.232,616.333,1930.232,633.667,1930.232,646.5C1930.232,659.333,1930.232,667.667,1930.232,680.5C1930.232,693.333,1930.232,710.667,1930.232,728C1930.232,745.333,1930.232,762.667,1930.232,777.5C1930.232,792.333,1930.232,804.667,1930.232,817C1930.232,829.333,1930.232,841.667,1930.851,852.874C1931.471,864.082,1932.709,874.164,1933.328,879.205L1933.947,884.246"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_PM_8" d="M1751.174,369L1743.505,373.167C1735.837,377.333,1720.499,385.667,1712.831,394C1705.162,402.333,1705.162,410.667,1705.162,423.5C1705.162,436.333,1705.162,453.667,1705.162,473C1705.162,492.333,1705.162,513.667,1705.162,535C1705.162,556.333,1705.162,577.667,1705.162,597C1705.162,616.333,1705.162,633.667,1705.162,646.5C1705.162,659.333,1705.162,667.667,1551.443,679.751C1397.723,691.835,1090.285,707.67,936.566,715.587L782.846,723.505"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TDE_AO_9" d="M1419.236,626L1432.599,630.167C1445.962,634.333,1472.689,642.667,1486.053,651C1499.416,659.333,1499.416,667.667,1499.416,680.5C1499.416,693.333,1499.416,710.667,1499.416,728C1499.416,745.333,1499.416,762.667,1499.416,777.5C1499.416,792.333,1499.416,804.667,1499.416,817C1499.416,829.333,1499.416,841.667,1559.337,856.202C1619.258,870.737,1739.099,887.473,1799.02,895.841L1858.941,904.21"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_AO_10" d="M1339.126,755L1353.734,759.167C1368.343,763.333,1397.559,771.667,1412.167,782C1426.775,792.333,1426.775,804.667,1426.775,817C1426.775,829.333,1426.775,841.667,1498.801,856.462C1570.827,871.258,1714.879,888.515,1786.905,897.144L1858.931,905.773"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_API_11" d="M1200.847,755L1194.116,759.167C1187.385,763.333,1173.923,771.667,1167.192,782C1160.461,792.333,1160.461,804.667,1160.461,817C1160.461,829.333,1160.461,841.667,1000.378,857.247C840.295,872.828,520.128,891.655,360.045,901.069L199.962,910.483"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_DB_12" d="M1211.232,755L1206.104,759.167C1200.975,763.333,1190.718,771.667,1185.589,782C1180.461,792.333,1180.461,804.667,1180.461,817C1180.461,829.333,1180.461,841.667,1047.372,857.214C914.283,872.761,648.105,891.522,515.016,900.903L381.928,910.284"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_Tools_13" d="M1221.617,755L1218.091,759.167C1214.565,763.333,1207.513,771.667,1203.987,782C1200.461,792.333,1200.461,804.667,1200.461,817C1200.461,829.333,1200.461,841.667,1095.872,856.953C991.284,872.24,782.107,890.48,677.519,899.6L572.93,908.72"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AO_LLM1_14" d="M1869.203,942.216L1854.725,947.918C1840.247,953.621,1811.291,965.026,1796.814,974.896C1782.336,984.765,1782.336,993.098,1782.336,1001.431C1782.336,1009.765,1782.336,1018.098,1782.336,1025.765C1782.336,1033.431,1782.336,1040.431,1782.336,1043.931L1782.336,1047.431"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AO_LLM2_15" d="M1937.75,942.216L1937.75,947.918C1937.75,953.621,1937.75,965.026,1937.75,974.896C1937.75,984.765,1937.75,993.098,1937.75,1001.431C1937.75,1009.765,1937.75,1018.098,1937.75,1025.765C1937.75,1033.431,1937.75,1040.431,1937.75,1043.931L1937.75,1047.431"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AO_LLM3_16" d="M2008.27,942.216L2023.165,947.918C2038.059,953.621,2067.848,965.026,2082.742,974.896C2097.637,984.765,2097.637,993.098,2097.637,1001.431C2097.637,1009.765,2097.637,1018.098,2097.637,1025.765C2097.637,1033.431,2097.637,1040.431,2097.637,1043.931L2097.637,1047.431"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_Redis_17" d="M1761.559,369L1755.493,373.167C1749.427,377.333,1737.294,385.667,1731.228,394C1725.162,402.333,1725.162,410.667,1725.162,423.5C1725.162,436.333,1725.162,453.667,1725.162,473C1725.162,492.333,1725.162,513.667,1725.162,535C1725.162,556.333,1725.162,577.667,1725.162,597C1725.162,616.333,1725.162,633.667,1725.162,646.5C1725.162,659.333,1725.162,667.667,1725.162,680.5C1725.162,693.333,1725.162,710.667,1725.162,728C1725.162,745.333,1725.162,762.667,1725.162,777.5C1725.162,792.333,1725.162,804.667,1725.162,817C1725.162,829.333,1725.162,841.667,1564.88,857.491C1404.598,873.315,1084.034,892.631,923.751,902.289L763.469,911.946"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_CH_18" d="M1783.285,369L1780.572,373.167C1777.859,377.333,1772.432,385.667,1769.719,394C1767.006,402.333,1767.006,410.667,1767.006,423.5C1767.006,436.333,1767.006,453.667,1767.006,473C1767.006,492.333,1767.006,513.667,1767.006,535C1767.006,556.333,1767.006,577.667,1767.006,597C1767.006,616.333,1767.006,633.667,1767.006,646.5C1767.006,659.333,1767.006,667.667,1767.006,680.5C1767.006,693.333,1767.006,710.667,1767.006,728C1767.006,745.333,1767.006,762.667,1767.006,777.5C1767.006,792.333,1767.006,804.667,1767.006,817C1767.006,829.333,1767.006,841.667,1623.557,857.471C1480.108,873.276,1193.209,892.552,1049.76,902.19L906.311,911.829"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DP_CH_19" d="M882.6,755L878.15,759.167C873.699,763.333,864.799,771.667,860.349,782C855.898,792.333,855.898,804.667,855.898,817C855.898,829.333,855.898,841.667,855.898,851.456C855.898,861.244,855.898,868.489,855.898,872.111L855.898,875.733"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DP_Kafka_20" d="M940.108,755L944.532,759.167C948.957,763.333,957.805,771.667,962.23,782C966.654,792.333,966.654,804.667,966.654,817C966.654,829.333,966.654,841.667,967.6,852.262C968.546,862.857,970.437,871.715,971.383,876.143L972.328,880.572"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PS_Kafka_21" d="M1818.822,369L1821.593,373.167C1824.364,377.333,1829.906,385.667,1832.676,394C1835.447,402.333,1835.447,410.667,1835.447,423.5C1835.447,436.333,1835.447,453.667,1835.447,473C1835.447,492.333,1835.447,513.667,1835.447,535C1835.447,556.333,1835.447,577.667,1835.447,597C1835.447,616.333,1835.447,633.667,1835.447,646.5C1835.447,659.333,1835.447,667.667,1835.447,680.5C1835.447,693.333,1835.447,710.667,1835.447,728C1835.447,745.333,1835.447,762.667,1835.447,777.5C1835.447,792.333,1835.447,804.667,1835.447,817C1835.447,829.333,1835.447,841.667,1698.092,857.66C1560.737,873.654,1286.028,893.308,1148.673,903.135L1011.318,912.963"></path><path marker-end="url(#mermaid-27db37ce-e353-406c-be17-72b3d6721758_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EO_Kafka_22" d="M1263.446,755L1266.375,759.167C1269.305,763.333,1275.163,771.667,1278.092,782C1281.021,792.333,1281.021,804.667,1281.021,817C1281.021,829.333,1281.021,841.667,1236.059,856.972C1191.097,872.276,1101.172,890.553,1056.21,899.691L1011.248,908.829"></path></g><g class="edgeLabels"><g transform="translate(1800.8671875, 278)" class="edgeLabel"><g transform="translate(-57.6953125, -12)" class="label"><foreignObject height="24" width="115.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Goal Submission</p></span></div></foreignObject></g></g><g transform="translate(2048.158203125, 535)" class="edgeLabel"><g transform="translate(-47.72265625, -12)" class="label"><foreignObject height="24" width="95.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>LLM Requests</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1499.416015625, 728)" class="edgeLabel"><g transform="translate(-46.62109375, -12)" class="label"><foreignObject height="24" width="93.2421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>LLM Planning</p></span></div></foreignObject></g></g><g transform="translate(1426.775390625, 817)" class="edgeLabel"><g transform="translate(-52.640625, -12)" class="label"><foreignObject height="24" width="105.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Task Execution</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1835.447265625, 599)" class="edgeLabel"><g transform="translate(-48.44140625, -12)" class="label"><foreignObject height="24" width="96.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Planning Logs</p></span></div></foreignObject></g></g><g transform="translate(1281.021484375, 817)" class="edgeLabel"><g transform="translate(-53.11328125, -12)" class="label"><foreignObject height="24" width="106.2265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Execution Logs</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1879.30859375, 60)" id="flowchart-UI-0" class="node default"><rect height="54" width="213.1640625" y="-27" x="-106.58203125" style="" class="basic label-container"></rect><g transform="translate(-76.58203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="153.1640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Dashboard/API Client</p></span></div></foreignObject></g></g><g transform="translate(1879.30859375, 214)" id="flowchart-PG-1" class="node default"><rect height="54" width="166.203125" y="-27" x="-83.1015625" style="" class="basic label-container"></rect><g transform="translate(-53.1015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="106.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Proxy Gateway</p></span></div></foreignObject></g></g><g transform="translate(1800.8671875, 342)" id="flowchart-PS-3" class="node default"><rect height="54" width="177.375" y="-27" x="-88.6875" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-58.6875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Planning Service</p></span></div></foreignObject></g></g><g transform="translate(1937.75, 915.2157402038574)" id="flowchart-AO-5" class="node default"><rect height="54" width="149.6953125" y="-27" x="-74.84765625" style="" class="basic label-container"></rect><g transform="translate(-44.84765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="89.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI Optimizer</p></span></div></foreignObject></g></g><g transform="translate(1332.640625, 471)" id="flowchart-GP-7" class="node default"><rect height="54" width="141.5703125" y="-27" x="-70.78515625" style="" class="basic label-container"></rect><g transform="translate(-40.78515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.5703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Goal Parser</p></span></div></foreignObject></g></g><g transform="translate(1332.640625, 599)" id="flowchart-TDE-8" class="node default"><rect height="54" width="252.3671875" y="-27" x="-126.18359375" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-96.18359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="192.3671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Decomposition Engine</p></span></div></foreignObject></g></g><g transform="translate(1244.46484375, 728)" id="flowchart-EO-9" class="node default"><rect height="54" width="226.015625" y="-27" x="-113.0078125" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-83.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="166.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Execution Orchestrator</p></span></div></foreignObject></g></g><g transform="translate(1354.134765625, 915.2157402038574)" id="flowchart-SM-10" class="node default"><rect height="54" width="162.21875" y="-27" x="-81.109375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-51.109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>State Manager</p></span></div></foreignObject></g></g><g transform="translate(695.57421875, 728)" id="flowchart-PM-20" class="node default"><rect height="54" width="166.5546875" y="-27" x="-83.27734375" style="" class="basic label-container"></rect><g transform="translate(-53.27734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="106.5546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Policy Manager</p></span></div></foreignObject></g></g><g transform="translate(911.4375, 728)" id="flowchart-DP-21" class="node default"><rect height="54" width="165.171875" y="-27" x="-82.5859375" style="" class="basic label-container"></rect><g transform="translate(-52.5859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="105.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Processor</p></span></div></foreignObject></g></g><g transform="translate(1782.3359375, 1078.4314804077148)" id="flowchart-LLM1-22" class="node default"><rect height="54" width="101.75" y="-27" x="-50.875" style="" class="basic label-container"></rect><g transform="translate(-20.875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="41.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GPT-4</p></span></div></foreignObject></g></g><g transform="translate(1937.75, 1078.4314804077148)" id="flowchart-LLM2-23" class="node default"><rect height="54" width="109.078125" y="-27" x="-54.5390625" style="" class="basic label-container"></rect><g transform="translate(-24.5390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="49.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Claude</p></span></div></foreignObject></g></g><g transform="translate(2097.63671875, 1078.4314804077148)" id="flowchart-LLM3-24" class="node default"><rect height="54" width="110.6953125" y="-27" x="-55.34765625" style="" class="basic label-container"></rect><g transform="translate(-25.34765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="50.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Gemini</p></span></div></foreignObject></g></g><g transform="translate(709.2109375, 915.2157402038574)" id="flowchart-Redis-25" class="node default"><path transform="translate(-50.265625, -36.21574061244284)" style="" class="basic label-container" d="M0,11.143827074961894 a50.265625,11.143827074961894 0,0,0 100.53125,0 a50.265625,11.143827074961894 0,0,0 -100.53125,0 l0,50.14382707496189 a50.265625,11.143827074961894 0,0,0 100.53125,0 l0,-50.14382707496189"></path><g transform="translate(-42.765625, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="85.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis Cache</p></span></div></foreignObject></g></g><g transform="translate(855.8984375, 915.2157402038574)" id="flowchart-CH-26" class="node default"><path transform="translate(-46.421875, -35.48228374695166)" style="" class="basic label-container" d="M0,10.654855831301104 a46.421875,10.654855831301104 0,0,0 92.84375,0 a46.421875,10.654855831301104 0,0,0 -92.84375,0 l0,49.654855831301106 a46.421875,10.654855831301104 0,0,0 92.84375,0 l0,-49.654855831301106"></path><g transform="translate(-38.921875, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="77.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ClickHouse</p></span></div></foreignObject></g></g><g transform="translate(979.82421875, 915.2157402038574)" id="flowchart-Kafka-27" class="node default"><path transform="translate(-27.50390625, -30.95946356494944)" style="" class="basic label-container" d="M0,7.639642376632959 a27.50390625,7.639642376632959 0,0,0 55.0078125,0 a27.50390625,7.639642376632959 0,0,0 -55.0078125,0 l0,46.63964237663296 a27.50390625,7.639642376632959 0,0,0 55.0078125,0 l0,-46.63964237663296"></path><g transform="translate(-20.00390625, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="40.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka</p></span></div></foreignObject></g></g><g transform="translate(119.484375, 915.2157402038574)" id="flowchart-API-28" class="node default"><rect height="54" width="152.96875" y="-27" x="-76.484375" style="" class="basic label-container"></rect><g transform="translate(-46.484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="92.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External APIs</p></span></div></foreignObject></g></g><g transform="translate(311.953125, 915.2157402038574)" id="flowchart-DB-29" class="node default"><rect height="54" width="131.96875" y="-27" x="-65.984375" style="" class="basic label-container"></rect><g transform="translate(-35.984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="71.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Databases</p></span></div></foreignObject></g></g><g transform="translate(498.44140625, 915.2157402038574)" id="flowchart-Tools-30" class="node default"><rect height="54" width="141.0078125" y="-27" x="-70.50390625" style="" class="basic label-container"></rect><g transform="translate(-40.50390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Other Tools</p></span></div></foreignObject></g></g></g></g></g></svg>