# proxy-gateway-job-reader-rbac.yaml
# Kubernetes RBAC definitions for the Proxy Gateway's init container
# to read Kafka topic creator Job status.

apiVersion: v1
kind: ServiceAccount
metadata:
  name: proxy-gateway-job-reader-sa # Name of the ServiceAccount
  namespace: default # Must be in the same namespace as the proxy-gateway pod
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role # Use Role for namespace-scoped permissions
metadata:
  name: job-reader-role # Name of the Role
  namespace: default # Must be in the same namespace where the Job exists
rules:
- apiGroups: ["batch"] # The API group for Kubernetes Jobs
  resources: ["jobs"] # The resource type we want to grant access to
  verbs: ["get", "list"] # Permissions: allow getting and listing jobs
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding # Binds the Role to the ServiceAccount
metadata:
  name: proxy-gateway-job-reader-binding # Name of the RoleBinding
  namespace: default # Must be in the same namespace as the Role and ServiceAccount
subjects:
- kind: ServiceAccount
  name: proxy-gateway-job-reader-sa # Reference the ServiceAccount created above
  namespace: default # Must match the ServiceAccount's namespace
roleRef:
  kind: Role
  name: job-reader-role # Reference the Role created above
  apiGroup: rbac.authorization.k8s.io # API group for Role

