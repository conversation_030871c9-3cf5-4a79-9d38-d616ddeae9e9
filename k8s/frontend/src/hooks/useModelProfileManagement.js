import { useState, useCallback } from 'react';
import useApi from './useApi'; // Changed from named import to default import
import { POLICY_MANAGER_API_PREFIX } from '../utils/constants';

/**
 * @typedef {Object} ModelProfileManagementResult
 * @property {(profile: ModelProfile) => Promise<void>} handleCreateModelProfile
 * @property {(profile: ModelProfile) => Promise<void>} handleUpdateModelProfile
 * @property {(profileId: string) => Promise<void>} handleDeleteModelProfile
 * @property {(profileData: ModelProfile) => Object.<string, string>} validateModelProfileForm
 * @property {boolean} loading
 * @property {string | null} error
 */

/**
 * Custom hook for managing LLM Model Profiles.
 * @param {() => Promise<void>} refetchModelProfiles - Function to re-fetch profiles after a CUD operation.
 * @param {(message: string, type?: 'success' | 'error' | 'warning') => void} showNotification - Function to display notifications.
 * @param {(errors: Object.<string, string>) => void} setFormErrors - Function to set form validation errors.
 * @returns {ModelProfileManagementResult}
 */
export const useModelProfileManagement = (refetchModelProfiles, showNotification, setFormErrors) => {
  const { callApi, apiLoading: loading, apiError: error } = useApi();

  /**
   * Validates a model profile form data.
   * @param {ModelProfile} profileData
   * @returns {Object.<string, string>} Validation errors.
   */
  const validateModelProfileForm = useCallback((profileData) => {
    const errors = {};
    if (!profileData.name) errors.name = "Name is required.";
    if (!profileData.provider) errors.provider = "Provider is required.";
    if (!profileData.model_id) errors.model_id = "Model ID is required.";
    if (isNaN(parseFloat(profileData.cost_per_1k_input_tokens)) || parseFloat(profileData.cost_per_1k_input_tokens) < 0) {
      errors.cost_per_1k_input_tokens = "Input token cost must be a non-negative number.";
    }
    if (isNaN(parseFloat(profileData.cost_per_1k_output_tokens)) || parseFloat(profileData.cost_per_1k_output_tokens) < 0) {
      errors.cost_per_1k_output_tokens = "Output token cost must be a non-negative number.";
    }
    setFormErrors(errors); // Update form errors state
    return errors;
  }, [setFormErrors]);

  /**
   * Handles creating a new model profile.
   * @param {ModelProfile} newProfile
   */
  const handleCreateModelProfile = useCallback(async (newProfile) => {
    const validationErrors = validateModelProfileForm(newProfile);
    if (Object.keys(validationErrors).length > 0) {
      showNotification('Please correct the errors in the model profile form.', 'error');
      return;
    }
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/model-profiles`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProfile)
      });
      showNotification('Model profile created successfully!', 'success');
      refetchModelProfiles(); // Refresh the list
      return true; // Indicate success
    } catch (err) {
      console.error("Failed to create model profile:", err);
      // Notification handled by useApi
      return false; // Indicate failure
    }
  }, [callApi, refetchModelProfiles, showNotification, validateModelProfileForm]);

  /**
   * Handles updating an existing model profile.
   * @param {ModelProfile} updatedProfile
   */
  const handleUpdateModelProfile = useCallback(async (updatedProfile) => {
    const validationErrors = validateModelProfileForm(updatedProfile);
    if (Object.keys(validationErrors).length > 0) {
      showNotification('Please correct the errors in the model profile form.', 'error');
      return;
    }
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/model-profiles/${updatedProfile.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedProfile)
      });
      showNotification('Model profile updated successfully!', 'success');
      refetchModelProfiles(); // Refresh the list
      return true;
    } catch (err) {
      console.error("Failed to update model profile:", err);
      // Notification handled by useApi
      return false;
    }
  }, [callApi, refetchModelProfiles, showNotification, validateModelProfileForm]);

  /**
   * Handles deleting a model profile.
   * @param {string} profileId
   */
  const handleDeleteModelProfile = useCallback(async (profileId) => {
    try {
      await callApi(`${POLICY_MANAGER_API_PREFIX}/model-profiles/${profileId}`, {
        method: 'DELETE'
      });
      showNotification('Model profile deleted successfully!', 'success');
      refetchModelProfiles(); // Refresh the list
      return true;
    } catch (err) {
      console.error("Failed to delete model profile:", err);
      // Notification handled by useApi
      return false;
    }
  }, [callApi, refetchModelProfiles, showNotification]);

  return {
    loading,
    error,
    handleCreateModelProfile,
    handleUpdateModelProfile,
    handleDeleteModelProfile,
    validateModelProfileForm,
  };
};
