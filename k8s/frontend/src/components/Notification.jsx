import React from 'react';
import { <PERSON>ertCircle, CheckCircle, Info, X, AlertTriangle } from 'react-feather';

/**
 * Notification component for displaying alerts and messages.
 * 
 * @param {Object} props
 * @param {Object} props.notification - The notification object
 * @param {string} props.notification.message - The notification message
 * @param {'success'|'error'|'warning'|'info'} props.notification.type - The notification type
 * @param {Function} props.onClose - Function to call when closing the notification
 */
const Notification = ({ notification, onClose }) => {
  // If notification is null or doesn't have a message, don't render anything
  if (!notification || !notification.message) {
    return null;
  }

  // Ensure message is a string
  const message = String(notification.message || '');
  
  // Default to info if type is not provided or invalid
  const validTypes = ['success', 'error', 'warning', 'info'];
  const type = validTypes.includes(notification.type) ? notification.type : 'info';
  
  // Define styles based on notification type
  const styles = {
    success: {
      bg: 'bg-green-50',
      border: 'border-green-400',
      text: 'text-green-800',
      icon: <CheckCircle className="h-5 w-5 text-green-400" aria-hidden="true" />,
    },
    error: {
      bg: 'bg-red-50',
      border: 'border-red-400',
      text: 'text-red-800',
      icon: <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />,
    },
    warning: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-400',
      text: 'text-yellow-800',
      icon: <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />,
    },
    info: {
      bg: 'bg-blue-50',
      border: 'border-blue-400',
      text: 'text-blue-800',
      icon: <Info className="h-5 w-5 text-blue-400" aria-hidden="true" />,
    },
  };

  const style = styles[type];

  // Safe onClose handler
  const handleClose = (e) => {
    e.preventDefault();
    if (typeof onClose === 'function') {
      onClose();
    }
  };

  return (
    <div className={`fixed top-4 right-4 z-50 rounded-md ${style.bg} p-4 shadow-md border-l-4 ${style.border} max-w-md`}>
      <div className="flex">
        <div className="flex-shrink-0">
          {style.icon}
        </div>
        <div className="ml-3 flex-1">
          <p className={`text-sm font-medium ${style.text}`}>
            {message}
          </p>
        </div>
        <div className="ml-auto pl-3">
          <div className="-mx-1.5 -my-1.5">
            <button
              type="button"
              onClick={handleClose}
              className={`inline-flex rounded-md p-1.5 ${style.text} hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-gray-600`}
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Notification;
