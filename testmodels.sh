    #!/bin/bash

    # Configuration
    # IMPORTANT: R<PERSON><PERSON>CE THIS WITH YOUR ACTUAL PROXY GATEWAY IP or HOSTNAME and PORT
    # If you are port-forwarding, it might be localhost:8080
    # Example: PROXY_GATEAY_IP="localhost"
    # Example: PROXY_GATEWAY_PORT="8080"
    PROXY_GATEWAY_IP="scale-llm.com" 
    PROXY_GATEWAY_PORT="80"
    ENDPOINT="https://${PROXY_GATEWAY_IP}/v1/chat/completions"

    echo "Using Proxy Gateway Endpoint: ${ENDPOINT}"
    echo "----------------------------------------------------"

    # --- Test Case 1: Basic Factual Query ---
    echo "Test Case 1: Basic Factual Query (should be routed by optimizer)"
    echo "Prompt: What is the capital of France?"
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "gpt-3.5-turbo",
        "messages": [
          {"role": "user", "content": "What is the capital of France?"}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    # --- Test Case 2: Creative Writing Prompt ---
    echo "Test Case 2: Creative Writing Prompt (should be classified as 'creative_writing')"
    echo "Prompt: Write a very short, imaginative story about a cat who can fly."
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "claude-3-haiku",
        "messages": [
          {"role": "user", "content": "Write a very short, imaginative story about a cat who can fly."}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    # --- Test Case 3: Conversation - First Turn (capture conversation_id) ---
    echo "Test Case 3: Conversation - First Turn (A new conversation will be started. Note the conversation_id from proxy-gateway logs.)"
    echo "Prompt: Hi, who are you and what do you do?"
    FIRST_TURN_RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "gpt-3.5-turbo",  # <--- CHANGED FROM "gemini-pro"
        "messages": [
          {"role": "user", "content": "Hi, who are you and what do you do?"}
        ]
      }')
    echo "First Turn Response: ${FIRST_TURN_RESPONSE}"
    echo ""
    echo "################################################################################"
    echo "### IMPORTANT: For Test Case 4, you NEED the 'conversation_id' from the    ###"
    echo "###            proxy-gateway logs for the request above.                   ###"
    echo "###            Look for 'Generated new ID: <UUID>' or 'Created new         ###"
    ###            conversation <UUID>'.                                       ###"
    echo "###            Then, update the 'MANUAL_CONVERSATION_ID' variable below.   ###"
    echo "################################################################################"
    echo ""
    sleep 5


    # --- Test Case 4: Conversation - Second Turn (requires manual conversation_id) ---
    echo "Test Case 4: Conversation - Second Turn (requires conversation_id from previous log)"

    # PASTE THE CONVERSATION_ID YOU FOUND IN THE PROXY-GATEWAY LOGS HERE:
    MANUAL_CONVERSATION_ID="<PASTE_CONVERSATION_ID_FROM_LOGS_HERE>" 

    if [ "${MANUAL_CONVERSATION_ID}" != "<PASTE_CONVERSATION_ID_FROM_LOGS_HERE>" ]; then
        echo "Continuing conversation with ID: ${MANUAL_CONVERSATION_ID}"
        echo "Prompt: Can you elaborate on the second part of your previous answer?"
        RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
          -H "Content-Type: application/json" \
          -H "X-Conversation-Id: ${MANUAL_CONVERSATION_ID}" \
          -d '{
            "model": "gpt-3.5-turbo", # <--- CHANGED FROM "gemini-pro"
            "messages": [
              {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
            ]
          }')
        echo "Response: ${RESPONSE}"
    else
        echo "Skipped Test Case 4. Please update MANUAL_CONVERSATION_ID in the script to run this test case."
    fi
    echo "----------------------------------------------------"
    sleep 2


    # --- Test Case 5: Request with X-Preferred-LLM-ID Header (User Override) ---
    echo "Test Case 5: Request with X-Preferred-LLM-ID (force routing to a specific model)"
    # Use a known model ID from your 'populate_redis.py' or 'modelProfiles' list
    # Example: "gpt-4o-mini", "gemini-pro", "claude-3-haiku", "default-backend-id"
    PREFERRED_LLM_ID="default-backend-id" # You can change this to another ID from your setup
    echo "Prompt: Summarize the plot of Romeo and Juliet very briefly."
    echo "Attempting to route to preferred LLM: ${PREFERRED_LLM_ID}"
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -H "X-Preferred-LLM-ID: ${PREFERRED_LLM_ID}" \
      -d '{
        "model": "gpt-3.5-turbo",
        "messages": [
          {"role": "user", "content": "Summarize the plot of Romeo and Juliet very briefly."}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    # --- Test Case 6: Request with X-User-Id and X-User-Roles Headers ---
    echo "Test Case 6: Request with X-User-Id and X-User-Roles (for logging/policy checks)"
    echo "Prompt: Explain the concept of quantum computing."
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -H "X-User-Id: user-alpha-123" \
      -H "X-User-Roles: admin,developer,tester" \
      -d '{
        "model": "gpt-3.5-turbo",
        "messages": [
          {"role": "user", "content": "Explain the concept of quantum computing."}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    # --- Test Case 7: Request with another user ID and no roles ---
    echo "Test Case 7: Request with another user ID (no roles specified)"
    echo "Prompt: Generate a short poem about a rainy day."
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -H "X-User-Id: user-beta-456" \
      -d '{
        "model": "gpt-3.5-turbo", # <--- CHANGED FROM "gemini-pro"
        "messages": [
          {"role": "user", "content": "Generate a short poem about a rainy day."}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    # --- Test Case 8: Request with no user ID or roles (should default to empty in logs) ---
    echo "Test Case 8: Request with no user ID or roles (testing default empty behavior)"
    echo "Prompt: What is the capital of Japan?"
    # NOTE: This test originally requested "claude-3-opus-20240229".
    # Assuming Claude-3-opus might also have credit issues,
    # we'll change it to "gpt-3.5-turbo" to ensure it hits a working path if possible.
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "gpt-3.5-turbo", # <--- CHANGED FROM "claude-3-opus-20240229"
        "messages": [
          {"role": "user", "content": "What is the capital of Japan?"}
        ]
      }')
    echo "Response: ${RESPONSE}"
    echo "----------------------------------------------------"
    sleep 2

    echo "All test requests sent. Please check your dashboard and service logs."
    echo "Suggested commands to view logs:"
    echo "  kubectl logs <your-proxy-gateway-pod-name>"
    echo "  kubectl logs <your-data-processor-pod-name>"
    echo "  kubectl logs <your-ai-optimizer-pod-name>"
    echo ""
    echo "Then, open your frontend dashboard in a browser to see the updated metrics, inference logs, evaluations, and curated data!"
    echo "You can also check the Redis database for model profiles and conversation data using the following command:"
    echo "kubectl exec -it $REDIS_POD_NAME -- redis-cli -h redis GET \"model_profile:gemini-1.5-flash\""