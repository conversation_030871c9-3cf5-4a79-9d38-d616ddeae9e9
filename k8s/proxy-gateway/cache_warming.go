package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
)

// CacheWarmingConfig defines configuration for the cache warming system
type CacheWarmingConfig struct {
	Enabled            bool         `json:"enabled"`
	IntervalMinutes    int          `json:"interval_minutes"`
	MaxConcurrentJobs  int          `json:"max_concurrent_jobs"`
	HistoricalDays     int          `json:"historical_days"`
	PreferredModels    []string     `json:"preferred_models"`
	MaxItemsPerWarming int          `json:"max_items_per_warming"`
	LastWarmingTime    time.Time    `json:"last_warming_time"`
	WarmingInProgress  bool         `json:"warming_in_progress"`
	WarmingStats       WarmingStats `json:"warming_stats"`
}

// WarmingStats tracks statistics about cache warming operations
type WarmingStats struct {
	TotalWarmingRuns     int            `json:"total_warming_runs"`
	TotalItemsWarmed     int            `json:"total_items_warmed"`
	AverageWarmingTimeMs int            `json:"average_warming_time_ms"`
	LastRunDuration      string         `json:"last_run_duration"`
	LastRunTime          time.Time      `json:"last_run_time"`
	SuccessRate          float64        `json:"success_rate"`
	ItemsPerCategory     map[string]int `json:"items_per_category"`
}

// WarmingJob defines a single cache warming job
type WarmingJob struct {
	Query     string    `json:"query"`
	Model     string    `json:"model"`
	Priority  int       `json:"priority"`
	Category  string    `json:"category"`
	UserID    string    `json:"user_id,omitempty"`
	Frequency int       `json:"frequency"` // How many times this query has been seen
	LastSeen  time.Time `json:"last_seen"`
}

// Global cache warming configuration
var (
	cacheWarmingConfig CacheWarmingConfig
	cacheWarmingMutex  sync.RWMutex
	warmingShutdown    chan struct{}
)

// initCacheWarming initializes the cache warming system
func initCacheWarming(ctx context.Context) {
	// Set default configuration
	cacheWarmingConfig = CacheWarmingConfig{
		Enabled:            true,
		IntervalMinutes:    60, // Run every hour by default
		MaxConcurrentJobs:  5,
		HistoricalDays:     7, // Look back 7 days for popular queries
		MaxItemsPerWarming: 100,
		PreferredModels:    []string{"gpt-3.5-turbo", "text-embedding-3-small"},
		WarmingStats: WarmingStats{
			ItemsPerCategory: make(map[string]int),
		},
	}

	// Initialize shutdown channel
	warmingShutdown = make(chan struct{})

	// Load config from Redis if available
	loadCacheWarmingConfigFromRedis(ctx)

	// Start the warming scheduler if enabled
	if cacheWarmingConfig.Enabled {
		go scheduleCacheWarming(ctx)
	}

	log.Printf("Cache warming system initialized (enabled: %t, interval: %d minutes)",
		cacheWarmingConfig.Enabled, cacheWarmingConfig.IntervalMinutes)
}

// loadCacheWarmingConfigFromRedis loads cache warming configuration from Redis
func loadCacheWarmingConfigFromRedis(ctx context.Context) error {
	val, err := redisClient.Get(ctx, "cache_warming_config").Result()
	if err != nil {
		return fmt.Errorf("failed to get cache warming config from Redis: %w", err)
	}

	var config CacheWarmingConfig
	if err := json.Unmarshal([]byte(val), &config); err != nil {
		return fmt.Errorf("failed to unmarshal cache warming config: %w", err)
	}

	cacheWarmingMutex.Lock()
	cacheWarmingConfig = config
	cacheWarmingMutex.Unlock()

	log.Printf("Loaded cache warming configuration from Redis")
	return nil
}

// saveCacheWarmingConfigToRedis saves the current configuration to Redis
func saveCacheWarmingConfigToRedis(ctx context.Context) error {
	cacheWarmingMutex.RLock()
	configBytes, err := json.Marshal(cacheWarmingConfig)
	cacheWarmingMutex.RUnlock()

	if err != nil {
		return fmt.Errorf("failed to marshal cache warming config: %w", err)
	}

	if err := redisClient.Set(ctx, "cache_warming_config", configBytes, 0).Err(); err != nil {
		return fmt.Errorf("failed to save cache warming config to Redis: %w", err)
	}

	return nil
}

// scheduleCacheWarming runs the cache warming process on a schedule
func scheduleCacheWarming(ctx context.Context) {
	for {
		cacheWarmingMutex.RLock()
		interval := time.Duration(cacheWarmingConfig.IntervalMinutes) * time.Minute
		enabled := cacheWarmingConfig.Enabled
		cacheWarmingMutex.RUnlock()

		if !enabled {
			log.Println("Cache warming is disabled, scheduler paused")
			time.Sleep(5 * time.Minute) // Check again in 5 minutes
			continue
		}

		select {
		case <-time.After(interval):
			// Time to run cache warming
			if err := runCacheWarming(ctx); err != nil {
				log.Printf("Error during scheduled cache warming: %v", err)
			}
		case <-warmingShutdown:
			log.Println("Cache warming scheduler shutting down")
			return
		case <-ctx.Done():
			log.Println("Context canceled, cache warming scheduler shutting down")
			return
		}
	}
}

// runCacheWarming performs a complete cache warming cycle
func runCacheWarming(ctx context.Context) error {
	cacheWarmingMutex.Lock()
	if cacheWarmingConfig.WarmingInProgress {
		cacheWarmingMutex.Unlock()
		log.Println("Cache warming already in progress, skipping")
		return nil
	}
	cacheWarmingConfig.WarmingInProgress = true
	cacheWarmingMutex.Unlock()

	defer func() {
		cacheWarmingMutex.Lock()
		cacheWarmingConfig.WarmingInProgress = false
		cacheWarmingConfig.LastWarmingTime = time.Now()
		cacheWarmingMutex.Unlock()
	}()

	startTime := time.Now()
	log.Println("Starting cache warming process")

	// 1. Identify popular queries to warm up
	jobsToWarm, err := identifyPopularQueries(ctx)
	if err != nil {
		return fmt.Errorf("failed to identify popular queries: %w", err)
	}

	// Limit the number of jobs
	cacheWarmingMutex.RLock()
	maxItems := cacheWarmingConfig.MaxItemsPerWarming
	cacheWarmingMutex.RUnlock()

	if len(jobsToWarm) > maxItems {
		jobsToWarm = jobsToWarm[:maxItems]
	}

	// 2. Execute warming jobs
	results, err := executeWarmingJobs(ctx, jobsToWarm)
	if err != nil {
		return fmt.Errorf("failed to execute warming jobs: %w", err)
	}

	// 3. Update statistics
	duration := time.Since(startTime)
	updateWarmingStats(len(jobsToWarm), results, duration)

	log.Printf("Completed cache warming: processed %d queries in %v with %d successes",
		len(jobsToWarm), duration, results)

	return saveCacheWarmingConfigToRedis(ctx)
}

// identifyPopularQueries analyzes logs and returns queries that should be cached
func identifyPopularQueries(ctx context.Context) ([]WarmingJob, error) {
	var jobs []WarmingJob

	// Get the historical window
	cacheWarmingMutex.RLock()
	days := cacheWarmingConfig.HistoricalDays
	preferredModels := cacheWarmingConfig.PreferredModels
	cacheWarmingMutex.RUnlock()

	// Note: cutoffTime is calculated but not used in this implementation
	// In a real system, you would use this to filter queries by recency
	_ = time.Now().AddDate(0, 0, -days)

	// Query Redis for popular prompts
	// We'll use a Redis sorted set that tracks prompt frequency
	popularPrompts, err := redisClient.ZRevRangeWithScores(ctx, "popular_prompts", 0, 1000).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get popular prompts from Redis: %w", err)
	}

	// Get category distribution
	categoryMap := make(map[string][]string)
	categories, err := redisClient.SMembers(ctx, "query_categories").Result()
	if err == nil {
		for _, category := range categories {
			members, err := redisClient.SMembers(ctx, "category:"+category).Result()
			if err == nil && len(members) > 0 {
				categoryMap[category] = members
			}
		}
	}

	// Create warming jobs from popular prompts
	for _, z := range popularPrompts {
		prompt := z.Member.(string)
		frequency := int(z.Score)

		// Determine category
		category := "general"
		for cat, prompts := range categoryMap {
			for _, p := range prompts {
				if p == prompt {
					category = cat
					break
				}
			}
		}

		// Choose a model for this job
		// For simplicity, we'll use the first preferred model
		model := preferredModels[0]
		if len(preferredModels) > 1 {
			// Alternate between models based on prompt
			modelIndex := len(prompt) % len(preferredModels)
			model = preferredModels[modelIndex]
		}

		job := WarmingJob{
			Query:     prompt,
			Model:     model,
			Priority:  calculatePriority(frequency),
			Category:  category,
			Frequency: frequency,
			LastSeen:  time.Now(), // We don't have actual last seen time in this implementation
		}

		jobs = append(jobs, job)
	}

	// If no popular prompts in Redis, try to generate some synthetic ones
	if len(jobs) == 0 {
		jobs = generateSyntheticWarmingJobs()
	}

	log.Printf("Identified %d popular queries for cache warming", len(jobs))
	return jobs, nil
}

// calculatePriority determines the priority of a warming job based on frequency
func calculatePriority(frequency int) int {
	if frequency > 100 {
		return 10 // Highest priority
	} else if frequency > 50 {
		return 8
	} else if frequency > 20 {
		return 6
	} else if frequency > 10 {
		return 4
	} else {
		return 2 // Lowest priority
	}
}

// generateSyntheticWarmingJobs creates synthetic jobs when no real data is available
func generateSyntheticWarmingJobs() []WarmingJob {
	// This is a fallback when we don't have real usage data
	// In a real system, you'd want to use actual historical data

	cacheWarmingMutex.RLock()
	preferredModels := cacheWarmingConfig.PreferredModels
	cacheWarmingMutex.RUnlock()

	model := preferredModels[0]
	if len(preferredModels) == 0 {
		model = "gpt-3.5-turbo"
	}

	syntheticQueries := []struct {
		query    string
		category string
		freq     int
	}{
		{"What are the latest AI trends?", "technology", 50},
		{"How do I optimize database performance?", "technology", 30},
		{"Explain machine learning in simple terms", "education", 80},
		{"What are best practices for cybersecurity?", "security", 45},
		{"How can I improve my presentation skills?", "professional", 25},
	}

	var jobs []WarmingJob
	for _, sq := range syntheticQueries {
		job := WarmingJob{
			Query:     sq.query,
			Model:     model,
			Priority:  calculatePriority(sq.freq),
			Category:  sq.category,
			Frequency: sq.freq,
			LastSeen:  time.Now().Add(-24 * time.Hour), // Pretend we saw it yesterday
		}
		jobs = append(jobs, job)
	}

	return jobs
}

// executeWarmingJobs processes warming jobs concurrently
func executeWarmingJobs(ctx context.Context, jobs []WarmingJob) (int, error) {
	if len(jobs) == 0 {
		return 0, nil
	}

	cacheWarmingMutex.RLock()
	concurrency := cacheWarmingConfig.MaxConcurrentJobs
	cacheWarmingMutex.RUnlock()

	// Create a semaphore channel to limit concurrency
	sem := make(chan struct{}, concurrency)
	var wg sync.WaitGroup

	// Track successful jobs
	var successCount int
	var successMutex sync.Mutex

	for _, job := range jobs {
		wg.Add(1)
		go func(j WarmingJob) {
			defer wg.Done()

			// Acquire semaphore slot
			sem <- struct{}{}
			defer func() { <-sem }()

			// Execute the cache warming query
			success, err := executeWarmingJob(ctx, j)
			if err != nil {
				log.Printf("Error warming cache for query '%s': %v",
					truncateString(j.Query, 30), err)
				return
			}

			if success {
				successMutex.Lock()
				successCount++
				successMutex.Unlock()
			}
		}(job)
	}

	// Wait for all jobs to complete
	wg.Wait()

	return successCount, nil
}

// executeWarmingJob performs a single cache warming job
func executeWarmingJob(ctx context.Context, job WarmingJob) (bool, error) {
	// Check for exact cache hit first
	cacheKey := getCacheKey(job.Query, job.Model)
	exists, err := redisClient.Exists(ctx, cacheKey).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check cache existence: %w", err)
	}

	// Skip if already in cache
	if exists == 1 {
		log.Printf("Query already in cache, skipping warming: %s",
			truncateString(job.Query, 30))
		return true, nil
	}

	// Prepare the request to fetch a fresh response
	// This simulates what would happen in a real request
	reqBody := map[string]interface{}{
		"model": job.Model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": job.Query,
			},
		},
	}

	// Marshal the request for logging purposes
	_, _ = json.Marshal(reqBody)

	// Get the model profile to determine where to send the request
	profile, ok := getModelProfileByID(job.Model)
	if !ok {
		return false, fmt.Errorf("model profile not found for %s", job.Model)
	}

	// Call the actual LLM API to get a response
	// In a real implementation, you would use your existing proxy mechanism
	// Here we'll just simulate the response for simplicity

	// Simulate API call delay
	time.Sleep(200 * time.Millisecond)

	// Create a simulated response
	response := map[string]interface{}{
		"id":      fmt.Sprintf("warming-%s", uuid.New().String()),
		"object":  "chat.completion",
		"created": time.Now().Unix(),
		"model":   job.Model,
		"choices": []map[string]interface{}{
			{
				"message": map[string]string{
					"role":    "assistant",
					"content": fmt.Sprintf("This is a cache warming response for: %s", job.Query),
				},
				"finish_reason": "stop",
			},
		},
		"usage": map[string]int{
			"prompt_tokens":     estimateTokenCount(job.Query),
			"completion_tokens": 20,
			"total_tokens":      estimateTokenCount(job.Query) + 20,
		},
		"system_fingerprint": "cache-warming-fp",
	}

	responseBytes, err := json.Marshal(response)
	if err != nil {
		return false, fmt.Errorf("failed to marshal response: %w", err)
	}

	// Store in Redis cache
	err = setLLMResponseInCache(ctx, cacheKey, responseBytes)
	if err != nil {
		return false, fmt.Errorf("failed to store in cache: %w", err)
	}

	// Also store in semantic cache
	inputTokens := int64(estimateTokenCount(job.Query))
	outputTokens := int64(20)
	totalCost := calculateCost(profile, inputTokens, outputTokens)

	err = storeInSemanticCache(ctx, job.Query, job.Model, responseBytes,
		inputTokens, outputTokens, totalCost)
	if err != nil {
		log.Printf("Warning: failed to store in semantic cache: %v", err)
		// Continue anyway since we successfully stored in regular cache
	}

	log.Printf("Successfully warmed cache for query: %s",
		truncateString(job.Query, 30))

	// Track this warming in Redis stats
	pipe := redisClient.Pipeline()
	pipe.Incr(ctx, "cache_warming:success_count")
	pipe.Incr(ctx, fmt.Sprintf("cache_warming:category:%s", job.Category))
	pipe.Exec(ctx)

	return true, nil
}

// calculateCost estimates the cost of a request based on tokens
func calculateCost(profile ModelProfile, inputTokens, outputTokens int64) float64 {
	return float64(inputTokens)*profile.CostPerInputToken +
		float64(outputTokens)*profile.CostPerOutputToken
}

// updateWarmingStats updates statistics about cache warming
func updateWarmingStats(jobCount, successCount int, duration time.Duration) {
	cacheWarmingMutex.Lock()
	defer cacheWarmingMutex.Unlock()

	stats := &cacheWarmingConfig.WarmingStats
	stats.TotalWarmingRuns++
	stats.TotalItemsWarmed += successCount
	stats.LastRunTime = time.Now()
	stats.LastRunDuration = duration.String()

	if jobCount > 0 {
		stats.SuccessRate = float64(successCount) / float64(jobCount)
	}

	// Update average warming time
	if stats.TotalWarmingRuns > 1 {
		currentAvg := float64(stats.AverageWarmingTimeMs)
		newTime := duration.Milliseconds()
		stats.AverageWarmingTimeMs = int((currentAvg*float64(stats.TotalWarmingRuns-1) + float64(newTime)) /
			float64(stats.TotalWarmingRuns))
	} else {
		stats.AverageWarmingTimeMs = int(duration.Milliseconds())
	}

	// Update category stats from Redis
	categories, err := redisClient.SMembers(context.Background(), "query_categories").Result()
	if err == nil {
		for _, category := range categories {
			count, err := redisClient.Get(context.Background(),
				fmt.Sprintf("cache_warming:category:%s", category)).Int()
			if err == nil {
				stats.ItemsPerCategory[category] = count
			}
		}
	}

	log.Printf("Updated cache warming stats: %d total runs, %.1f%% success rate",
		stats.TotalWarmingRuns, stats.SuccessRate*100)
}

// GetCacheWarmingStats returns the current cache warming statistics
func GetCacheWarmingStats() WarmingStats {
	cacheWarmingMutex.RLock()
	defer cacheWarmingMutex.RUnlock()

	// Return a copy to avoid concurrent modification
	stats := WarmingStats{
		TotalWarmingRuns:     cacheWarmingConfig.WarmingStats.TotalWarmingRuns,
		TotalItemsWarmed:     cacheWarmingConfig.WarmingStats.TotalItemsWarmed,
		AverageWarmingTimeMs: cacheWarmingConfig.WarmingStats.AverageWarmingTimeMs,
		LastRunDuration:      cacheWarmingConfig.WarmingStats.LastRunDuration,
		LastRunTime:          cacheWarmingConfig.WarmingStats.LastRunTime,
		SuccessRate:          cacheWarmingConfig.WarmingStats.SuccessRate,
		ItemsPerCategory:     make(map[string]int),
	}

	// Copy map
	for k, v := range cacheWarmingConfig.WarmingStats.ItemsPerCategory {
		stats.ItemsPerCategory[k] = v
	}

	return stats
}

// TriggerCacheWarming manually triggers a cache warming cycle
func TriggerCacheWarming(ctx context.Context) error {
	return runCacheWarming(ctx)
}

// UpdateCacheWarmingConfig updates the cache warming configuration
func UpdateCacheWarmingConfig(config CacheWarmingConfig) error {
	cacheWarmingMutex.Lock()
	defer cacheWarmingMutex.Unlock()

	// Preserve statistics
	config.WarmingStats = cacheWarmingConfig.WarmingStats

	// Update config
	cacheWarmingConfig = config

	// Save to Redis
	return saveCacheWarmingConfigToRedis(context.Background())
}

// RegisterCacheWarmingHandlers registers HTTP handlers for cache warming management
func RegisterCacheWarmingHandlers(mux *http.ServeMux) {
	mux.HandleFunc("/v1/cache/warming", handleCacheWarmingRequest)
}

// handleCacheWarmingRequest handles API requests for cache warming management
func handleCacheWarmingRequest(w http.ResponseWriter, r *http.Request) {
	// Check request method
	if r.Method == http.MethodGet {
		// Return current status and stats
		cacheWarmingMutex.RLock()
		status := struct {
			Enabled           bool         `json:"enabled"`
			IntervalMinutes   int          `json:"interval_minutes"`
			LastWarmingTime   time.Time    `json:"last_warming_time"`
			WarmingInProgress bool         `json:"warming_in_progress"`
			Stats             WarmingStats `json:"stats"`
		}{
			Enabled:           cacheWarmingConfig.Enabled,
			IntervalMinutes:   cacheWarmingConfig.IntervalMinutes,
			LastWarmingTime:   cacheWarmingConfig.LastWarmingTime,
			WarmingInProgress: cacheWarmingConfig.WarmingInProgress,
			Stats:             cacheWarmingConfig.WarmingStats,
		}
		cacheWarmingMutex.RUnlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(status)
		return
	}

	if r.Method == http.MethodPost {
		// Parse request body
		var req struct {
			Action string              `json:"action"`
			Config *CacheWarmingConfig `json:"config,omitempty"`
		}

		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, fmt.Sprintf("Invalid request: %v", err), http.StatusBadRequest)
			return
		}

		switch req.Action {
		case "trigger":
			// Trigger immediate cache warming
			go TriggerCacheWarming(context.Background())
			w.WriteHeader(http.StatusAccepted)
			w.Write([]byte(`{"status":"warming_started"}`))

		case "update_config":
			// Update configuration
			if req.Config == nil {
				http.Error(w, "Missing config", http.StatusBadRequest)
				return
			}

			if err := UpdateCacheWarmingConfig(*req.Config); err != nil {
				http.Error(w, fmt.Sprintf("Failed to update config: %v", err),
					http.StatusInternalServerError)
				return
			}

			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status":"config_updated"}`))

		case "enable":
			// Enable cache warming
			cacheWarmingMutex.Lock()
			cacheWarmingConfig.Enabled = true
			cacheWarmingMutex.Unlock()

			saveCacheWarmingConfigToRedis(context.Background())
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status":"enabled"}`))

		case "disable":
			// Disable cache warming
			cacheWarmingMutex.Lock()
			cacheWarmingConfig.Enabled = false
			cacheWarmingMutex.Unlock()

			saveCacheWarmingConfigToRedis(context.Background())
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status":"disabled"}`))

		default:
			http.Error(w, "Unknown action", http.StatusBadRequest)
		}

		return
	}

	// Method not allowed
	http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
}
