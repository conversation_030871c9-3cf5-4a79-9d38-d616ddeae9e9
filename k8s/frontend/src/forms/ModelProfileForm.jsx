import React, { useState, useEffect } from 'react';
import { Info as InfoIcon } from 'lucide-react';
import { Modal } from '../components/Modal'; // Import Modal from the new location

/**
 * @typedef {Object} ModelProfileFormProps
 * @property {ModelProfile} profile - The model profile object to edit (or new profile stub).
 * @property {(profile: ModelProfile) => void} onSave - Function to call when the form is saved.
 * @property {() => void} onCancel - Function to call when the form is cancelled.
 * @property {Object.<string, string>} errors - Validation errors.
 */

/**
 * Model Profile Form component for creating and editing LLM profiles.
 * @param {ModelProfileFormProps} props
 */
const ModelProfileForm = ({ profile, onSave, onCancel, errors }) => {
  const [formData, setFormData] = useState(profile);

  useEffect(() => {
    setFormData(profile);
  }, [profile]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleArrayChange = (name, value) => {
    // Ensure the array is always created, even if value is empty
    setFormData(prev => ({ ...prev, [name]: value.split(',').map(s => s.trim()).filter(s => s) }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave({
      ...formData,
      expected_latency_ms: parseFloat(formData.expected_latency_ms) || 0,
      expected_cost: parseFloat(formData.expected_cost) || 0,
      cost_per_input_token: parseFloat(formData.cost_per_input_token) || 0,
      cost_per_output_token: parseFloat(formData.cost_per_output_token) || 0,
      cpu_cost_per_hour: parseFloat(formData.cpu_cost_per_hour) || 0,
      memory_cost_per_hour: parseFloat(formData.memory_cost_per_hour) || 0,
      // Ensure numerical fields are parsed, and other string/array fields are handled
      // Add other new fields with default values or proper parsing if needed
      version: formData.version || '',
      owner: formData.owner || '',
      status: formData.status || '',
      documentation_url: formData.documentation_url || '',
      license: formData.license || '',
      fine_tuning_details: formData.fine_tuning_details || '',
      input_context_length: parseInt(formData.input_context_length, 10) || 0,
      output_context_length: parseInt(formData.output_context_length, 10) || 0,
      training_data_info: formData.training_data_info || '',
      // last_evaluated_at: handled by backend or left as string from form if not parsed
      // evaluation_metrics: already json.RawMessage, treat as string from form if not explicitly handled
      compliance_tags: Array.isArray(formData.compliance_tags) ? formData.compliance_tags : [],
      region: formData.region || '',
      provider: formData.provider || '',
    });
  };

  return (
    <Modal isOpen={true} onClose={onCancel} title={profile.id ? "Edit LLM Profile" : "Create LLM Profile"}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
          <input type="text" id="name" name="name" value={formData.name || ''} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>
        <div>
          <label htmlFor="aliases" className="block text-sm font-medium text-gray-700">Aliases (comma-separated)</label>
          <input type="text" id="aliases" name="aliases" value={((formData.aliases || [])).join(', ')} onChange={(e) => handleArrayChange('aliases', e.target.value)} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="capabilities" className="block text-sm font-medium text-gray-700">Capabilities (comma-separated)</label>
          <input type="text" id="capabilities" name="capabilities" value={((formData.capabilities || [])).join(', ')} onChange={(e) => handleArrayChange('capabilities', e.target.value)} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="pricing_tier" className="block text-sm font-medium text-gray-700">Pricing Tier</label>
          <input type="text" id="pricing_tier" name="pricing_tier" value={formData.pricing_tier || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="data_sensitivity" className="block text-sm font-medium text-gray-700">Data Sensitivity</label>
          <input type="text" id="data_sensitivity" name="data_sensitivity" value={formData.data_sensitivity || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="expected_latency_ms" className="block text-sm font-medium text-gray-700">Expected Latency (ms)</label>
          <input type="number" step="0.01" id="expected_latency_ms" name="expected_latency_ms" value={formData.expected_latency_ms || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="expected_cost" className="block text-sm font-medium text-gray-700">Expected Cost</label>
          <input type="number" step="0.000000001" id="expected_cost" name="expected_cost" value={formData.expected_cost || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700">LLM URL</label> {/* Changed htmlFor and name to 'url' */}
          <input type="text" id="url" name="url" value={formData.url || ''} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" /> {/* Changed id and name to 'url', and value to formData.url */}
          {errors.url && <p className="text-red-500 text-xs mt-1">{errors.url}</p>} {/* Changed errors.backend_url to errors.url */}
        </div>
        <div>
          <label htmlFor="backend_type" className="block text-sm font-medium text-gray-700">LLM Type</label>
          <input type="text" id="backend_type" name="backend_type" value={formData.backend_type || ''} onChange={handleChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
          {errors.backend_type && <p className="text-red-500 text-xs mt-1">{errors.backend_type}</p>}
        </div>
        <div>
          <label htmlFor="cost_per_input_token" className="block text-sm font-medium text-gray-700">Cost Per Input Token</label>
          <input type="number" step="0.000000001" id="cost_per_input_token" name="cost_per_input_token" value={formData.cost_per_input_token || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="cost_per_output_token" className="block text-sm font-medium text-gray-700">Cost Per Output Token</label>
          <input type="number" step="0.000000001" id="cost_per_output_token" name="cost_per_output_token" value={formData.cost_per_output_token || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="cpu_cost_per_hour" className="block text-sm font-medium text-gray-700">CPU Cost Per Hour</label>
          <input type="number" step="0.000000001" id="cpu_cost_per_hour" name="cpu_cost_per_hour" value={formData.cpu_cost_per_hour || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="memory_cost_per_hour" className="block text-sm font-medium text-gray-700">Memory Cost Per Hour</label>
          <input type="number" step="0.000000001" id="memory_cost_per_hour" name="memory_cost_per_hour" value={formData.memory_cost_per_hour || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="api_key" className="block text-sm font-medium text-gray-700">API Key</label>
          <input type="password" id="api_key" name="api_key" value={formData.api_key || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" placeholder="Leave empty if not required or for mock LLMs" />
        </div>
        {/* New fields for consistency */}
        <div>
          <label htmlFor="version" className="block text-sm font-medium text-gray-700">Version</label>
          <input type="text" id="version" name="version" value={formData.version || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="owner" className="block text-sm font-medium text-gray-700">Owner</label>
          <input type="text" id="owner" name="owner" value={formData.owner || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">Status</label>
          <input type="text" id="status" name="status" value={formData.status || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="documentation_url" className="block text-sm font-medium text-gray-700">Documentation URL</label>
          <input type="text" id="documentation_url" name="documentation_url" value={formData.documentation_url || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="license" className="block text-sm font-medium text-gray-700">License</label>
          <input type="text" id="license" name="license" value={formData.license || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="fine_tuning_details" className="block text-sm font-medium text-gray-700">Fine Tuning Details</label>
          <textarea id="fine_tuning_details" name="fine_tuning_details" value={formData.fine_tuning_details || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"></textarea>
        </div>
        <div>
          <label htmlFor="input_context_length" className="block text-sm font-medium text-gray-700">Input Context Length</label>
          <input type="number" id="input_context_length" name="input_context_length" value={formData.input_context_length || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="output_context_length" className="block text-sm font-medium text-gray-700">Output Context Length</label>
          <input type="number" id="output_context_length" name="output_context_length" value={formData.output_context_length || 0} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="training_data_info" className="block text-sm font-medium text-gray-700">Training Data Info</label>
          <textarea id="training_data_info" name="training_data_info" value={formData.training_data_info || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"></textarea>
        </div>
        <div>
          <label htmlFor="compliance_tags" className="block text-sm font-medium text-gray-700">Compliance Tags (comma-separated)</label>
          <input type="text" id="compliance_tags" name="compliance_tags" value={((formData.compliance_tags || [])).join(', ')} onChange={(e) => handleArrayChange('compliance_tags', e.target.value)} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="region" className="block text-sm font-medium text-gray-700">Region</label>
          <input type="text" id="region" name="region" value={formData.region || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>
        <div>
          <label htmlFor="provider" className="block text-sm font-medium text-gray-700">Provider</label>
          <input type="text" id="provider" name="provider" value={formData.provider || ''} onChange={handleChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" />
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button type="button" onClick={onCancel} className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-all duration-200">
            Cancel
          </button>
          <button type="submit" className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-all duration-200">
            {profile.id ? "Update Profile" : "Create Profile"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default ModelProfileForm;
