import { useState, useCallback, useEffect } from 'react';
import useApi, { DASHBOARD_API_PREFIX, POLICY_MANAGER_PREFIX } from './useApi';

const useDashboardData = ({ showNotification }) => {
  const { callApi, apiLoading, apiError } = useApi();

  const [dashboardLoading, setDashboardLoading] = useState(false);
  const [dashboardError, setDashboardError] = useState(null);

  const [backendLatencies, setBackendLatencies] = useState([]);
  const [backendLatenciesLoading, setBackendLatenciesLoading] = useState(false);
  const [backendLatenciesError, setBackendLatenciesError] = useState(null);

  // Updated `optimalBackend` state to match the Go struct's JSON tags
  const [optimalBackend, setOptimalBackend] = useState(null);
  const [optimalBackendLoading, setOptimalBackendLoading] = useState(false);
  const [optimalBackendError, setOptimalBackendError] = useState(null);

  const [preferredBackend, setPreferredBackend] = useState(null); // This holds the user's optimization preference

  const [policies, setPolicies] = useState([]);
  const [policiesLoading, setPoliciesLoading] = useState(false);
  const [policiesError, setPoliciesError] = useState(null);

  const [modelProfiles, setModelProfiles] = useState([]);
  const [modelProfileMap, setModelProfileMap] = useState({}); // For easy lookup
  const [modelProfilesLoading, setModelProfilesLoading] = useState(false);
  const [modelProfilesError, setModelProfilesError] = useState(null);

  const [inferenceSummary, setInferenceSummary] = useState(null);
  const [inferenceSummaryLoading, setInferenceSummaryLoading] = useState(false);
  const [inferenceSummaryError, setInferenceSummaryError] = useState(null);

  const [timeSeriesData, setTimeSeriesData] = useState([]);
  const [timeSeriesLoading, setTimeSeriesLoading] = useState(false);
  const [timeSeriesError, setTimeSeriesError] = useState(null);

  const [recentInferenceLogs, setRecentInferenceLogs] = useState([]);
  const [recentLogsLoading, setRecentLogsLoading] = useState(false);
  const [recentLogsError, setRecentLogsError] = useState(null);

  const [evaluationResults, setEvaluationResults] = useState([]);
  const [evaluationResultsLoading, setEvaluationResultsLoading] = useState(false);
  const [evaluationResultsError, setEvaluationResultsError] = useState(null);

  const [curatedData, setCuratedData] = useState([]);
  const [curatedDataLoading, setCuratedDataLoading] = useState(false);
  const [curatedDataError, setCuratedDataError] = useState(null);

  const [refreshInterval, setRefreshInterval] = useState(30000);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');


  // --- Fetching Functions ---

  const fetchBackendLatencies = useCallback(async () => {
    setBackendLatenciesLoading(true);
    setBackendLatenciesError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/backend-latencies?timeframe=${selectedTimeframe}`); // Pass timeframe
      if (data && Array.isArray(data)) {
        setBackendLatencies(data);
        console.log("[useDashboardData] Fetched Backend Latencies:", data);
      } else {
        console.warn("[useDashboardData] Backend Latencies API returned unexpected data:", data);
        setBackendLatencies([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching backend latencies:", error);
      setBackendLatenciesError(error.message);
      showNotification(`Failed to fetch backend latencies: ${error.message}`, 'error');
      setBackendLatencies([]);
    } finally {
      setBackendLatenciesLoading(false);
    }
  }, [callApi, showNotification, selectedTimeframe]); // Add selectedTimeframe to dependencies

  const fetchOptimalBackend = useCallback(async () => {
    setOptimalBackendLoading(true);
    setOptimalBackendError(null);
    try {
      // Pass timeframe to the optimal-backend API call
      const data = await callApi(`${DASHBOARD_API_PREFIX}/optimal-backend?timeframe=${selectedTimeframe}`);
      if (data) {
        // Log the raw data to see what's actually being received
        console.log("[useDashboardData] Raw Optimal Backend Data:", JSON.stringify(data, null, 2));

        // Use the new structure: optimalBackendId, averageMetric, preference, message, timestamp
        setOptimalBackend({
          optimalBackendId: data.optimalBackendId, // This is directly from the Go struct field
          averageMetric: data.averageMetric,     // This is the corrected field
          message: data.message,
          preference: data.preference,
          timestamp: data.timestamp,
        });

        // Automatically set preferredBackend to the optimal if none is explicitly set
        // Use data.preference from the API response
        setPreferredBackend(data.preference || null);

      } else {
        console.warn("[useDashboardData] Optimal Backend API returned no data.");
        setOptimalBackend(null);
        setPreferredBackend(null); // Clear preferredBackend if no data
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching optimal backend:", error);
      setOptimalBackendError(error.message);
      showNotification(`Failed to fetch optimal backend: ${error.message}`, 'error');
      setOptimalBackend(null);
      setPreferredBackend(null);
    } finally {
      setOptimalBackendLoading(false);
    }
  }, [callApi, showNotification, selectedTimeframe]); // Add selectedTimeframe to dependencies

  const fetchPolicies = useCallback(async () => {
    setPoliciesLoading(true);
    setPoliciesError(null);
    try {
      const data = await callApi(`${POLICY_MANAGER_PREFIX}/policies`);
      if (data && Array.isArray(data)) {
        setPolicies(data);
        console.log("[useDashboardData] Fetched Policies:", data);
      } else {
        console.warn("[useDashboardData] Policies API returned unexpected data:", data);
        setPolicies([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching policies:", error);
      setPoliciesError(error.message);
      showNotification(`Failed to fetch policies: ${error.message}`, 'error');
      setPolicies([]);
    } finally {
      setPoliciesLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchModelProfiles = useCallback(async () => {
    setModelProfilesLoading(true);
    setModelProfilesError(null);
    try {
      const data = await callApi(`${POLICY_MANAGER_PREFIX}/model-profiles`);
      if (data && Array.isArray(data)) {
        setModelProfiles(data);
        const profileMap = data.reduce((map, profile) => {
          map[profile.id] = profile;
          return map;
        }, {});
        setModelProfileMap(profileMap);
        console.log("[useDashboardData] Fetched Model Profiles:", data, "Map:", profileMap);
      } else {
        console.warn("[useDashboardData] Model Profiles API returned unexpected data:", data);
        setModelProfiles([]);
        setModelProfileMap({});
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching model profiles:", error);
      setModelProfilesError(error.message);
      showNotification(`Failed to fetch model profiles: ${error.message}`, 'error');
      setModelProfiles([]);
      setModelProfileMap({});
    } finally {
      setModelProfilesLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchInferenceSummary = useCallback(async (timeframe) => {
    setInferenceSummaryLoading(true);
    setInferenceSummaryError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/summary?timeframe=${timeframe}`);
      if (data) {
        setInferenceSummary(data);
        console.log("[useDashboardData] Fetched Inference Summary:", data);
      } else {
        console.warn("[useDashboardData] Inference Summary API returned no data.");
        setInferenceSummary(null);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching inference summary:", error);
      setInferenceSummaryError(error.message);
      showNotification(`Failed to fetch inference summary: ${error.message}`, 'error');
      setInferenceSummary(null);
    } finally {
      setInferenceSummaryLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchTimeSeriesData = useCallback(async (timeframe) => {
    setTimeSeriesLoading(true);
    setTimeSeriesError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/time-series?timeframe=${timeframe}`);
      if (data && Array.isArray(data)) {
        console.log("[useDashboardData] Raw Time Series Data (detailed):", JSON.stringify(data, null, 2));
        setTimeSeriesData(data);
      } else {
        console.warn("[useDashboardData] Time Series API returned unexpected data:", data);
        setTimeSeriesData([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching time series data:", error);
      setTimeSeriesError(error.message);
      showNotification(`Failed to fetch time series data: ${error.message}`, 'error');
      setTimeSeriesData([]);
    } finally {
      setTimeSeriesLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchRecentInferenceLogs = useCallback(async (timeframe) => {
    setRecentLogsLoading(true);
    setRecentLogsError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/inference-logs?timeframe=${timeframe}`);
      if (data && Array.isArray(data)) {
        setRecentInferenceLogs(data);
        console.log("[useDashboardData] Fetched Recent Inference Logs:", data);
      } else {
        console.warn("[useDashboardData] Recent Inference Logs API returned unexpected data:", data);
        setRecentInferenceLogs([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching recent inference logs:", error);
      setRecentLogsError(error.message);
      showNotification(`Failed to fetch recent inference logs: ${error.message}`, 'error');
      setRecentInferenceLogs([]);
    } finally {
      setRecentLogsLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchEvaluationResults = useCallback(async (timeframe) => {
    setEvaluationResultsLoading(true);
    setEvaluationResultsError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/evaluation-results?timeframe=${timeframe}`);
      if (data === null) {
        setEvaluationResults([]);
        console.warn("[useDashboardData] Evaluation Results API returned null, treating as empty array.");
      } else if (data && Array.isArray(data)) {
        setEvaluationResults(data);
        console.log("[useDashboardData] Fetched Evaluation Results:", data);
      } else {
        console.warn("[useDashboardData] Evaluation Results API returned unexpected data:", data);
        setEvaluationResults([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching evaluation results:", error);
      setEvaluationResultsError(error.message);
      showNotification(`Failed to fetch evaluation results: ${error.message}`, 'error');
      setEvaluationResults([]);
    } finally {
      setEvaluationResultsLoading(false);
    }
  }, [callApi, showNotification]);

  const fetchCuratedData = useCallback(async (timeframe) => {
    setCuratedDataLoading(true);
    setCuratedDataError(null);
    try {
      const data = await callApi(`${DASHBOARD_API_PREFIX}/curated-data?timeframe=${timeframe}`);
      if (data === null) {
        setCuratedData([]);
        console.warn("[useDashboardData] Curated Data API returned null, treating as empty array.");
      } else if (data && Array.isArray(data)) {
        setCuratedData(data);
        console.log("[useDashboardData] Fetched Curated Data:", data);
      } else {
        console.warn("[useDashboardData] Curated Data API returned unexpected data:", data);
        setCuratedData([]);
      }
    } catch (error) {
      console.error("[useDashboardData] Error fetching curated data:", error);
      setCuratedDataError(error.message);
      showNotification(`Failed to fetch curated data: ${error.message}`, 'error');
      setCuratedData([]);
    } finally {
      setCuratedDataLoading(false);
    }
  }, [callApi, showNotification]);


  const fetchDashboardData = useCallback(async (timeframe) => {
    setDashboardLoading(true);
    setDashboardError(null);
    try {
      await Promise.all([
        fetchBackendLatencies(),
        fetchOptimalBackend(),
        fetchPolicies(),
        fetchModelProfiles(),
        fetchInferenceSummary(timeframe),
        fetchTimeSeriesData(timeframe),
        fetchRecentInferenceLogs(timeframe),
        fetchEvaluationResults(timeframe),
        fetchCuratedData(timeframe),
      ]);
    } catch (error) {
      console.error("[useDashboardData] Master dashboard data fetch failed:", error);
      setDashboardError(error.message);
      showNotification(`Failed to load all dashboard data: ${error.message}`, 'error');
    } finally {
      setDashboardLoading(false);
    }
  }, [
    fetchBackendLatencies, fetchOptimalBackend, fetchPolicies, fetchModelProfiles,
    fetchInferenceSummary, fetchTimeSeriesData, fetchRecentInferenceLogs,
    fetchEvaluationResults, fetchCuratedData, showNotification
  ]);


  const setOptimizationPreference = useCallback(async (preference) => {
    setPreferredBackend(preference); // Optimistic update
    try {
      await callApi(`${DASHBOARD_API_PREFIX}/set-preference`, { // Corrected endpoint here
        method: 'POST',
        body: JSON.stringify({ preference: preference })
      });
      showNotification(`Optimization preference set to: ${preference}`, 'success');
      fetchOptimalBackend(); // Re-fetch optimal backend to reflect changes
    } catch (error) {
      console.error("Error setting optimization preference:", error);
      showNotification(`Failed to set preference: ${error.message}`, 'error');
      // Revert preferredBackend on error if necessary, or just let next fetch correct it
    }
  }, [callApi, showNotification, fetchOptimalBackend]);

  useEffect(() => {
    fetchDashboardData(selectedTimeframe);

    const intervalId = setInterval(() => {
      fetchDashboardData(selectedTimeframe);
    }, refreshInterval);

    return () => clearInterval(intervalId);
  }, [fetchDashboardData, refreshInterval, selectedTimeframe]);

  return {
    backendLatencies,
    optimalBackend,
    preferredBackend,
    policies,
    modelProfiles,
    modelProfileMap,
    inferenceSummary,
    timeSeriesData,
    recentInferenceLogs,
    evaluationResults,
    curatedData,

    dashboardLoading,
    policiesLoading,
    modelProfilesLoading,
    backendLatenciesLoading,
    optimalBackendLoading,
    inferenceSummaryLoading,
    timeSeriesLoading,
    recentLogsLoading,
    evaluationResultsLoading,
    curatedDataLoading,
    apiLoading,

    dashboardError,
    policiesError,
    modelProfilesError,
    backendLatenciesError,
    optimalBackendError,
    inferenceSummaryError,
    timeSeriesError,
    recentLogsError,
    evaluationResultsError,
    curatedDataError,
    apiError,

    fetchDashboardData,
    fetchPolicies,
    fetchModelProfiles,
    fetchBackendLatencies,
    fetchOptimalBackend,
    fetchInferenceSummary,
    fetchTimeSeriesData,
    fetchRecentInferenceLogs,
    fetchEvaluationResults,
    fetchCuratedData,
    setOptimizationPreference,

    refreshInterval,
    setRefreshInterval,
    selectedTimeframe,
    setSelectedTimeframe,
  };
};

export default useDashboardData;
export { useDashboardData };
