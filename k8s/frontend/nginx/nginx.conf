# Main Nginx configuration file for frontend-dashboard with proxying to internal services.

user nginx;
worker_processes auto; # Use 'auto' for optimal worker processes

error_log /var/log/nginx/error.log debug; # Increase log level to debug
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    # Custom log format from user's original config with request time
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct=$upstream_connect_time uht=$upstream_header_time urt=$upstream_response_time';

    access_log /var/log/nginx/access.log main;

    sendfile        on;
    keepalive_timeout  120; # Increased from 65
    client_body_timeout 120s; # Added
    client_header_timeout 120s; # Added
    send_timeout 120s; # Added

    # --- Kubernetes DNS Resolver Configuration ---
    # This is crucial for resolving internal Kubernetes service names at runtime.
    resolver kube-dns.kube-system.svc.cluster.local valid=5s ipv6=off;

    # --- Define Upstream Servers ---
    upstream dashboard_api_upstream {
        server dashboard-api.default.svc.cluster.local:8081 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream proxy_gateway_upstream {
        server proxy-gateway.default.svc.cluster.local:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream policy_manager_upstream {
        server policy-manager.default.svc.cluster.local:8083 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    server {
        listen 80;
        server_name localhost;

        # Increase timeouts and buffer sizes
        client_max_body_size 10M;
        client_body_buffer_size 128k;
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
        proxy_temp_file_write_size 64k;

        # Enable keepalive connections to upstream
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # 1. LLM inference proxy
        location /v1/ {
            proxy_pass http://proxy_gateway_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_buffering off;
            proxy_cache off;
            chunked_transfer_encoding off;
        }

        # 2. Policies API - both exact match and with trailing slash
        location /api/policies {
            # No rewrite - pass the full path to policy manager
            proxy_pass http://policy_manager_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 3. Model profiles API - both exact match and with trailing slash
        location /api/model_profiles {
            # No rewrite - pass the full path to policy manager
            proxy_pass http://policy_manager_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 4. Dashboard API specific endpoints - no rewrite needed
        location ~ ^/api/(summary|time-series|recent-logs|backend-latencies|optimal-backend) {
            proxy_pass http://dashboard_api_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_intercept_errors on;
            error_log /var/log/nginx/dashboard_api_error.log debug;
            
            # Add debugging headers
            add_header X-Proxy-Response-Time $upstream_response_time;
            add_header X-Request-Time $request_time;
            add_header X-Proxy-Destination "dashboard_api_upstream$request_uri";
        }

        # 5. General API proxy - for dashboard-api endpoints
        location /api/ {
            # For dashboard-api, we'll keep the rewrite
            rewrite ^/api/(.*) /$1 break;
            proxy_pass http://dashboard_api_upstream;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 6. Static files
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # Add custom error handling
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }

        # Add health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
        }
    }
}
