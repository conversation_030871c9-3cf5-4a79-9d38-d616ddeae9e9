/**
 * Safely converts any value to a string, handling objects, arrays, and null/undefined values.
 * 
 * @param {any} value - The value to convert
 * @param {string} defaultValue - The default value to return if value is null/undefined
 * @returns {string} A string representation of the value
 */
export const formatValue = (value, defaultValue = '') => {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  
  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return value.map(item => 
        typeof item === 'object' && item !== null 
          ? JSON.stringify(item) 
          : String(item)
      ).join(', ');
    }
    return JSON.stringify(value);
  }
  
  return String(value);
};

/**
 * Formats rule objects for display
 * 
 * @param {Array|Object} rules - The rules to format
 * @returns {string} A formatted string representation of the rules
 */
export const formatRules = (rules) => {
  if (!rules) return '';
  
  if (Array.isArray(rules)) {
    return rules.map(rule => `${rule.type || 'rule'}: ${rule.value || ''}`).join('\n');
  }
  
  if (typeof rules === 'object' && rules !== null) {
    return JSON.stringify(rules);
  }
  
  return String(rules);
};