# k8s/proxy-gateway/proxy-gateway.yaml
# Kubernetes YAML definitions for the Proxy Gateway

apiVersion: v1
kind: Service
metadata:
  name: proxy-gateway # Service name for in-cluster communication
spec:
  selector:
    app: proxy-gateway
  ports:
  - name: http
    protocol: TCP
    port: 8080 # Proxy listens on 8080
    targetPort: 8080
  type: <PERSON>adBalancer # Use LoadBalancer to expose the proxy externally

---
# ConfigMap definition for the Proxy Gateway's configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: proxy-gateway-config # This is the name the Deployment references
data:
  config.yaml: | # This key should match the subPath in your volumeMounts
    # Configuration for the AI Cost & Performance Optimization Proxy Gateway

    # The address and port the proxy will listen on *inside the container*
    listen_addr: ":8080" # Listen internally on port 8080

    # List of backend AI model endpoints (using Kubernetes service names)
    backends:
      - id: "mock-backend-gpu1"
        url: "http://mock-backend-gpu1:5001" # Use the Kubernetes service name and internal port
        weight: 70
        cost: 0.0000001
        latency: 100

      - id: "mock-backend-gpu2"
        url: "http://mock-backend-gpu2:5002" # Use the Kubernetes service name and internal port
        weight: 30
        cost: 0.00000015
        latency: 150

    # Kafka Configuration for logging inference events (using Kubernetes service name)
    kafka:
      brokers: ["kafka:9092"] # Use the Kafka service name and internal port
      topic: "inference-logs"

    # Redis Configuration for fetching live latency metrics (using Kubernetes service name)
    redis:
      addr: "redis:6379" # Use the Redis service name and internal port
      password: "" # No password for Redis in this MVP (adjust if you secure Redis)
      db: 0 # Default Redis database

    # This is the crucial section for routing API requests from the frontend
    routes:
      # This rule forwards requests starting with /api/ to the dashboard-api service
      # It's crucial that the 'path' here matches what your frontend is requesting
      # and the 'target' points to the correct service and path on the backend.
      - path: "/api/" # A general rule for all /api/ paths
        target: "http://dashboard-api:8081/api/" # Points to the dashboard-api service on its port 8081, and includes the /api/ prefix

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-gateway
  labels:
    app: proxy-gateway
spec:
  replicas: 1 # Running a single replica for the proxy gateway in this MVP
  selector:
    matchLabels:
      app: proxy-gateway
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: proxy-gateway
    spec:
      initContainers:
      - name: wait-for-kafka-topic
        image: confluentinc/cp-kafka:latest # Use a Kafka image for kafka-topics tool
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Kafka topic inference-logs..."; until /usr/bin/kafka-topics --bootstrap-server kafka:9092 --list | grep -q inference-logs; do echo "Topic not ready, waiting..."; sleep 5; done; echo "Kafka topic inference-logs is ready."']
        resources:
          requests:
            memory: "100Mi"
            cpu: "50m"
          limits:
            memory: "200Mi"
            cpu: "100m"
      - name: wait-for-redis
        image: busybox:1.36 # A small image with networking tools
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Redis..."; until nc -z redis 6379; do echo "Redis not ready, waiting..."; sleep 2; done; echo "Redis is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-mock-backend-gpu1
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Mock Backend GPU1..."; until nc -z mock-backend-gpu1 5001; do echo "Mock Backend GPU1 not ready, waiting..."; sleep 2; done; echo "Mock Backend GPU1 is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-mock-backend-gpu2
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Mock Backend GPU2..."; until nc -z mock-backend-gpu2 5002; do echo "Mock Backend GPU2 not ready, waiting..."; sleep 2; done; echo "Mock Backend GPU2 is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-dashboard-api
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Dashboard API..."; until nc -z dashboard-api 8081; do echo "Dashboard API not ready, waiting..."; sleep 2; done; echo "Dashboard API is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      - name: wait-for-policy-manager
        image: busybox:1.36
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'echo "Waiting for Policy Manager..."; until nc -z policy-manager 8083; do echo "Policy Manager not ready, waiting..."; sleep 2; done; echo "Policy Manager is ready."']
        resources:
          requests:
            memory: "10Mi"
            cpu: "10m"
          limits:
            memory: "20Mi"
            cpu: "20m"
      containers:
      - name: proxy-gateway
        image: ai-cost-performance-optimizer-proxy-gateway:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        volumeMounts:
        - name: proxy-gateway-config-volume
          mountPath: "/app/config.yaml"
          subPath: config.yaml
        env:
        - name: CONFIG_FILE
          value: "/app/config.yaml"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: KAFKA_TOPIC
          value: "inference-logs"
        - name: REDIS_ADDR
          value: "redis:6379"
        - name: REDIS_PASSWORD
          value: ""
        - name: REDIS_DB
          value: "0"
        - name: DASHBOARD_API_URL
          value: "http://dashboard-api:8081"
        # --- Reference API Keys from Secrets ---
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-api-key    # Name of the Secret object
              key: OPENAI_API_KEY     # Key within the Secret
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: google-api-key    # Name of the Secret object
              key: GOOGLE_API_KEY     # Key within the Secret
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: anthropic-api-key # Name of the Secret object
              key: ANTHROPIC_API_KEY  # Key within the Secret
        # --- End Secret References --
        - name: AI_OPTIMIZER_CLASSIFIER_URL
          value: "http://ai-optimizer:8085/classify"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
      volumes:
      - name: proxy-gateway-config-volume
        configMap:
          name: proxy-gateway-config
