# Configuration for the AI Cost & Performance Optimization Proxy Gateway

# The address and port the proxy will listen on *inside the container*
listen_addr: ":8080" # Listen internally on port 8080

# List of backend AI model endpoints
backends:
  - id: "mock-backend-gpu1"
    url: "http://mock-backend-gpu1:5001"
    weight: 70
    cost: 0.0000001
    latency: 100
    capabilities: ["text-generation", "image-analysis"] # Example capabilities
    data_sensitivity_level: "low" # Example sensitivity level

  - id: "mock-backend-gpu2"
    url: "http://mock-backend-gpu2:5002"
    weight: 30
    cost: 0.********
    latency: 150
    capabilities: ["text-generation"] # Example capabilities
    data_sensitivity_level: "medium" # Example sensitivity level

# Kafka Configuration for logging inference events
kafka:
  brokers: ["kafka:9092"]
  topic: "inference-logs"

# Redis Configuration for fetching live latency metrics
redis:
  addr: "redis:6379"
  password: ""
  db: 0

# New: Routing Policy Definition
policy:
  name: "Default Routing Policy"
  max_latency_ms: 200.0 # Only consider backends with latency <= 200ms
  max_cost: 0.******** # Only consider backends with cost <= 0.********
  required_capabilities: ["text-generation"] # Backends must have this capability
  forbidden_data_sensitivity_levels: ["PHI"] # Backends must NOT handle PHI data
