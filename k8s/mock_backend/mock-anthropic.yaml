# mock-anthropic.yaml
# Kubernetes YAML definitions for Mock Anthropic Claude Backend

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-anthropic
  labels:
    app: mock-backend
    backend-id: mock-anthropic # Specific label for this backend instance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-anthropic
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-anthropic
        com.docker.compose.service: mock-anthropic # Crucial for data-processor/Prometheus
    spec:
      containers:
      - name: mock-anthropic
        image: ai-cost-performance-optimizer-mock-anthropic:latest # <--- Placeholder for substitution
        imagePullPolicy: Always
        ports:
        - containerPort: 5005
          name: http-anthropic
        env:
        - name: LISTEN_ADDR
          value: ":5005"
        - name: BACKEND_ID
          value: "mock-anthropic"
        - name: DELAY_MS
          value: "300"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        args: ["mock_anthropic"]
---
apiVersion: v1
kind: Service
metadata:
  name: mock-anthropic # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-anthropic # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5005
    targetPort: 5005
  type: ClusterIP # Use ClusterIP for in-cluster access
