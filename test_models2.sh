#!/bin/bash

# Configuration
# IMPORTANT: R<PERSON><PERSON>CE THIS WITH YOUR ACTUAL PROXY GATEWAY IP or HOSTNAME and PORT
# If you are port-forwarding, it might be localhost:8080
# Example: PROXY_GATEAY_IP="localhost"
# Example: PROXY_GATEWAY_PORT="8080"
PROXY_GATEWAY_IP="scale-llm.com" 
PROXY_GATEWAY_PORT="80"
ENDPOINT="https://${PROXY_GATEWAY_IP}/v1/chat/completions"

echo "Using Proxy Gateway Endpoint: ${ENDPOINT}"
echo "----------------------------------------------------"

# --- Test Case 1: Basic Factual Query (should be routed by optimizer) ---
echo "Test Case 1: Basic Factual Query (should be routed by optimizer)"
echo "Prompt: What is the capital of France?"
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "What is the capital of France?"}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- Test Case 2: Creative Writing Prompt (should be classified as 'creative_writing') ---
echo "Test Case 2: Creative Writing Prompt (should be classified as 'creative_writing')"
echo "Prompt: Write a very short, imaginative story about a cat who can fly."
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-haiku",
    "messages": [
      {"role": "user", "content": "Write a very short, imaginative story about a cat who can fly."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- Test Case 3: Conversation - First Turn (capture conversation_id) ---
echo "Test Case 3: Conversation - First Turn (A new conversation will be started. Note the conversation_id from proxy-gateway logs.)"
echo "Prompt: Hi, who are you and what do you do?"
FIRST_TURN_RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }')
echo "First Turn Response: ${FIRST_TURN_RESPONSE}"
echo ""
echo "################################################################################"
echo "### IMPORTANT: For Test Case 4, you NEED the 'conversation_id' from the    ###"
echo "###            proxy-gateway logs for the request above.                   ###"
echo "###            Look for 'Generated new ID: <UUID>' or 'Created new         ###"
echo "###            conversation <UUID>'.                                       ###"
echo "###            Then, update the 'MANUAL_CONVERSATION_ID' variable below.   ###"
echo "################################################################################"
echo ""
sleep 5


# --- Test Case 4: Conversation - Second Turn (requires manual conversation_id) ---
echo "Test Case 4: Conversation - Second Turn (requires conversation_id from previous log)"

# PASTE THE CONVERSATION_ID YOU FOUND IN THE PROXY-GATEWAY LOGS HERE:
MANUAL_CONVERSATION_ID="<PASTE_CONVERSATION_ID_FROM_LOGS_HERE>" 

if [ "${MANUAL_CONVERSATION_ID}" != "<PASTE_CONVERSATION_ID_FROM_LOGS_HERE>" ]; then
    echo "Continuing conversation with ID: ${MANUAL_CONVERSATION_ID}"
    echo "Prompt: Can you elaborate on the second part of your previous answer?"
    RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
      -H "Content-Type: application/json" \
      -H "X-Conversation-Id: ${MANUAL_CONVERSATION_ID}" \
      -d '{
        "model": "gpt-3.5-turbo",
        "messages": [
          {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
        ]
      }')
    echo "Response: ${RESPONSE}"
else
    echo "Skipped Test Case 4. Please update MANUAL_CONVERSATION_ID in the script to run this test case."
fi
echo "----------------------------------------------------"
sleep 2


# --- Test Case 5: Request with X-Preferred-LLM-ID Header (User Override) ---
echo "Test Case 5: Request with X-Preferred-LLM-ID (force routing to a specific model)"
# Use a known model ID from your 'populate_redis.py' or 'modelProfiles' list
# Example: "gpt-4o-mini", "gemini-pro", "claude-3-haiku", "default-backend-id"
PREFERRED_LLM_ID="default-backend-id" # You can change this to another ID from your setup
echo "Prompt: Summarize the plot of Romeo and Juliet very briefly."
echo "Attempting to route to preferred LLM: ${PREFERRED_LLM_ID}"
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: ${PREFERRED_LLM_ID}" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Summarize the plot of Romeo and Juliet very briefly."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- Test Case 6: Request with X-User-Id and X-User-Roles Headers ---
echo "Test Case 6: Request with X-User-Id and X-User-Roles (for logging/policy checks)"
echo "Prompt: Explain the concept of quantum computing."
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: user-alpha-123" \
  -H "X-User-Roles: admin,developer,tester" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain the concept of quantum computing."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- Test Case 7: Request with another user ID and no roles ---
echo "Test Case 7: Request with another user ID (no roles specified)"
echo "Prompt: Generate a short poem about a rainy day."
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: user-beta-456" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Generate a short poem about a rainy day."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- Test Case 8: Request with no user ID or roles (testing default empty behavior) ---
echo "Test Case 8: Request with no user ID or roles (testing default empty behavior)"
echo "Prompt: What is the capital of Japan?"
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "What is the capital of Japan."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- NEW Test Case 9: Explicitly route to Google Gemini 2.5 Flash ---
echo "Test Case 9: Explicitly routing to Google Gemini 2.5 Flash Preview (should work with valid Google API key)"
echo "Prompt: Explain the concept of a black hole in simple terms."
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gemini-2.5-flash-preview-05-20" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain the concept of a black hole in simple terms."}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- NEW Test Case 10: Model Not Supported ---
echo "Test Case 10: Model Not Supported (should return an error)"
echo "Prompt: What is the meaning of life?"
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "What is the meaning of life?"}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

# --- NEW Test Case 11: No Routing Strategies in Redis ---
echo "Test Case 11: No Routing Strategies in Redis (should return an error)"
echo "First, clear all routing strategies from Redis"
# redis-cli KEYS "routing_strategy:*" | xargs redis-cli DEL
echo "NOTE: Test Case 11 requires Redis to be manually cleared."
echo "Prompt: What is the airspeed velocity of an unladen swallow?"
RESPONSE=$(curl -s -X POST "${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "What is the airspeed velocity of an unladen swallow?"}
    ]
  }')
echo "Response: ${RESPONSE}"
echo "----------------------------------------------------"
sleep 2

echo "All test requests sent. Please check your dashboard and service logs."
echo "Suggested commands to view logs:"
echo "  kubectl logs <your-proxy-gateway-pod-name>"
echo "  kubectl logs <your-data-processor-pod-name>"
echo "    kubectl logs <your-ai-optimizer-pod-name>" # Fix added extra spaces
echo ""
echo "Then, open your frontend dashboard in a browser to see the updated metrics, inference logs, evaluations, and curated data!"
