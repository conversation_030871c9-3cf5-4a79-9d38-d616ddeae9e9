package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// StateManager manages execution state and context persistence
type StateManager struct {
	storage       StateStorage
	cache         map[string]*ExecutionContext
	goalCache     map[string]*Goal
	planCache     map[string]*Plan
	mu            sync.RWMutex
	autoSaveInterval time.Duration
	stopAutoSave  chan struct{}
}

// StateStorage interface for persisting state data
type StateStorage interface {
	SaveGoal(goal Goal) error
	LoadGoal(goalID string) (*Goal, error)
	SavePlan(plan Plan) error
	LoadPlan(planID string) (*Plan, error)
	SaveExecutionContext(ctx ExecutionContext) error
	LoadExecutionContext(contextID string) (*ExecutionContext, error)
	ListGoals(userID string, status GoalStatus) ([]Goal, error)
	ListPlans(goalID string) ([]Plan, error)
	DeleteGoal(goalID string) error
	DeletePlan(planID string) error
	DeleteExecutionContext(contextID string) error
}

// MemoryStateStorage is an in-memory implementation of StateStorage
type MemoryStateStorage struct {
	goals     map[string]Goal
	plans     map[string]Plan
	contexts  map[string]ExecutionContext
	mu        sync.RWMutex
}

// NewStateManager creates a new state manager
func NewStateManager(storage StateStorage) *StateManager {
	sm := &StateManager{
		storage:          storage,
		cache:            make(map[string]*ExecutionContext),
		goalCache:        make(map[string]*Goal),
		planCache:        make(map[string]*Plan),
		autoSaveInterval: 30 * time.Second,
		stopAutoSave:     make(chan struct{}),
	}

	// Start auto-save routine
	go sm.autoSaveRoutine()

	return sm
}

// NewMemoryStateStorage creates a new in-memory state storage
func NewMemoryStateStorage() *MemoryStateStorage {
	return &MemoryStateStorage{
		goals:    make(map[string]Goal),
		plans:    make(map[string]Plan),
		contexts: make(map[string]ExecutionContext),
	}
}

// CreateExecutionContext creates a new execution context
func (sm *StateManager) CreateExecutionContext(goalID, planID string) (*ExecutionContext, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	contextID := uuid.New().String()
	
	execCtx := &ExecutionContext{
		ID:           contextID,
		GoalID:       goalID,
		PlanID:       planID,
		CurrentState: ExecutionStateInitializing,
		Variables:    make(map[string]interface{}),
		Results:      make(map[string]TaskResult),
		Metadata:     make(map[string]interface{}),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Cache the context
	sm.cache[contextID] = execCtx

	// Persist to storage
	if err := sm.storage.SaveExecutionContext(*execCtx); err != nil {
		delete(sm.cache, contextID)
		return nil, fmt.Errorf("failed to save execution context: %w", err)
	}

	log.Printf("Created execution context: %s for goal: %s, plan: %s", contextID, goalID, planID)
	return execCtx, nil
}

// GetExecutionContext retrieves an execution context
func (sm *StateManager) GetExecutionContext(contextID string) (*ExecutionContext, error) {
	sm.mu.RLock()
	
	// Check cache first
	if ctx, exists := sm.cache[contextID]; exists {
		sm.mu.RUnlock()
		return ctx, nil
	}
	sm.mu.RUnlock()

	// Load from storage
	ctx, err := sm.storage.LoadExecutionContext(contextID)
	if err != nil {
		return nil, fmt.Errorf("failed to load execution context: %w", err)
	}

	// Cache the loaded context
	sm.mu.Lock()
	sm.cache[contextID] = ctx
	sm.mu.Unlock()

	return ctx, nil
}

// UpdateExecutionContext updates an execution context
func (sm *StateManager) UpdateExecutionContext(ctx ExecutionContext) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	ctx.UpdatedAt = time.Now()

	// Update cache
	sm.cache[ctx.ID] = &ctx

	// Persist to storage
	if err := sm.storage.SaveExecutionContext(ctx); err != nil {
		return fmt.Errorf("failed to update execution context: %w", err)
	}

	return nil
}

// UpdateTaskResult updates a task result in the execution context
func (sm *StateManager) UpdateTaskResult(ctx *ExecutionContext, taskID string, result TaskResult) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	ctx.Results[taskID] = result
	ctx.UpdatedAt = time.Now()

	// Update cache
	sm.cache[ctx.ID] = ctx

	// Persist to storage (async to avoid blocking)
	go func() {
		if err := sm.storage.SaveExecutionContext(*ctx); err != nil {
			log.Printf("Failed to persist task result update: %v", err)
		}
	}()

	return nil
}

// SetVariable sets a variable in the execution context
func (sm *StateManager) SetVariable(ctx *ExecutionContext, key string, value interface{}) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	ctx.Variables[key] = value
	ctx.UpdatedAt = time.Now()

	// Update cache
	sm.cache[ctx.ID] = ctx

	return nil
}

// GetVariable gets a variable from the execution context
func (sm *StateManager) GetVariable(ctx *ExecutionContext, key string) (interface{}, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	value, exists := ctx.Variables[key]
	return value, exists
}

// SaveGoal saves a goal
func (sm *StateManager) SaveGoal(goal Goal) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	goal.UpdatedAt = time.Now()

	// Update cache
	sm.goalCache[goal.ID] = &goal

	// Persist to storage
	if err := sm.storage.SaveGoal(goal); err != nil {
		delete(sm.goalCache, goal.ID)
		return fmt.Errorf("failed to save goal: %w", err)
	}

	return nil
}

// GetGoal retrieves a goal
func (sm *StateManager) GetGoal(goalID string) (*Goal, error) {
	sm.mu.RLock()
	
	// Check cache first
	if goal, exists := sm.goalCache[goalID]; exists {
		sm.mu.RUnlock()
		return goal, nil
	}
	sm.mu.RUnlock()

	// Load from storage
	goal, err := sm.storage.LoadGoal(goalID)
	if err != nil {
		return nil, fmt.Errorf("failed to load goal: %w", err)
	}

	// Cache the loaded goal
	sm.mu.Lock()
	sm.goalCache[goalID] = goal
	sm.mu.Unlock()

	return goal, nil
}

// UpdateGoal updates a goal
func (sm *StateManager) UpdateGoal(goal Goal) error {
	return sm.SaveGoal(goal)
}

// SavePlan saves a plan
func (sm *StateManager) SavePlan(plan Plan) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	plan.UpdatedAt = time.Now()

	// Update cache
	sm.planCache[plan.ID] = &plan

	// Persist to storage
	if err := sm.storage.SavePlan(plan); err != nil {
		delete(sm.planCache, plan.ID)
		return fmt.Errorf("failed to save plan: %w", err)
	}

	return nil
}

// GetPlan retrieves a plan
func (sm *StateManager) GetPlan(planID string) (*Plan, error) {
	sm.mu.RLock()
	
	// Check cache first
	if plan, exists := sm.planCache[planID]; exists {
		sm.mu.RUnlock()
		return plan, nil
	}
	sm.mu.RUnlock()

	// Load from storage
	plan, err := sm.storage.LoadPlan(planID)
	if err != nil {
		return nil, fmt.Errorf("failed to load plan: %w", err)
	}

	// Cache the loaded plan
	sm.mu.Lock()
	sm.planCache[planID] = plan
	sm.mu.Unlock()

	return plan, nil
}

// UpdatePlan updates a plan
func (sm *StateManager) UpdatePlan(plan Plan) error {
	return sm.SavePlan(plan)
}

// ListGoals lists goals for a user
func (sm *StateManager) ListGoals(userID string, status GoalStatus) ([]Goal, error) {
	return sm.storage.ListGoals(userID, status)
}

// ListPlans lists plans for a goal
func (sm *StateManager) ListPlans(goalID string) ([]Plan, error) {
	return sm.storage.ListPlans(goalID)
}

// autoSaveRoutine periodically saves cached data
func (sm *StateManager) autoSaveRoutine() {
	ticker := time.NewTicker(sm.autoSaveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sm.performAutoSave()
		case <-sm.stopAutoSave:
			return
		}
	}
}

// performAutoSave saves all cached data to storage
func (sm *StateManager) performAutoSave() {
	sm.mu.RLock()
	
	// Save execution contexts
	for _, ctx := range sm.cache {
		if err := sm.storage.SaveExecutionContext(*ctx); err != nil {
			log.Printf("Auto-save failed for execution context %s: %v", ctx.ID, err)
		}
	}

	// Save goals
	for _, goal := range sm.goalCache {
		if err := sm.storage.SaveGoal(*goal); err != nil {
			log.Printf("Auto-save failed for goal %s: %v", goal.ID, err)
		}
	}

	// Save plans
	for _, plan := range sm.planCache {
		if err := sm.storage.SavePlan(*plan); err != nil {
			log.Printf("Auto-save failed for plan %s: %v", plan.ID, err)
		}
	}

	sm.mu.RUnlock()
}

// Close stops the state manager and performs final save
func (sm *StateManager) Close() error {
	close(sm.stopAutoSave)
	sm.performAutoSave()
	return nil
}

// Memory storage implementation

// SaveGoal saves a goal to memory
func (ms *MemoryStateStorage) SaveGoal(goal Goal) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.goals[goal.ID] = goal
	return nil
}

// LoadGoal loads a goal from memory
func (ms *MemoryStateStorage) LoadGoal(goalID string) (*Goal, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	if goal, exists := ms.goals[goalID]; exists {
		return &goal, nil
	}
	return nil, fmt.Errorf("goal not found: %s", goalID)
}

// SavePlan saves a plan to memory
func (ms *MemoryStateStorage) SavePlan(plan Plan) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.plans[plan.ID] = plan
	return nil
}

// LoadPlan loads a plan from memory
func (ms *MemoryStateStorage) LoadPlan(planID string) (*Plan, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	if plan, exists := ms.plans[planID]; exists {
		return &plan, nil
	}
	return nil, fmt.Errorf("plan not found: %s", planID)
}

// SaveExecutionContext saves an execution context to memory
func (ms *MemoryStateStorage) SaveExecutionContext(ctx ExecutionContext) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.contexts[ctx.ID] = ctx
	return nil
}

// LoadExecutionContext loads an execution context from memory
func (ms *MemoryStateStorage) LoadExecutionContext(contextID string) (*ExecutionContext, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	if ctx, exists := ms.contexts[contextID]; exists {
		return &ctx, nil
	}
	return nil, fmt.Errorf("execution context not found: %s", contextID)
}

// ListGoals lists goals from memory
func (ms *MemoryStateStorage) ListGoals(userID string, status GoalStatus) ([]Goal, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	var goals []Goal
	for _, goal := range ms.goals {
		if goal.UserID == userID && (status == "" || goal.Status == status) {
			goals = append(goals, goal)
		}
	}
	return goals, nil
}

// ListPlans lists plans from memory
func (ms *MemoryStateStorage) ListPlans(goalID string) ([]Plan, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	var plans []Plan
	for _, plan := range ms.plans {
		if plan.GoalID == goalID {
			plans = append(plans, plan)
		}
	}
	return plans, nil
}

// DeleteGoal deletes a goal from memory
func (ms *MemoryStateStorage) DeleteGoal(goalID string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	delete(ms.goals, goalID)
	return nil
}

// DeletePlan deletes a plan from memory
func (ms *MemoryStateStorage) DeletePlan(planID string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	delete(ms.plans, planID)
	return nil
}

// DeleteExecutionContext deletes an execution context from memory
func (ms *MemoryStateStorage) DeleteExecutionContext(contextID string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	delete(ms.contexts, contextID)
	return nil
}
