# mock-openai.yaml
# Kubernetes YAML definitions for Mock OpenAI Backend

apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-openai
  labels:
    app: mock-backend
    backend-id: mock-openai # Specific label for this backend instance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mock-backend
      backend-id: mock-openai
  template:
    metadata:
      labels:
        app: mock-backend
        backend-id: mock-openai
        com.docker.compose.service: mock-openai # Crucial for data-processor/Prometheus
    spec:
      containers:
      - name: mock-openai
        image:  us-central1-docker.pkg.dev/silken-zenith-460615-s7/ai-optimizer-repo/ai-cost-performance-optimizer-mock-openai:latest  #<--- Placeholder for substitution
        imagePullPolicy: Always
        ports:
        - containerPort: 5003
          name: http-openai
        env:
        - name: LISTEN_ADDR
          value: ":5003"
        - name: BACKEND_ID
          value: "mock-openai"
        - name: DELAY_MS
          value: "200"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        args: ["mock_openai"]
---
apiVersion: v1
kind: Service
metadata:
  name: mock-openai # Service name for in-cluster communication
spec:
  selector:
    app: mock-backend
    backend-id: mock-openai # Select pods with this specific backend-id label
  ports:
  - protocol: TCP
    port: 5003
    targetPort: 5003
  type: ClusterIP # Use ClusterIP for in-cluster access
